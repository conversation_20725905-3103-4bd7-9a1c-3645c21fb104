# Use the official Golang base image
FROM golang:1.22-alpine as builder

# Install required system packages for building the app
RUN apk add --no-cache build-base pkgconfig git cmake

# Install dependencies
RUN apk add --no-cache libde265-dev libjpeg-turbo-dev

# Clone and build libheif
RUN GIT_TERMINAL_PROMPT=0 git clone https://github.com/strukturag/libheif.git /libheif && \
  cd /libheif && \
  git checkout tags/v1.17.6 && \
  mkdir build && \
  cd build && \
  cmake .. && \
  make -j4 && \
  make install && \
  rm -rf /libheif

# Copy the source code
COPY src /project/src

# Set the working directory
WORKDIR /project/src

# Download dependencies
RUN go mod download

# Build everything to make subsequent builds faster
RUN \
  cd /project/src/cmd/microservices/accounts && \
  go build -o accounts . && \
  rm accounts

RUN \
  cd /project/src/cmd/microservices/banking && \
  go build -o banking . && \
  rm banking

RUN \
  cd /project/src/cmd/microservices/business && \
  go build -o business . && \
  rm business

RUN \
  cd /project/src/cmd/microservices/kyc && \
  go build -o kyc . && \
  rm kyc

RUN \
  cd /project/src/cmd/microservices/treezor && \
  go build -o treezor . && \
  rm treezor
