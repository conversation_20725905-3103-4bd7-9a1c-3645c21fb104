# Test with:
# > docker build -f .gitpod.Dockerfile -t gitpod-dockerfile-test .

FROM gitpod/workspace-go:latest

# Useful tools
RUN \
    sudo apt-get update && \
    sudo apt-get install -y apt-utils iputils-ping dnsutils mc rsync jq cmake

# Install `kubectl` and `helm`
RUN \
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" && \
    sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl && \
    rm kubectl && \
    curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Install Microsoft AZ CLI tool
RUN \
    curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Install kubelogin, the azure auth is getting obsolete
RUN \
    curl -LO 'https://github.com/Azure/kubelogin/releases/download/v0.0.13/kubelogin-linux-amd64.zip' && \
    unzip kubelogin-linux-amd64.zip && \
    rm -f kubelogin-linux-amd64.zip && \
    sudo mv bin/linux_amd64/kubelogin /usr/local/bin/

# Install dependencies for HEIF image support
RUN \
    sudo apt-get install -y libde265-dev libjpeg-dev && \
    git clone https://github.com/strukturag/libheif.git /home/<USER>/libheif && \
    cd /home/<USER>/libheif && \
    git checkout tags/v1.17.6 && \
    mkdir build && \
    cd build && \
    cmake .. && \
    make -j4 && \
    sudo make install && \
    sudo rm -rf /home/<USER>/libheif

# Install a static version of Envoy
RUN \
    sudo bash -c "curl -L 'https://github.com/envoyproxy/envoy/releases/download/v1.25.3/envoy-contrib-1.25.3-linux-x86_64' > /usr/local/bin/envoy" && \
    sudo chmod +x /usr/local/bin/envoy

# Install the latest official Tailscale
USER root
RUN curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/focal.gpg | apt-key add - \
    && curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/focal.list | tee /etc/apt/sources.list.d/tailscale.list \
    && apt-get update \
    && apt-get install -y tailscale \
    && update-alternatives --set ip6tables /usr/sbin/ip6tables-nft
USER gitpod

# Install the latest version of Golang
RUN json_data=$(curl -s https://go.dev/dl/?mode=json) && \
    latest_version=$(echo "$json_data" | jq -r '.[0].version') && \
    download_url=https://go.dev/dl/${latest_version}.linux-amd64.tar.gz && \
    curl -LO "${download_url}" && \
    rm -fr /home/<USER>/go && \
    tar -C /home/<USER>"${latest_version}.linux-amd64.tar.gz" && \
    rm "${latest_version}.linux-amd64.tar.gz"
