FROM yochbee.azurecr.io/builder/staging:latest AS builder

COPY src /project/src

RUN \
  cd /project/src/cmd/microservices/accounts && \
  go build -o accounts . && \
  mv accounts /accounts

FROM alpine:latest AS final

# Install libde265, libjpeg-turbo
RUN apk add --no-cache libde265 libjpeg-turbo

# Copy the compiled libheif from the builder stage
COPY --from=builder /usr/local/lib/libheif.so.1 /usr/local/lib/libheif.so.1
COPY --from=builder /usr/local/lib/pkgconfig/libheif.pc /usr/local/lib/pkgconfig/libheif.pc
COPY --from=builder /usr/local/include/libheif /usr/local/include/libheif

WORKDIR /
COPY --from=builder /accounts .
ENTRYPOINT ["/accounts"]
