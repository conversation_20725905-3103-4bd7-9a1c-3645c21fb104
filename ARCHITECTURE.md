# Yochbee architecture documentation

This document describes the Architecture of <PERSON><PERSON><PERSON> and how microservices are layed-out together.

## Goals

Yochbee is using a microservice style architecture to attain certain system characteristics, including but not limited to the following:

- Resilience
- Scalability
- Plugability and flexibility
  - An existing service can be replaced, even with a different programming language and it will continue operation without any problem or down-time as if nothing was changed (provided the same functionality is maintained)
  - It is absolutely normal to have different programming languages being used in a microservice, this allows to leverage different developer experiences and different ecosystems
- Testability
  - Thru abstraction
- Future-proofing
  - Each part of the system is designed to be replacable if there is strong enough reason to do so
  - Inversion of control / Dependency injection
    - This allows the replacement of common dependencies because microservices will only use abstractions instead of the actual implementation, in other words, microservices will not depend on specific implementations
    - When there is a change in the base dependency, only one part of the code (which is the implementation) is updated, and all microservices will be able to take advantage of the change
- Productivity

## Terms

- *DI*: Dependency injection

## Architecture

### Kubernetes

At this point in time, Kubernetes has been widely accepted as the standard for microservice orchestration and management. As such, this documentation will not be a guide, nor a tutorial, nor an authoritative source of information for Kubernetes, please consult the official documentation for comprehensive source of documentation about this subject: https://kubernetes.io/docs/home/

Some of the desired features that are provided out-of-the-box by Kubernetes are:

- Scalability
- Maintanability
- Observability

This is how things work in a nutshell:

- The Kubernetes cluster is provided and hosted on Microsoft Azure
  - A cluster is composed of one or more virtual machines, also known as Nodes in Kubernetes terms
  - Each node is inter-connected in the same Virtual LAN (VLAN) and consequently, can communicate and pass data between each other
- Microservices are deployed to Kubernetes using manifest files, and are known as *Deployments*
  - They materialize as containers, known as *Pods* in Kubernetes terms; this is roughly the equivalent of a Docker Container
  - A microservice could be replicated to multiple Pods (or instance if you will)
  - The actual service (for example, a REST API on port 8000) exposed as a *Service* object in Kubernetes
    - Kubernetes automatically manages connections to these services, such as when Pods or Deployment are being restarted; Kubernetes automatically connects incoming requests to newly active Pods
    - Service consummers, for example, connected user consuming an API, will not see any service disruption, even during restart of a Deployment/Pods
- Different configurations are also stored as *ConfigMap* and *Secret* on Kubernetes

### Configuration — `etcd`

https://etcd.io/

*etcd* is a well known key-value store server software, it is widely adopted and is also used by Kubernetes itself; however, the version used by Kubernetes cannot and should not be used by our microservices.

It is excellent for storing configurations, and it allows microservices to be configured on the fly, without even restarting thanks to real-time configuration update notification. The Golang implementation of microservices already takes advantage of this feature out-of-the-box.

etcd is a microservice in the system, and in practice, this is a simple installation that rarely needs maintenance, it's also well known for its reliability and is rock solid stability, much like Kubernetes (in fact, both are part of the CLOUD NATIVE foundation).

This should be lazily injected into the DI system (this is already the case for the Golang implementation).

### The bus — RabbitMQ

Central to the whole system is the message bus, itself a microservice. It allows any service written in any programming language (provided it has the necessary library and abstraction to talk to the bus) to implement and consume services.

It is a component in the whole system that **must not be stopped or restarted**. With that said, the whole system is **considered defunct without the bus**.

Here are important goals for the bus:

- To decouple all microservices from each other
- To provide message replays in case of a microservice crash
  - In this scenario, the microservice might have been given a message to be processed, but it crashed during the processing; as soon as the microservice is restarted, the bus will resend the same message again to that microservice
- To provide event-based system behavior
  - An event is emitted to all subscribers, and they will all receive the same message and do their own processing
  - When a new microservice is create that is interested in that particular event, no change will be required from the microservice that generates the event
  - The new microservice can just subscribe to the event, all existing microservices will remain intact
- To provide the basic components for RPC in order to enable creating and consuming methods over the bus

This should be lazily injected into the DI system (this is already the case for the Golang implementation). It can then be replaced with a different implementation if required.

RabbitMQ was the bus that was chosen, and it has libraries for all major programming languages.

## Microservice implementation

Provided here are some guidelines on how to properly write a new microservice.

### Rules

In order to attain the above goals, certain rules needs to be followed. Like all rules, there could be exceptions, but when the situation seems to be an exception, first, ask yourself if you're designing things correctly; usually, when things are properly designed, they don't need to break the rules. Typically, the common reason to skip the rules is performance optimization, but that should only come after a conclusion that things are actually slow.

- Communication between microservices
  - All communications must be done thru the bus
  - Microservices should reason only about methods (over the bus), messages (typically already covered by methods) and events, not other microservices
    - Thus, there should not be any direct `host:port` communication between microservices
- Message replays
  - Implementations of bus methods or the processing of events should be idempotent, meaning that it could receive the same message multiple times, but it will leave the system in the same state as after the initial processing
- Common dependencies
  - If there is something that multiple microservices will require, move it in a *base*, lazily inject it in the DI system
  - Ideally, make an abstraction of the dependecy and only the abstraction in microservices

### Implementation

This part will cover implementation in Go (aka. Golang) but the same principle should be applied to other implementations with different programming languages.

First of all, you should have a *base* module/library/DLL which contains all the common depedencies. As a common part, you only need to do this once for your language of choice.

After that, it's all about the logic of your business.

#### Microservice Startup

All microservices should call the method `_base.SetupApp()`. What does this method do?

- Initializes the process requirements, such as initializing the global random number generator, parsing environment for special processing that is common to all microservices
- Registers the common dependencies needed during the lifetime of the microservice, such as database access, configuration provider, etc...

For the Go implementation, it also accepts a user method that will be executed before the start of the application. Normally, it is there that you will register your services, whether it will be exposed on the bus or as REST APIs.

#### Dependency injection

For Go, the chosen DI library is Uber's FX. For the documentation, see: https://pkg.go.dev/go.uber.org/fx#section-readme

It is important to make sure that the DI system will **lazily inject the dependencies as they are needed**. Failing to do this will increase the memory usage (and probably CPU usage also) of the microservice instance process, reducing the overall efficiency of the whole system.

Do not forget that the DI system is usually common to all microservices and provides all possible needs for all microservices when needed, and this is what gives microservice architecture the productivity that it's known for because you no longer need to write a long boilerplate codes when creating a new microservice.

When choosing a DI library for a different programming language, be sure to check lazy-loading and to enable it, not all DI libraries do this by default.

When creating the interface definition, place it in a folder structure such as `_base/bus/interface.go`. The implementations themselves will go inside another child folder, such as `rabbitmq`.

##### Adding a new dependency in the DI system

For this Go project, you can look at the [dependencies.go](src/_base/dependencies.go) file as the central place to register all lazy-loaded dependencies.

```golang
func SetupApp(userNonBlockingFunctions ...interface{}) *fx.App {
  // ...
	app := fx.New(
		fx.Provide(
      // Put your CreateDependeciesXXXXX functions here,
      // Put your CreateDependeciesYYYYY functions here,
      // ...
		),
		fx.Invoke(userNonBlockingFunctions...),
	)
	return app
}
```

##### Resolution and use of dependencies

How the DI system works? FX internally does reflections (like most DI libraries) in order to identify required arguments and provided value types.

```golang
// Provider function, placed in the above fx.Provide(...)
func CreateBusClient(config config.Provider) (bus2.Client, bus2.RpcClient, error) {
  // ...
}
// Provider function, placed in the above fx.Provide(...)
func CreateEchoWebServer(lc fx.Lifecycle) *echo.Echo {
  // ...
}

func RegisterRESTAPIs(e *echo.Echo, rpc bus2.RpcClient) {
  // returns nothing
}

```

In the above example, the `RegisterRESTAPIs()` method is not a special method, no magic here. But when given to FX to be called, FX will know that the function requires 2 arguments of types `*echo.Echo` and `bus2.RpcClient`. As you can then tell, there is no user-provided arguments here, that is where the inversion of control comes in.

For the first argument, FX sees that the registered provider `CreateEchoWebServer()` can provide the needed type, it will call the method in order to satisfy the dependency, that is: create the instance and keep that instance around for reuse. The same process is done for the second argument.

To be efficient, try to reduce the number of calls made by FX, reflection is always an expensive operation, regardless of the programming language.

#### Registering and Calling a method on the bus

All bus implementations will need to provide an abstraction for RPC calls, a request-response scheme (just like HTTP POST method). The current Go implementation already provides this (see the [bus/interface.go](src/_base/bus/interface.go)).


```golang
// File src/_base/methods/xxxxx.go
package methods
const (
	Domain_StaticallyCheckedMethodName = "domain.NameOfMethodHere"
)

// File src/cmd/microservices/xxxxx/services.go
// Given as argument to _base.SetupApp()
func RegisterBusMethods() {
  rpc.Register(methods.Domain_StaticallyCheckedMethodName, func(payload []byte) ([]byte, error) {
    // ...
  }, true)
}
```

Use the `rpc.Register()` abstract method to register a method with a given name. Here are some best practice:

- Put the method name as a const value in `src/_base/methods/` folder; this way, you can check where that method is being used, just like normal functions in Go. This applies to all programming languages, not just Go.
- All methods on the bus will be used by at least one other microservice.
- If the method will not be used by another microservice, there is no need to register it on the bus, treat it as ordinary methods.

Depending on the language being used, it could be abstracted further to deal with types, but for now, it needs to be clear that it's a wire communication, thus it has to be bytes exchange between the microservice and the bus.

To call the method, the bus abstraction will also need to provide the proper methods to call the method with the payload. Implementers need to handle timeout in order to avoid blackhole scenario, where there is nobody expecting to answer and the call will just wait forever causing memory leaks over time.

```golang
func XXXXX() {
  rresp, err := rpc.Call(methods.Domain_StaticallyCheckedMethodName, nil, 10*time.Second)
  if err != nil {
    return err // HTTP 500
  }
}
```

Use the `rcp.Call()` abstract method to call any method that was registered on the bus. As you can see above, the const value is referenced from the `methods` package, and the IDE can help you when you want to find all reference to this value.

#### Registering a REST API

There are hundreds of web server implementations, and you can even implement your own if you want. As a result, it is nearly impossible to come-up with an interface definition that will abstract away webserver behaviors.

Thus, web server dependency will very likely be a specific implementation, not an abstraction; in this specific case, it is an exception to the abstraction rules.

https://echo.labstack.com/

For this Go project, the chosen implementation is **Echo from LabStack**. It is a popular, high performance, flexible and easy to use web server with lots of useful features out-of-the-box (CORS, Websocket, HTTP/2, TLS, Let's Encrypt, etc...). For a different programming language, it will be up to the implementor to see if it possible to abstract-away the web server. It is thus important to choose a good web server that meets your specific requirements, if any.

Consequently, the registration of a route is going to be different as each callback will have different parameter types across libraries/frameworks.


```golang
func RegisterRESTAPIs(e *echo.Echo, rpc bus2.RpcClient) {
	e.GET("/healthz", func(c echo.Context) error {
		return c.String(http.StatusOK, "Ok")
	})

	e.GET("/api/v1alpha/list-accounts", func(c echo.Context) error {
    // ...
	})
}
```

It is recommended to have a special `/healthz` route that will return the actual status of the microservice. It can be leveraged by Kubernetes to gauge the status of the microservice.
