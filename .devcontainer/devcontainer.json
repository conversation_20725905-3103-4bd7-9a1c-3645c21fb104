{"name": "<PERSON><PERSON><PERSON> Backend Dev Container", "dockerFile": "Dockerfile", "features": {"ghcr.io/devcontainers/features/common-utils:2": {"installZsh": "false", "installOhMyZsh": "false", "upgradePackages": "true"}, "ghcr.io/devcontainers/features/kubectl-helm-minikube:1": {"installMinikube": "false"}, "ghcr.io/devcontainers/features/azure-cli:1": {}, "ghcr.io/devcontainers-extra/features/tailscale:1": {}, "ghcr.io/devcontainers/features/docker-outside-of-docker:1": {}, "ghcr.io/devcontainers-extra/features/envoy:1": {}, "ghcr.io/devcontainers/features/node:1": {"nodeGypDependencies": true, "installYarnUsingApt": true, "version": "lts", "nvmVersion": "latest"}, "ghcr.io/olliglorioso/mongodb-org-tools/mongodb-org-tools:0": {}}, "forwardPorts": [5341, 8000, 8082, 25672, 15672], "customizations": {"vscode": {"extensions": ["golang.go", "mhutchie.git-graph", "pkief.material-icon-theme"]}}, "postCreateCommand": "bash .devcontainer/scripts/post-create.sh", "postStartCommand": "bash .devcontainer/scripts/post-start.sh", "postAttachCommand": "bash .devcontainer/scripts/post-attach.sh", "mounts": ["source=yochbee-tailscale-state,target=/var/lib/tailscale,type=volume", "source=yochbee-azure-state,target=/home/<USER>/.azure,type=volume", "source=yochbee-kube-state,target=/home/<USER>/.kube,type=volume", "source=yochbee-docker-state,target=/home/<USER>/.docker,type=volume", "source=${localEnv:HOME}/.gitconfig,target=/home/<USER>/.gitconfig,type=bind", "source=${localEnv:HOME}/.claude,target=/home/<USER>/.claude,type=bind"], "runArgs": ["--cap-add=NET_ADMIN", "--cap-add=SYS_ADMIN", "--device=/dev/net/tun:/dev/net/tun"], "remoteUser": "vscode"}