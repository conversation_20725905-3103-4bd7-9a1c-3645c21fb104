# Using a specific version of the Go dev container image for reproducibility
ARG VARIANT=1.24-bookworm
FROM golang:${VARIANT}

# Set DEBIAN_FRONTEND to noninteractive to avoid prompts during apt-get install
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies from .gitpod.Dockerfile
# libde265-dev and libjpeg-dev are for libheif
# The rest are general utilities. Some might be covered by common-utils feature,
# but installing them here ensures they are present.
RUN apt-get update && apt-get install -y --no-install-recommends \
    apt-utils \
    iputils-ping \
    dnsutils \
    mc \
    rsync \
    jq \
    cmake \
    libde265-dev \
    libjpeg-dev \
    unzip \
    # Clean up apt lists to reduce image size
    && rm -rf /var/lib/apt/lists/*

# Install libheif v1.17.6 (it needs to be exactly this version)
# Using /usr/local as prefix to make it available system-wide
RUN \
    git clone https://github.com/strukturag/libheif.git /tmp/libheif && \
    cd /tmp/libheif && \
    git checkout tags/v1.17.6 && \
    mkdir build && \
    cd build && \
    cmake .. -DCMAKE_INSTALL_PREFIX=/usr/local && \
    make -j$(nproc) && \
    make install && \
    rm -rf /tmp/libheif

# Install kubelogin
ARG KUBELOGIN_VERSION=v0.2.8
RUN \
    curl -sSL "https://github.com/Azure/kubelogin/releases/download/${KUBELOGIN_VERSION}/kubelogin-linux-amd64.zip" -o /tmp/kubelogin.zip && \
    unzip /tmp/kubelogin.zip -d /tmp/kubelogin-extracted && \
    install -o root -g root -m 0755 /tmp/kubelogin-extracted/bin/linux_amd64/kubelogin /usr/local/bin/kubelogin && \
    rm -rf /tmp/kubelogin.zip /tmp/kubelogin-extracted

# Reset DEBIAN_FRONTEND
ENV DEBIAN_FRONTEND=dialog
