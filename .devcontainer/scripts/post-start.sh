#!/bin/bash

# Color definitions
RESET='\033[0m'
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'

# Bold
BRED='\033[1;31m'
BGREEN='\033[1;32m'
BYELLOW='\033[1;33m'
BBLUE='\033[1;34m'
BMAGENTA='\033[1;35m'
BCYAN='\033[1;36m'

# Helper functions for logging
info() {
    printf "${BCYAN}INFO:${RESET} $1\n"
}

success() {
    printf "${BGREEN}SUCCESS:${RESET} $1\n"
}

warning() {
    printf "${BYELLOW}WARNING:${RESET} $1\n"
}

error_msg() { 
    printf "${BRED}ERROR:${RESET} $1\n"
}

header() {
    printf "\n${BMAGENTA}================================================================================${RESET}\n"
    printf "${BMAGENTA}$1${RESET}\n"
    printf "${BMAGENTA}================================================================================${RESET}\n"
}

subheader() {
    printf "\n${BBLUE}--------------------------------------------------------------------------------${RESET}\n"
    printf "${BBLUE}$1${RESET}\n"
    printf "${BBLUE}--------------------------------------------------------------------------------${RESET}\n"
}

# --- End of Helper Functions ---

header "Post Start Commands"

subheader "RabbitMQ Docker Compose Setup"
info "Ensuring docker-compose is available and starting RabbitMQ..."
if ! command -v docker-compose &> /dev/null && command -v docker &> /dev/null; then
    info "docker-compose not found, creating symlink from docker."
    if sudo ln -s $(which docker) /usr/local/bin/docker-compose; then
        success "docker-compose symlink created."
    else
        error_msg "Failed to create docker-compose symlink."
    fi
fi

if (cd deployments/dev && docker compose --file gitpod.docker-compose.yaml --project-name yochbee-dev up -d); then
    success "RabbitMQ Docker Compose services started."
else
    error_msg "Failed to start RabbitMQ Docker Compose services."
fi

subheader "Tailscale Startup"
info "Starting Tailscale. You might be prompted to log in."
if sudo tailscale up --hostname "devcontainer-${USER_NAME}-$(basename ${containerWorkspaceFolder})"; then
    success "Tailscale started successfully."
else
    warning "Tailscale up failed or already running."
fi

success "All post-start commands executed." 