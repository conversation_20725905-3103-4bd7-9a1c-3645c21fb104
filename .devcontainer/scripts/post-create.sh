#!/bin/bash

# Color definitions
RESET='\033[0m'
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'

# Bold
BRED='\033[1;31m'
BGREEN='\033[1;32m'
BYELLOW='\033[1;33m'
BBLUE='\033[1;34m'
BMAGENTA='\033[1;35m'
BCYAN='\033[1;36m'

# Helper functions for logging
info() {
    printf "${BCYAN}INFO:${RESET} $1\n"
}

success() {
    printf "${BGREEN}SUCCESS:${RESET} $1\n"
}

warning() {
    printf "${BYELLOW}WARNING:${RESET} $1\n"
}

error_msg() { 
    printf "${BRED}ERROR:${RESET} $1\n"
}

header() {
    printf "\n${BMAGENTA}================================================================================${RESET}\n"
    printf "${BMAGENTA}$1${RESET}\n"
    printf "${BMAGENTA}================================================================================${RESET}\n"
}

subheader() {
    printf "\n${BBLUE}--------------------------------------------------------------------------------${RESET}\n"
    printf "${BBLUE}$1${RESET}\n"
    printf "${BBLUE}--------------------------------------------------------------------------------${RESET}\n"
}

# --- End of Helper Functions ---

header "Post Create Commands"

subheader "Go Setup & Stringer Installation"
info "Navigating to src directory, downloading Go modules, and installing stringer..."
if pushd src && go mod download && go install golang.org/x/tools/cmd/stringer@latest && popd; then
    success "Go setup and stringer installation complete."
else
    error_msg "Go setup or stringer installation failed."
fi

subheader "Git Credential Cache Configuration"
info "Setting Git credential cache timeout to 36000 seconds..."
if git config --global credential.helper 'cache --timeout=36000'; then
    success "Git credential cache configured."
else
    error_msg "Failed to configure Git credential cache."
fi

subheader "Azure Git Remote Setup"
info "Attempting to add Azure Git remote..."
if git remote add azure 'https://yochbeeorganisation.visualstudio.com/Yochbee/_git/BackendGolang'; then
    success "Azure Git remote added."
else
    warning "Remote azure already exists or failed to add."
fi

subheader "Compiler Warm-up"
info "Warming up the Go compiler for KYC microservice..."
if pushd src/cmd/microservices/kyc && go build -o /dev/null . && popd; then
    success "Compiler warm-up complete."
else
    error_msg "Compiler warm-up failed."
fi

subheader "Virtual Hosts Setup"
info "Adding virtual hosts to /etc/hosts..."

# Add the entries to match .gitpod.yml configuration
if sudo bash -c "echo '********* mongodb-srv.databases.svc.cluster.local' >> /etc/hosts" && \
   sudo bash -c "echo '********* bus-service' >> /etc/hosts" && \
   sudo bash -c "echo '********* seq-logging-service' >> /etc/hosts" && \
   sudo bash -c "echo '********* redis-locks' >> /etc/hosts" && \
   sudo bash -c "echo '********* yochbee-backend' >> /etc/hosts" && \
   sudo bash -c "echo '********* accounts-service' >> /etc/hosts" && \
   sudo bash -c "echo '********* kyc-service' >> /etc/hosts" && \
   sudo bash -c "echo '********* banking-services' >> /etc/hosts"; then
    success "Virtual hosts added to /etc/hosts."
else
    error_msg "Failed to add virtual hosts to /etc/hosts."
fi

success "All post-create commands executed." 