#!/bin/bash

# Color definitions
RESET='\033[0m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'

# Bold
BRED='\033[1;31m'
BGREEN='\033[1;32m'
BYELLOW='\033[1;33m'
BBLUE='\033[1;34m'
BMAGENTA='\033[1;35m'
BCYAN='\033[1;36m'

# --- Header Function ---
header() {
    printf "\n${BMAGENTA}================================================================================${RESET}\n"
    printf "${BMAGENTA}$1${RESET}\n"
    printf "${BMAGENTA}================================================================================${RESET}\n"
}

# --- End of Helper Functions ---

header "Post Attach Commands"

# Use printf instead of direct command execution to prevent terminal title issues
printf "${BGREEN}SUCCESS:${RESET} Dev Container attached. Welcome!\n"

subheader() {
    printf "\n${BBLUE}--------------------------------------------------------------------------------${RESET}\n"
    printf "${BBLUE}$1${RESET}\n"
    printf "${BBLUE}--------------------------------------------------------------------------------${RESET}\n"
}

subheader "Azure Setup Instructions"
printf "${BCYAN}INFO:${RESET} To set up Azure credentials for Yochbee, please run the following commands in order:\n"
printf "${YELLOW}  az login --use-device-code${RESET}\n"
printf "${YELLOW}  az account set --subscription 7e9d31aa-c867-4320-8027-5d83be33d4aa${RESET}\n"
printf "${YELLOW}  az aks get-credentials --resource-group Staging --name YochbeeStagingKubernetes${RESET}\n"
printf "${YELLOW}  az aks get-credentials --resource-group Production --name YochbeeProdK8s${RESET}\n"
printf "${YELLOW}  kubectl config use-context YochbeeStagingKubernetes${RESET}\n"
printf "${YELLOW}  kubelogin convert-kubeconfig -l azurecli${RESET}\n"
printf "${YELLOW}  az acr login --name yochbee${RESET}\n"

subheader "Persisted Volume Permissions Fix"
printf "${BCYAN}INFO:${RESET} Setting correct ownership for persisted volumes...\n"

# Function to fix the ownership of a directory
fix_directory_ownership() {
    local dir="$1"
    if [ -d "$dir" ]; then
        printf "${BCYAN}INFO:${RESET} Fixing ownership for $dir\n"
        if sudo chown -R vscode:vscode "$dir"; then
            printf "${BGREEN}SUCCESS:${RESET} Fixed ownership of $dir\n"
        else
            printf "${BRED}ERROR:${RESET} Failed to fix ownership of $dir\n"
        fi
    else
        printf "${BCYAN}INFO:${RESET} Directory $dir does not exist yet\n"
    fi
}

# Fix ownership for all volume-mounted directories
fix_directory_ownership "/home/<USER>/.azure"
fix_directory_ownership "/home/<USER>/.kube"
fix_directory_ownership "/home/<USER>/.docker"
# Don't fix permissions on tailscale state directory as it's managed by the daemon

subheader "Virtual Hosts Setup"
printf "${BCYAN}INFO:${RESET} Ensuring host entries are in /etc/hosts file...\n"

# Check and add host entries if they don't exist
add_host_entry() {
    local host="$1"
    if ! grep -q "$host" /etc/hosts; then
        if sudo bash -c "echo '********* $host' >> /etc/hosts"; then
            printf "${BGREEN}SUCCESS:${RESET} Added $host to /etc/hosts.\n"
        else
            printf "${BRED}ERROR:${RESET} Failed to add $host to /etc/hosts.\n"
        fi
    else
        printf "${BCYAN}INFO:${RESET} $host already in /etc/hosts.\n"
    fi
}

add_host_entry "mongodb-srv.databases.svc.cluster.local"
add_host_entry "bus-service"
add_host_entry "seq-logging-service"
add_host_entry "redis-locks"
add_host_entry "yochbee-backend"
add_host_entry "accounts-service"
add_host_entry "kyc-service"
add_host_entry "banking-services"

subheader "Services & Proxies"
printf "${BCYAN}INFO:${RESET} Starting RabbitMQ and other services with Docker Compose...\n"
if (cd deployments/dev && docker compose --file gitpod.docker-compose.yaml --project-name yochbee-dev up -d); then
    printf "${BGREEN}SUCCESS:${RESET} RabbitMQ Docker Compose services started.\n"
else
    printf "${BRED}ERROR:${RESET} Failed to start RabbitMQ Docker Compose services. Try running manually:\n"
    printf "${YELLOW}  cd deployments/dev && docker compose --file gitpod.docker-compose.yaml --project-name yochbee-dev up -d${RESET}\n"
fi

printf "${BCYAN}INFO:${RESET} Starting Envoy Mobile Gateway (This will run in background)...\n"
sudo nohup envoy --base-id 1 -c deployments/dev/envoy-gateways/gitpod.pixel.yaml > /tmp/envoy.log 2>&1 &
if [ $? -eq 0 ]; then
    printf "${BGREEN}SUCCESS:${RESET} Envoy Mobile Gateway started in background. Log at /tmp/envoy.log\n"
else
    printf "${BRED}ERROR:${RESET} Failed to start Envoy Mobile Gateway. Try running manually:\n"
    printf "${YELLOW}  sudo envoy --base-id 1 -c deployments/dev/envoy-gateways/gitpod.pixel.yaml${RESET}\n"
fi

printf "${BCYAN}INFO:${RESET} Starting kubectl port forwards in background...\n"
# Start ETCD port forward in background
nohup kubectl port-forward services/etcd-service 2379:2379 --address 0.0.0.0 > /tmp/etcd-forward.log 2>&1 &
ETCD_PID=$!
# Start RabbitMQ port forward in background
nohup kubectl port-forward services/bus-service 25672:15672 --address 0.0.0.0 > /tmp/rabbitmq-forward.log 2>&1 &
RABBITMQ_PID=$!

# Check if they started successfully (gives them a moment to start)
sleep 2
if ps -p $ETCD_PID > /dev/null; then
    printf "${BGREEN}SUCCESS:${RESET} ETCD port forward started (PID: $ETCD_PID).\n"
else
    printf "${BRED}ERROR:${RESET} ETCD port forward failed to start. Check /tmp/etcd-forward.log\n"
    printf "${YELLOW}  Run manually: kubectl port-forward services/etcd-service 2379:2379 --address 0.0.0.0${RESET}\n"
fi

if ps -p $RABBITMQ_PID > /dev/null; then
    printf "${BGREEN}SUCCESS:${RESET} RabbitMQ port forward started (PID: $RABBITMQ_PID).\n"
else
    printf "${BRED}ERROR:${RESET} RabbitMQ port forward failed to start. Check /tmp/rabbitmq-forward.log\n"
    printf "${YELLOW}  Run manually: kubectl port-forward services/bus-service 25672:15672 --address 0.0.0.0${RESET}\n"
fi

subheader "Golang Debugger (Delve) Setup"
printf "${BCYAN}INFO:${RESET} Installing/updating the Delve (dlv) debugger for Go...\n"
if command -v go &> /dev/null; then
    # Install the latest version of Delve
    if go install github.com/go-delve/delve/cmd/dlv@latest; then
        printf "${BGREEN}SUCCESS:${RESET} Delve debugger installed/updated successfully.\n"
        # Make sure the Go bin directory is in PATH
        if [[ ":$PATH:" != *":/go/bin:"* ]] && [[ -d "/go/bin" ]]; then
            printf "${BCYAN}INFO:${RESET} Adding /go/bin to PATH for this session\n"
            export PATH=$PATH:/go/bin
        fi
        DLV_PATH=$(which dlv || echo "not found")
        printf "${BCYAN}INFO:${RESET} Delve installed at: ${DLV_PATH}\n"
    else
        printf "${BRED}ERROR:${RESET} Failed to install Delve debugger. Try manually:\n"
        printf "${YELLOW}  go install github.com/go-delve/delve/cmd/dlv@latest${RESET}\n"
    fi
else
    printf "${BRED}ERROR:${RESET} Go is not available in PATH. Cannot install Delve.\n"
    printf "${YELLOW}  Ensure Go is installed and in your PATH before installing Delve.${RESET}\n"
fi

printf "${BGREEN}SUCCESS:${RESET} All post-attach commands executed.\n" 