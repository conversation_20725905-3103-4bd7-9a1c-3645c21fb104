# Yochbee Backend Dev Container Documentation

This document outlines how to set up and use the Dev Container configuration for the Yochbee Backend project.

## Option 1: Running on a Remote Linux Server (via SSH)

This section covers setup when working on a remote Linux server.

### Prerequisites (Remote Server)

1.  **Remote Linux Server**: Access to a Linux server (e.g., via SSH).
2.  **Git**: Git must be installed on the remote server (`sudo apt update && sudo apt install git`).
3.  **Docker**: Docker Engine must be installed and running on the remote server. Follow the [official Docker installation guide](https://docs.docker.com/engine/install/) for your Linux distribution. Ensure the Docker daemon is running and your user has permissions to interact with it (usually by adding your user to the `docker` group: `sudo usermod -aG docker $USER` and then logging out/in).
4.  **Local Machine**: Visual Studio Code installed.
5.  **VS Code Extension**: The [Remote - SSH](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh) extension installed in your local VS Code.

### Setup and Launch Steps (Remote Server)

1.  **Connect to Remote Server**: Open VS Code on your local machine and use the Remote - SSH extension to connect to your remote Linux server.
    *   Click the green Remote indicator icon in the bottom-left corner of VS Code.
    *   Select "Connect to Host..." or "Connect Current Window to Host...".
    *   Enter your SSH connection string (e.g., `user@your-server-ip-or-hostname`).
    *   Authenticate as required (password or SSH key).

2.  **Clone Repository (on Remote)**: Once connected via SSH, open a terminal within VS Code (Terminal -> New Terminal). Clone the Yochbee Backend repository onto the remote server:
    ```bash
    git clone <your-repository-url> # Replace with the actual repo URL
    cd <repository-directory>       # Replace with the cloned directory name
    ```

3.  **Open Project Folder (on Remote)**: In VS Code (still connected via SSH), go to File -> Open Folder... and navigate to the directory where you cloned the repository *on the remote server*.

4.  **Reopen in Container**: VS Code will detect the `.devcontainer/devcontainer.json` file and display a notification asking if you want to "Reopen in Container".
    *   Click "Reopen in Container".

5.  **Wait for Build/Start**: VS Code will now build the image and start the container *on the remote server*. Follow the logs in the terminal for progress.

6.  **Ready**: Once the container is running and `postAttachCommand` completes, your environment is ready. The VS Code window is connected to the Dev Container running on the remote host.

## Option 2: Running Locally (using Docker Desktop)

This section covers setup using Docker Desktop on your local machine (Windows, macOS, or Linux).

### Prerequisites (Local Machine)

1.  **Docker Desktop**: Install and run Docker Desktop for your operating system ([Windows](https://docs.docker.com/desktop/install/windows-install/), [macOS](https://docs.docker.com/desktop/install/mac-install/), [Linux](https://docs.docker.com/desktop/install/linux-install/)). Ensure it's running.
2.  **Git**: Git must be installed locally.
3.  **Visual Studio Code**: VS Code installed locally.
4.  **VS Code Extension**: The [Dev Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) extension installed in VS Code (this replaces the need for Remote - SSH for this local workflow).

### Setup and Launch Steps (Local Machine)

1.  **Clone Repository (Locally)**: Open a terminal on your *local* machine. Clone the Yochbee Backend repository:
    ```bash
    git clone <your-repository-url> # Replace with the actual repo URL
    cd <repository-directory>       # Replace with the cloned directory name
    ```

2.  **Open Project Folder**: Open VS Code *locally*. Go to File -> Open Folder... and navigate to the directory where you cloned the repository on your local machine.

3.  **Reopen in Container**: VS Code will detect the `.devcontainer/devcontainer.json` file and display a notification asking if you want to "Reopen in Container".
    *   Click "Reopen in Container".

4.  **Wait for Build/Start**: VS Code will use Docker Desktop to build the image and start the container *on your local machine*. Follow the logs in the terminal for progress.

5.  **Ready**: Once the container is running and `postAttachCommand` completes, your environment is ready. The VS Code window is connected to the Dev Container running locally via Docker Desktop.

## Container Lifecycle & Startup Process

Regardless of whether you run remotely or locally, once you click "Reopen in Container", the following happens:

*   **Build the Docker image**: Using `.devcontainer/Dockerfile` as a base, applying the Dev Container features specified in `devcontainer.json`. This happens only the first time or when the configuration changes.
*   **Create and start the container**: Mounting your source code and specified volumes.
*   **Run `postCreateCommand`**: Executes one-time setup tasks like `go mod download`, `go install stringer`, git configuration, and warming up the Go compiler.
*   **Run `postStartCommand`**: Executes every time the container starts. This includes:
    *   Displaying informational messages about manual Azure login steps.
    *   Starting the RabbitMQ service via `docker compose`.
    *   Displaying informational messages about `/etc/hosts` alternatives.
    *   Attempting to connect Tailscale (`sudo tailscale up ...`).
    *   Displaying reminder messages.
*   **Run `postAttachCommand`**: Executes every time VS Code attaches to the running container, displaying welcome messages and reminders about manually running Envoy or `kubectl port-forward` if needed.

## Project-Specific Considerations (Applies to Both Remote and Local)

*   **Azure Authentication**: The first time you start the Dev Container (or after credentials expire), you will need to manually run the Azure login commands displayed in the terminal (`az login --use-device-code`, `az account set`, `az aks get-credentials`, `kubelogin convert-kubeconfig`, `az acr login`). Your credentials will typically be cached by the Azure CLI within the container for subsequent sessions.
*   **Docker**: The container uses the Docker daemon running on your **host** machine (either the remote server or your local machine via Docker Desktop) using the `docker-outside-of-docker` feature. Docker commands run inside the container will interact with your host's Docker.
*   **RabbitMQ**: A RabbitMQ instance for development is automatically started via Docker Compose during the `postStartCommand`.
*   **/etc/hosts Modifications**: This setup **does not** automatically modify the container's `/etc/hosts` file like the original Gitpod configuration did. For service discovery:
    *   Use Docker Compose service names (e.g., `rabbitmq` if defined in `gitpod.docker-compose.yaml`) for services running via compose.
    *   Use Kubernetes service names (e.g., `etcd-service.default.svc.cluster.local`) when interacting with the cluster via `kubectl`.
    *   To access services running directly on your *host* machine from *inside* the container, use `host.docker.internal`.
    *   The informational messages printed during startup list the original hostnames for reference.
*   **Tailscale**: The Tailscale feature and `postStartCommand` script handle starting Tailscale. You may be prompted to log in the first time. Your Tailscale authentication state is persisted across container rebuilds using a named Docker volume (`yochbee-tailscale-state`) mounted at `/var/lib/tailscale`.
*   **Envoy & Port Forwarding**: The original Gitpod configuration had dedicated tasks for running Envoy and `kubectl port-forward` commands.
    *   **Envoy**: Installed via a feature. You need to run it manually if required for your workflow. The command used in Gitpod is echoed during `postAttachCommand` for reference (`sudo envoy --base-id 1 -c deployments/dev/envoy-gateways/gitpod.pixel.yaml`).
    *   **Kubectl Port Forward**: Commands for forwarding Staging ETCD and RabbitMQ UI are *not* run automatically. You need to run them manually in a container terminal if needed (commands are echoed during `postAttachCommand`).
*   **User**: You will be operating as the `vscode` user inside the container.
*   **Volume Mounts**:
    *   `${localEnv:HOME}/.cache/go-build` (Host) -> `/home/<USER>/.cache/go-build` (Container): Persists Go build cache from your host into the container (bind mount).
    *   `yochbee-gomodcache` (Volume) -> `/go/pkg/mod` (Container): Persists downloaded Go modules (named volume).
    *   `yochbee-tailscale-state` (Volume) -> `/var/lib/tailscale` (Container): Persists Tailscale authentication state (named volume).
*   **VS Code Extensions**: Extensions listed in `devcontainer.json` (Go, Git Graph, etc.) are automatically installed inside the container environment. 