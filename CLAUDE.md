# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) and AI agents when working with code in this repository.

## Architecture Overview

Yochbee is a microservices-based fintech backend built in Go, implementing a hybrid architecture that balances granularity with maintainability. The system uses message-driven communication through RabbitMQ and stores configurations in etcd.

### Core Components

- **Microservices**: Located in `src/cmd/microservices/`, each service is a self-contained unit

  - `accounts` - User account management and authentication
  - `banking` - Financial operations, transactions, cards management
  - `business` - Business logic coordination
  - `kyc` - Know Your Customer document processing
  - `notifications` - Email, SMS, and push notifications
  - `treezor` - Integration with Treezor payment services
  - `treezorwebhookhandler` - Webhook processing for Treezor events

- **Base Framework**: Common utilities in `src/_base/`

  - Dependency injection (Uber FX)
  - Bus communication (RabbitMQ)
  - Configuration management (etcd)
  - Database abstraction (MongoDB)
  - Security utilities
  - Web framework (Echo)

- **Common Models**: Shared data structures in `src/common/`
  - Domain models for each service
  - RPC method definitions
  - Database access patterns

### Communication Patterns

- **Bus Communication**: All inter-service communication uses RabbitMQ

  - Methods are registered in `src/_base/methods/` as constants
  - RPC calls use `rpc.Call()` with timeout handling
  - Events use topic-based pub/sub pattern

- **External APIs**: REST endpoints exposed via Echo web framework
  - Each service registers routes in `RegisterRESTAPIs()` functions
  - Health check endpoints at `/healthz`

## Development Commands

### Building and Running

```bash
# Build all services
cd src && go build ./cmd/microservices/accounts
cd src && go build ./cmd/microservices/banking
cd src && go build ./cmd/microservices/business
cd src && go build ./cmd/microservices/kyc
cd src && go build ./cmd/microservices/notifications
cd src && go build ./cmd/microservices/treezor
cd src && go build ./cmd/microservices/treezorwebhookhandler

# Run tests
cd src && go test ./...

# Run specific tests
cd src && go test ./path/to/package

# Run with verbose output
cd src && go test -v ./...
```

### Docker Operations

```bash
# Build production images
docker build -f Dockerfile-accounts-PROD -t yochbee-accounts .
docker build -f Dockerfile-banking-PROD -t yochbee-banking .
docker build -f Dockerfile-business-PROD -t yochbee-business .
docker build -f Dockerfile-kyc-PROD -t yochbee-kyc .
docker build -f Dockerfile-notifications-PROD -t yochbee-notifications .
docker build -f Dockerfile-treezor-PROD -t yochbee-treezor .
docker build -f Dockerfile-treezor-webhook-handler-PROD -t yochbee-treezor-webhook-handler .
```

### Deployment

```bash
# Deploy to staging
cd deployments/staging && ./redeploy-all.sh

# Deploy to production
cd deployments/production && ./redeploy-all.sh

# Deploy individual services
cd deployments/staging && ./redeploy-accounts.sh
cd deployments/staging && ./redeploy-banking.sh
# (etc.)
```

### Development Sync

```bash
# Sync code to remote development environment
./sync-up.sh

# Sync code from remote development environment
./sync-down.sh
```

## Code Structure Guidelines

### Microservice Structure

Each microservice follows this pattern:

- `main.go` - Entry point with dependency injection setup
- `collection--*.go` - Database collection definitions
- `services--*.go` - Business logic and RPC method handlers
- `subscriptions--*.go` - Event subscription handlers
- `webservices--*.go` - REST API endpoint handlers

### Adding New Dependencies

1. Add provider function to `src/_base/dependencies.go`
2. Register in `fx.Provide()` section of `SetupApp()`
3. Use dependency injection in service constructors

### Creating New RPC Methods

1. Define method name constant in `src/_base/methods/`
2. Register method in service's `RegisterBusMethods()`
3. Implement idempotent handler function
4. Add timeout handling for calls

### Database Operations

- Use MongoDB collections through dependency injection
- Implement query builders in `src/_base/dbnosql/mongodb/`
- Follow existing patterns in `collection--*.go` files

## Testing

### Running Tests

```bash
# All tests
cd src && go test ./...

# Specific package
cd src && go test ./common/treezor

# With coverage
cd src && go test -cover ./...

# Verbose output
cd src && go test -v ./...
```

### Test Files

Test files follow the pattern `*_test.go` and are located alongside source files. Key test areas:

- Base framework utilities
- Model validation and transformation
- Business logic units
- Integration patterns

## Configuration

### etcd Configuration

Access etcd via port-forward:

```bash
# Staging
cd deployments/staging && ./portforward-etcd-127.0.2.1:12379.sh

# Production
cd deployments/production && ./portforward-etcd-127.0.2.1:12379.sh
```

### Environment-Specific Settings

- Development: `deployments/dev/README.md`
- Staging: `deployments/staging/`
- Production: `deployments/production/`

## HTTP Testing

Use `.http` files in `deployments/dev/` for API testing:

- `staging-treezor-api.http`
- `staging-users-management.http`
- `staging-webhook-management.http`

## Logging and Monitoring

### SEQ Log Analysis

When asked to check server logs or analyze system issues, refer to `.cursor/rules/seq-usage-rules.mdc` for detailed SEQ logging system usage guidelines. This includes:

- Loading environment variables from `.env.seq`
- Querying logs via SEQ HTTP API
- Filtering by service, log level, and time ranges
- Analyzing errors and system health

## Key Principles

1. **Idempotency**: All bus methods and event handlers must be idempotent
2. **Lazy Loading**: Dependencies are loaded only when needed
3. **Abstraction**: Use interfaces for all external dependencies
4. **Timeouts**: Always specify timeouts for RPC calls
5. **Health Checks**: Include `/healthz` endpoint in all services
6. **Error Handling**: Proper error propagation and logging
