# Guide d'intégration - Notifications SDDR - August 4th, 2025

## Nouveaux champs dans l'API

L'endpoint existant `/notifications/:accountId` retourne maintenant des champs supplémentaires pour les notifications SDDR :

```json
{
  // Champs existants...
  
  // Nouveaux champs SDDR (optionnels)
  "notificationType": "sddr_reception" | "sddr_return",
  "sepaTransactionId": "CACP.*********.1586",
  "sepaMandateId": "********************",
  "sepaAmount": "4.99",  // Format décimal avec 2 décimales
  "sepaCreditorName": "SFR",
  "sepaCollectionDate": "2025-07-22 00:00:00",  // Format original du webhook
  "sepaReasonCode": "AM04",                     // Seulement pour sddr_return
  "sepaReasonText": "Provisions insuffisantes"  // Seulement pour sddr_return
}
```

## Types de notifications

### 1. `sddr_reception` - Prélèvement à venir
- **Titre** : "Prélèvement SEPA programmé"
- **Message** : "Prélèvement SEPA de 4.99 € prévu le 22/07/2025 par SFR"
- **Action suggérée** : Vérifier le solde du compte
- **Note** : La date dans le message est formatée (DD/MM/YYYY) mais `sepaCollectionDate` garde le format original

### 2. `sddr_return` - Prélèvement rejeté  
- **Titre** : "Prélèvement SEPA rejeté"
- **Message** : "Prélèvement SEPA rejeté : Provisions insuffisantes - Montant 4.99 € par SFR"
- **Action suggérée** : Selon le code raison (ex: ajouter des fonds si AM04)

## Codes de rejet principaux

| Code | Texte français | Action |
|------|---------------|---------|
| AM04 | Provisions insuffisantes | Ajouter des fonds |
| AG01 | Transaction interdite sur ce type de compte | Changer de méthode |
| AC04 | Compte fermé | Mettre à jour le compte |
| MS03 | Raison non spécifiée | Contacter le support |

## Payload Firebase

```json
// Reception
{
  "notificationType": "sddr_reception",
  "transactionID": "CACP.*********.1586",
  "amount": "4.99",  // Format décimal, pas en centimes
  "creditorName": "SFR",
  "collectionDate": "2025-07-22 00:00:00"
}

// Rejet
{
  "notificationType": "sddr_return",
  "transactionID": "CACP.*********.1586",
  "amount": "4.99",  // Format décimal, pas en centimes
  "reasonCode": "AM04",
  "reasonText": "Provisions insuffisantes",
  "creditorName": "SFR"
}
```

## Points essentiels

✅ Le champ `sepaReasonText` contient toujours le texte en français  
✅ Utiliser `notificationType` pour identifier les notifications SDDR  
✅ Les rejets (returns) sont prioritaires - afficher en urgence  
✅ Tous les champs SDDR sont optionnels (omitempty)
✅ Les montants sont en format décimal (ex: "4.99" pour 4,99 €), pas en centimes