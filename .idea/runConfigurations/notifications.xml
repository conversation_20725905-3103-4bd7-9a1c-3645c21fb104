<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="notifications" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="Yochbee-Backend" />
    <working_directory value="$PROJECT_DIR$/src" />
    <envs>
      <env name="ETCD_ENDPOINTS" value="yochbee-backend:2379" />
      <env name="FAKE_SEND_EMAIL" value="true" />
      <env name="FAKE_SEND_EMAIL_FROM" value="true" />
      <env name="SEQ_URL" value="http://yochbee-backend:5341" />
      <env name="FAKE_SEND_SMS" value="true" />
    </envs>
    <kind value="PACKAGE" />
    <package value="yochbee/cmd/microservices/notifications" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/src/cmd/microservices/accounts/main.go" />
    <method v="2" />
  </configuration>
</component>