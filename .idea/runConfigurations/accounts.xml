<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="accounts" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="Yochbee-Backend" />
    <working_directory value="$PROJECT_DIR$/src" />
    <envs>
      <env name="ALLOW_ACCOUNTS_DELETION" value="true" />
      <env name="DEBUG" value="true" />
      <env name="ETCD_ENDPOINTS" value="yochbee-backend:2379" />
      <env name="SEQ_URL" value="http://yochbee-backend:5341" />
      <env name="DISABLE_AUTO_ONBOARDING_TRACKERS_CLEANUP" value="true" />
    </envs>
    <kind value="PACKAGE" />
    <package value="yochbee/cmd/microservices/accounts" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/src/cmd/microservices/accounts/main.go" />
    <method v="2" />
  </configuration>
</component>