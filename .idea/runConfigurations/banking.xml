<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="banking" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="Yochbee-Backend" />
    <working_directory value="$PROJECT_DIR$/src" />
    <envs>
      <env name="ETCD_ENDPOINTS" value="yochbee-backend:2379" />
      <env name="SEQ_URL" value="http://yochbee-backend:5341" />
    </envs>
    <kind value="PACKAGE" />
    <package value="yochbee/cmd/microservices/banking" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/src/cmd/microservices/accounts/main.go" />
    <method v="2" />
  </configuration>
</component>