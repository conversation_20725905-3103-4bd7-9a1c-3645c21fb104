<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="GoSnakeCaseUsage" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="GoStructInitializationWithoutFieldNames" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="GoUnhandledErrorResult" enabled="true" level="WARNING" enabled_by_default="true">
      <methods>
        <method importPath="hash" receiver="Hash" name="Write" />
        <method importPath="strings" receiver="*Builder" name="Write" />
        <method importPath="strings" receiver="*Builder" name="WriteByte" />
        <method importPath="bytes" receiver="*Buffer" name="WriteRune" />
        <method importPath="bytes" receiver="*Buffer" name="Write" />
        <method importPath="bytes" receiver="*Buffer" name="WriteString" />
        <method importPath="strings" receiver="*Builder" name="WriteString" />
        <method importPath="bytes" receiver="*Buffer" name="WriteByte" />
        <method importPath="strings" receiver="*Builder" name="WriteRune" />
        <method importPath="math/rand" receiver="*Rand" name="Read" />
        <method importPath="github.com/streadway/amqp" receiver="*Channel" name="Cancel" />
        <method importPath="github.com/streadway/amqp" receiver="*Channel" name="QueueDelete" />
        <method importPath="io" receiver="ReadCloser" name="Close" />
        <method importPath="mime/multipart" receiver="File" name="Close" />
        <method importPath="io" receiver="WriteCloser" name="Close" />
      </methods>
    </inspection_tool>
  </profile>
</component>