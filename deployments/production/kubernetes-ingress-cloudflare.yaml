---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cloudflared-config
  labels:
    environment: production
    app: ingress
data:
  config.yml: |
    tunnel: d62f21b4-f894-4781-a0a3-6e3032bc00b9
    credentials-file: /etc/cloudflared/d62f21b4-f894-4781-a0a3-6e3032bc00b9.json

    ingress:
      - hostname: mobile-api.yochbee.com
        service: http://gateway-mobile:8000                  # ----- points to Envoy Mobile gateway
      
      - hostname: treezor-webhook.yochbee.com
        service: http://treezor-webhook-handler-service:8082
        
      - hostname: logs.yochbee.com
        service: http://seq-logging-service:5341

      # Catch all
      - service: http_status:404
---
# This is not exposed as a Service
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    environment: production
    app: cloudflare-ingress
  name: cloudflare-ingress
spec:
  selector:
    matchLabels:
      app: cloudflare-ingress
  strategy:
    type: RollingUpdate
  revisionHistoryLimit: 1
  replicas: 1
  template:
    metadata:
      labels:
        app: cloudflare-ingress
    spec:
      restartPolicy: Always
      containers:
        - image: cloudflare/cloudflared:2023.8.1
          name: cloudflared
          resources:
            limits:
              cpu: 50m
              memory: 50Mi
          args:
            - tunnel
            - --no-autoupdate
            - --loglevel
            - debug
            - --protocol
            - http2
            - --config
            - /ingress/config.yml
            - run
          volumeMounts:
            - name: cloudflare-secrets
              mountPath: /etc/cloudflared
              readOnly: true
            - name: config
              mountPath: /ingress
              readOnly: true
      volumes:
        - name: cloudflare-secrets
          secret:
            secretName: cloudflare-secrets
        - name: config
          configMap:
            name: cloudflared-config
