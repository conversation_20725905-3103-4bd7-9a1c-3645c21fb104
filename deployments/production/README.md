# Yochbee deployment

This project is built to run on Kubernetes. It adopts a hybrid microservices architecture, where the services are 
not overly granular, but not too coarse-grained either.

With that being said, the first thing to do is to create a K8s cluster. I recommend using Azure Kubernetes Service 
as that is the one used along the development of this project. However, any other K8s cluster should work as well, 
especially if price is a concern (Azure is not cheap).

## Azure Kubernetes Service

The easiest way to create the proper configuration of the cluster is the Azure CLI, whether it is from the **Azure 
Portal Shell** or from your local machine (will require `az login` first). 

### Kubernetes version

The first thing to do is to check the latest version of Kubernetes available in Azure. This can be done with the
following command:

```bash
az aks get-versions --location francecentral --output table
```

Always use the latest that's available at deployment time. Periodically check for updates and upgrade the cluster
when possible. K8s is a fast-moving target, it is important to keep up with the latest versions.

### Create the cluster

Go to the Portal and create a `Production` resource group. https://portal.azure.com/#view/HubsExtension/BrowseResourceGroups

Then, create a cluster with 3 nodes (1 node to start and to validate), each with 2 cores and 7GB of RAM:

```bash
az aks create --resource-group Production --name YochbeeProdK8s --node-count 1 \
  --network-plugin azure --location francecentral \
  --kubernetes-version 1.27.1 \
  --node-osdisk-size 128 --node-vm-size Standard_DS2_v2 --node-osdisk-type Managed \
  --attach-acr yochbee --enable-cluster-autoscaler --min-count 1 --max-count 6 --generate-ssh-keys
```

After the creation of the Cluster, it will be running with these default elements in the cluster:

![Default pods with no workloads](README.md.files/img_0.png)

![Default Config Maps](README.md.files/img.png)

![Default Secrets](README.md.files/img_1.png)

![Default Services](README.md.files/img_2.png)

![Default Namespaces](README.md.files/img_3.png)

For the management, I recommend using Lens Desktop (it is a paid product, but it is worth it). The metrics have been 
installed to make it easier to track the cluster health right within Lens.

![Activated metrics for Lens](README.md.files/img_4.png)

### Azure CLI integration

To integrate the cluster with the Azure CLI, make sure you are logged-in as `<EMAIL>` in your browser, then 
run the following command:

```bash
az login
az account set --subscription 7e9d31aa-c867-4320-8027-5d83be33d4aa # This is the YoChBee subscription
az aks get-credentials --resource-group Production --name YochbeeProdK8s

# List and select the context
kubectl config get-contexts
kubectl config use-context YochbeeProdK8s

# To test the connection
kubectl get deployments --all-namespaces=true
```

## First deployments

During deployment, it is ideal that the ETCD service is installed first, then the other services. This is because 
the other services will need to read the configurations from ETCD. You can do so by using the 
`kubernetes-production.yaml` file.

Then, deploy the `kubernetes-redis-locks.yaml` which will be used for distributed locks.

## Domain name

## Configurations

These are production configurations.

### ETCD contents

In order to edit the configurations, you'll need to port-forward the ETCD service to your local machine:

```bash
kubectl --context YochbeeProdK8s port-forward services/etcd-service 2379:2379
```

Then, you can use tools such as ETCD-manager to edit the configurations:

For key `config.bus.yochbee.mobile`:

```json
{
  "connectionString": "***********************************",
  "exchange": "yochbee",
  "exchangeType": "direct",
  "durable": true,
  "autoDelete": false
}
```

For key `config.bus.yochbee.signals`:

```json
{
  "connectionString": "***********************************",
  "exchange": "yochbee-signals",
  "exchangeType": "topic",
  "durable": true,
  "autoDelete": false
}
```

For key `config.database.mongodb.connection_string` (update with real connection string):

```text
mongodb://mongodb-srv:27017
```

For key `notifications.emails.noreply` (this is an example of Gmail, but it should be something different):

```json
{
  "host": "smtp.gmail.com",
  "port": 465,
  "from": "<EMAIL>",
  "username": "...",
  "password": "...",
  "insecure": false
}
```

For key `config.database.redis`:

```json
{
  "host": "redis-locks",
  "port": 6379,
  "password": "",
  "db": 0
}
```

For key `config.database.redis.locks`. This is the Redis instance used for locks (a local instance):

```json
{
  "host": "redis-locks",
  "port": 6379,
  "password": "",
  "db": 0
}
```

For key `config.treezor`:

```json
{
  "clientId": "...",
  "clientSecret": "...",
  "coreConnectApiBaseUrl": "...",
  "tariffId": 244,
  "cardPrint": 14445,
  "webhookSecret": "..."
}
```

For key `config.hipay`:

```json
{
  "baseUrl": "https://stage-api.hipay.com",
  "privateToken": {
    "username": "...",
    "password": "..."
  },
  "publicToken": {
    "username": "...",
    "password": "..."
  }
}
```

For key `notifications.sms.twilio`:

```json
{
  "accountSid": "**********************************",
  "authToken": "1330b3abf99e8efecd55043f1aed35e6",
  "phoneNumber": "+***********"
}

```

For key `storage.default`. This will need to be created manually in Azure Storage.

```json
{
  "accountName": "yochbeeproduction",
  "accountKey": "..."
}
```

## Remaining deployments

If the production images for the services are not built yet, you should do so now.

Now that the configurations are in place and the images ready, you can deploy the remaining services in this exact 
order:

- `kubernetes-redis-locks.yaml`
- `kubernetes-seq.yaml`
- `kubernetes-pdf-generator.yaml`
- `kubernetes-production.yaml`
- `kubernetes-gateways-production.yaml`
- `kubernetes-ingress-cloudflare.yaml`
