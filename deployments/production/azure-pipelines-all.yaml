trigger:
  branches:
    include:
      - production

resources:
  - repo: self

pool:
  vmImage: "ubuntu-latest"

variables:
  CGO_ENABLED: 0

# Build the following microservices one by one:
# - accounts
# - banking
# - business
# - kyc
# - notifications
# - treezor
# - treezor-webhook-handler
#
# Then, it restarts the Deployment of the microservices one by one, having a delay of 10 seconds between each restart.

steps:
  - task: Docker@2
    displayName: Build the base builder image
    inputs:
      command: buildAndPush
      repository: "builder/production"
      dockerfile: "Dockerfile-builder"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image accounts/production
    inputs:
      command: buildAndPush
      repository: "accounts/production"
      dockerfile: "Dockerfile-accounts-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image banking/production
    inputs:
      command: buildAndPush
      repository: "banking/production"
      dockerfile: "Dockerfile-banking-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image business/production
    inputs:
      command: buildAndPush
      repository: "business/production"
      dockerfile: "Dockerfile-business-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image kyc/production
    inputs:
      command: buildAndPush
      repository: "kyc/production"
      dockerfile: "Dockerfile-kyc-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image notifications/production
    inputs:
      command: buildAndPush
      repository: "notifications/production"
      dockerfile: "Dockerfile-notifications-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image treezor/production
    inputs:
      command: buildAndPush
      repository: "treezor/production"
      dockerfile: "Dockerfile-treezor-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image treezor-webhook-handler/production
    inputs:
      command: buildAndPush
      repository: "treezor-webhook-handler/production"
      dockerfile: "Dockerfile-treezor-webhook-handler-PROD"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Kubernetes@1
    displayName: "Restart treezor"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/treezor"
  - bash: sleep 10
    displayName: "Wait 10 seconds"

  - task: Kubernetes@1
    displayName: "Restart treezor-webhook-handler"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/treezor-webhook-handler"

  - task: Kubernetes@1
    displayName: "Restart accounts"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/accounts"
  - bash: sleep 10
    displayName: "Wait 10 seconds"

  - task: Kubernetes@1
    displayName: "Restart banking"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/banking"
  - bash: sleep 10
    displayName: "Wait 10 seconds"

  - task: Kubernetes@1
    displayName: "Restart business"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/business"
  - bash: sleep 10
    displayName: "Wait 10 seconds"

  - task: Kubernetes@1
    displayName: "Restart kyc"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/kyc"
  - bash: sleep 10
    displayName: "Wait 10 seconds"

  - task: Kubernetes@1
    displayName: "Restart notifications"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-prod-admin-defaultns"
      command: "rollout"
      arguments: "restart deployment/notifications"