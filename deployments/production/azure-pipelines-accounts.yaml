trigger: none

resources:
  - repo: self

pool:
  vmImage: 'ubuntu-20.04'

variables:
  CGO_ENABLED: 0

steps:
  - task: Docker@2
    # Name of the final image is yochbee.azurecr.io/accounts/production:latest
    displayName: Build image accounts/production
    inputs:
      command: buildAndPush
      repository: 'accounts/production'
      dockerfile: 'Dockerfile-accounts'
      containerRegistry: '81b7e02e-5b0d-49ca-860a-c76f580ec338'
      tags: 'latest'
