#### Apply with:
#
# > kubectl apply -f kubernetes-production.yaml -n default
#
########################################################################################################################
---
# ETCD (configurations provider)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: etcd
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: etcd
  template:
    metadata:
      labels:
        app: etcd
    spec:
      restartPolicy: Always
      containers:
        - image: quay.io/coreos/etcd
          name: etcd
          args:
            - /usr/local/bin/etcd
            - --data-dir=/etcd-data
            - --name=node1
            - --initial-advertise-peer-urls=http://127.0.0.1:2380
            - --listen-peer-urls=http://0.0.0.0:2380
            - --advertise-client-urls=http://127.0.0.1:2379
            - --listen-client-urls=http://0.0.0.0:2379
            - --initial-cluster=node1=http://127.0.0.1:2380
          ports:
            - containerPort: 2379
            - containerPort: 2380
          volumeMounts:
            - mountPath: /etcd-data
              name: etcd-data
          resources:
            limits:
              cpu: 20m
              memory: 25Mi
      volumes:
        - name: etcd-data
          persistentVolumeClaim:
            claimName: etcd-data
---
apiVersion: v1
kind: Service
metadata:
  name: etcd-service
spec:
  selector:
    app: etcd
  ports:
    - name: "2379"
      port: 2379
      targetPort: 2379
    - name: "2380"
      port: 2380
      targetPort: 2380
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: etcd-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Mi
########################################################################################################################
---
# Bus (RabbitMQ)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bus
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: bus
  template:
    metadata:
      labels:
        app: bus
    spec:
      restartPolicy: Always
      containers:
        - image: rabbitmq:3.9.14-management
          name: rabbitmq
          ports:
            - containerPort: 15672
            - containerPort: 5672
          resources:
            limits:
              cpu: 100m
              memory: 200Mi
---
apiVersion: v1
kind: Service
metadata:
  name: bus-service
spec:
  selector:
    app: bus
  ports:
    - name: "management"
      port: 15672
      targetPort: 15672
    - name: "amqp"
      port: 5672
      targetPort: 5672
########################################################################################################################
---
# Accounts
apiVersion: apps/v1
kind: Deployment
metadata:
  name: accounts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: accounts
  template:
    metadata:
      labels:
        app: accounts
    spec:
      restartPolicy: Always
      containers:
        - name: accounts
          image: yochbee.azurecr.io/accounts/production:latest
          resources: {}
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: accounts-service
spec:
  selector:
    app: accounts
  ports:
    - name: "http"
      port: 8080
      targetPort: 8080
########################################################################################################################
---
# Notifications
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notifications
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notifications
  template:
    metadata:
      labels:
        app: notifications
      annotations:
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
    spec:
      restartPolicy: Always
      containers:
        - name: notifications
          image: yochbee.azurecr.io/notifications/production:latest
          resources:
            limits:
              cpu: 20m
              memory: 50Mi
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
          ports:
            - containerPort: 8090
---
apiVersion: v1
kind: Service
metadata:
  name: notifications-service
spec:
  selector:
    app: notifications
  ports:
    - name: "http"
      port: 8090
      targetPort: 8090
########################################################################################################################
---
# Treezor
apiVersion: apps/v1
kind: Deployment
metadata:
  name: treezor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: treezor
  template:
    metadata:
      labels:
        app: treezor
    spec:
      restartPolicy: Always
      containers:
        - name: treezor
          image: yochbee.azurecr.io/treezor/production:latest
          resources:
            limits:
              cpu: 50m
              memory: 50Mi
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
            - name: ALLOW_TOKENS_LOGGING
              value: "true"
            - name: DEBUG
              value: "true"
########################################################################################################################
---
# Treezor Webhook handler
apiVersion: apps/v1
kind: Deployment
metadata:
  name: treezor-webhook-handler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: treezor-webhook-handler
  template:
    metadata:
      labels:
        app: treezor-webhook-handler
    spec:
      restartPolicy: Always
      containers:
        - name: treezor-webhook-handler
          image: yochbee.azurecr.io/treezor-webhook-handler/production:latest
          resources:
            limits:
              cpu: 30m
              memory: 50Mi
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
          ports:
            - containerPort: 8082
---
apiVersion: v1
kind: Service
metadata:
  name: treezor-webhook-handler-service
spec:
  selector:
    app: treezor-webhook-handler
  ports:
    - name: "http-webhook"
      port: 8082
      targetPort: 8082
########################################################################################################################
---
# KYC
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kyc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kyc
  template:
    metadata:
      labels:
        app: kyc
    spec:
      restartPolicy: Always
      containers:
        - name: kyc
          image: yochbee.azurecr.io/kyc/production:latest
          resources:
            limits:
              cpu: 10m
              memory: 150Mi
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
          ports:
            - containerPort: 8084
---
apiVersion: v1
kind: Service
metadata:
  name: kyc-service
spec:
  selector:
    app: kyc
  ports:
    - name: "http"
      port: 8084
      targetPort: 8084
########################################################################################################################
---
# Banking
apiVersion: apps/v1
kind: Deployment
metadata:
  name: banking
spec:
  replicas: 1
  selector:
    matchLabels:
      app: banking
  template:
    metadata:
      labels:
        app: banking
    spec:
      restartPolicy: Always
      containers:
        - name: banking
          image: yochbee.azurecr.io/banking/production:latest
          resources:
            limits:
              cpu: 30m
              memory: 150Mi
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
          ports:
            - containerPort: 8086
---
apiVersion: v1
kind: Service
metadata:
  name: banking-services
spec:
  selector:
    app: banking
  ports:
    - name: "http"
      port: 8086
      targetPort: 8086
########################################################################################################################
---
# Business
apiVersion: apps/v1
kind: Deployment
metadata:
  name: business
spec:
  replicas: 1
  selector:
    matchLabels:
      app: business
  template:
    metadata:
      labels:
        app: business
    spec:
      restartPolicy: Always
      containers:
        - name: business
          image: yochbee.azurecr.io/business/production:latest
          resources:
            limits:
              cpu: 10m
              memory: 50Mi
          env:
            - name: ETCD_ENDPOINTS
              value: etcd-service:2379
            - name: SEQ_URL
              value: http://seq-logging-service:5341
          ports:
            - containerPort: 8088
---
apiVersion: v1
kind: Service
metadata:
  name: business-services
spec:
  selector:
    app: business
  ports:
    - name: "http"
      port: 8088
      targetPort: 8088
########################################################################################################################
---
apiVersion: v1
kind: Pod
metadata:
  name: busybox
  namespace: default
spec:
  containers:
    - name: busybox
      image: busybox:1.28
      command:
        - sleep
        - "3600"
  restartPolicy: Always
