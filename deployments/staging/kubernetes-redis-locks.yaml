# This is a redis deployment for the distributed locks.

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-locks
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-locks
  template:
    metadata:
      labels:
        app: redis-locks
    spec:
      containers:
        - name: redis
          image: redis:latest
          command:
            - redis-server
            - --maxmemory
            - 30mb
            - --maxmemory-policy
            - allkeys-lru
          ports:
            - containerPort: 6379
              name: redis
---
apiVersion: v1
kind: Service
metadata:
  name: redis-locks
spec:
  selector:
    app: redis-locks
  ports:
    - name: redis
      port: 6379
      targetPort: 6379
