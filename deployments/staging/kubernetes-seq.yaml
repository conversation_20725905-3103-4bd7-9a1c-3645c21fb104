---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    environment: staging
    app: seq-logging
  name: seq-logging
spec:
  selector:
    matchLabels:
      app: seq-logging
  strategy:
    type: RollingUpdate
  revisionHistoryLimit: 1
  replicas: 1
  template:
    metadata:
      labels:
        app: seq-logging
    spec:
      restartPolicy: Always
      containers:
        - image: datalust/seq
          name: seq
          ports:
            - containerPort: 80 # both the UI and the Ingestion will happen here
            - containerPort: 5341
          env:
            - name: ACCEPT_EULA
              value: 'Y'
            - name: SEQ_FIRSTRUN_ADMINPASSWORDHASH
              value: 'FBbV2r7TKngH8wf+ztK2QyFbSrdSm8b/+L3EapskDgMDwJiPVA==' # defaults to 'mlewgQQu7l4INeHui' in staging
          resources:
            limits:
              cpu: 100m
              memory: 800Mi
---
apiVersion: v1
kind: Service
metadata:
  name: seq-logging-service
spec:
  ports:
    - name: "http"
      port: 5341
      targetPort: 80
  selector:
    app: seq-logging
