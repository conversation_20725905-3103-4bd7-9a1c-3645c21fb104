# Yochbee staging configurations

These can be initial inspirations for final production configurations.

## ETCD contents

For key `config.bus.yochbee.mobile`:

```json
{
  "connectionString": "***********************************",
  "exchange": "yochbee",
  "exchangeType": "direct",
  "durable": true,
  "autoDelete": false
}
```

For key `config.bus.yochbee.signals`:

```json
{
  "connectionString": "***********************************",
  "exchange": "yochbee-signals",
  "exchangeType": "topic",
  "durable": true,
  "autoDelete": false
}
```

For key `config.database.mongodb.connection_string`:

```text
mongodb://microservices:<EMAIL>:27017/?directConnection=true&ssl=false
```

For key `config.database.redis.locks`. This is the Redis instance used for locks (a local instance):

```json
{
  "host": "redis-locks",
  "port": 6379,
  "password": "",
  "db": 0
}
```

For key `config.treezor`:

```json
{
  "clientId": "...",
  "clientSecret": "...",
  "coreConnectApiBaseUrl": "...",
  "tariffId": 244,
  "cardPrint": 14445,
  "webhookSecret": "..."
}
```

For key `config.hipay`:

```json
{
  "baseUrl": "https://stage-api.hipay.com",
  "privateToken": {
    "username": "...",
    "password": "..."
  },
  "publicToken": {
    "username": "...",
    "password": "..."
  }
}
```

For key `notifications.sms.twilio`:

```json
{
  "accountSid": "**********************************",
  "authToken": "1330b3abf99e8efecd55043f1aed35e6",
  "phoneNumber": "+***********"
}
```

For key `storage.default`:

```json
{
  "accountName": "yochbeestaging",
  "accountKey": "..."
}
```

For key `notifications.emails.noreply` (this is an example of Gmail, but it should be something different):

```json
{
  "host": "smtp.gmail.com",
  "port": 465,
  "from": "<EMAIL>",
  "username": "...",
  "password": "...",
  "insecure": false
}
```

## MongoDB

Ensure to use MongoDB Atlas instead of managing this by yourself (which is not worth it).

To create a user the microservice user:

```
use admin
db.createUser({
    user: "microservices",
    pwd: "q5TOWaSJVR2aIgaSALhbN",
    roles: [
        { role: "dbOwner", db: "yochbee" }
    ],
    mechanisms: [ "SCRAM-SHA-1", "SCRAM-SHA-256" ]
})
```
