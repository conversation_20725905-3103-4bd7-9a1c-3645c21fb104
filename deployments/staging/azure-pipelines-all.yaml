trigger:
  branches:
    include:
      - staging

resources:
  - repo: self

pool:
  vmImage: "ubuntu-latest"

variables:
  CGO_ENABLED: 0

# Build the following microservices one by one:
# - accounts
# - banking
# - business
# - kyc
# - notifications
# - treezor
# - treezor-webhook-handler
#
# Then, it restarts the Deployment of the microservices one by one, having a delay of 10 seconds between each restart.

steps:
  - task: Docker@2
    displayName: Build the base builder image
    inputs:
      command: buildAndPush
      repository: "builder/staging"
      dockerfile: "Dockerfile-builder"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image accounts/staging
    inputs:
      command: buildAndPush
      repository: "accounts/staging"
      dockerfile: "Dockerfile-accounts"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image banking/staging
    inputs:
      command: buildAndPush
      repository: "banking/staging"
      dockerfile: "Dockerfile-banking"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image business/staging
    inputs:
      command: buildAndPush
      repository: "business/staging"
      dockerfile: "Dockerfile-business"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image kyc/staging
    inputs:
      command: buildAndPush
      repository: "kyc/staging"
      dockerfile: "Dockerfile-kyc"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image notifications/staging
    inputs:
      command: buildAndPush
      repository: "notifications/staging"
      dockerfile: "Dockerfile-notifications"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image treezor/staging
    inputs:
      command: buildAndPush
      repository: "treezor/staging"
      dockerfile: "Dockerfile-treezor"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Docker@2
    displayName: Build image treezor-webhook-handler/staging
    inputs:
      command: buildAndPush
      repository: "treezor-webhook-handler/staging"
      dockerfile: "Dockerfile-treezor-webhook-handler"
      containerRegistry: "yochbee"
      tags: "latest"

  - task: Kubernetes@1
    displayName: "Restart treezor"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/treezor"
  - bash: sleep 3
    displayName: "Wait 3 seconds"

  - task: Kubernetes@1
    displayName: "Restart treezor-webhook-handler"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/treezor-webhook-handler"

  - task: Kubernetes@1
    displayName: "Restart accounts"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/accounts"
  - bash: sleep 3
    displayName: "Wait 3 seconds"

  - task: Kubernetes@1
    displayName: "Restart banking"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/banking"
  - bash: sleep 3
    displayName: "Wait 3 seconds"

  - task: Kubernetes@1
    displayName: "Restart business"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/business"
  - bash: sleep 3
    displayName: "Wait 3 seconds"

  - task: Kubernetes@1
    displayName: "Restart kyc"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/kyc"
  - bash: sleep 3
    displayName: "Wait 3 seconds"

  - task: Kubernetes@1
    displayName: "Restart notifications"
    inputs:
      connectionType: "Kubernetes Service Connection"
      kubernetesServiceEndpoint: "k8s-staging-admin-nsdefault"
      command: "rollout"
      arguments: "restart deployment/notifications"
