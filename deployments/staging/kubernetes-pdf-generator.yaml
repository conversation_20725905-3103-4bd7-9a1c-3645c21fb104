---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    environment: staging
    app: pdf-generator
  name: pdf-generator
spec:
  selector:
    matchLabels:
      app: pdf-generator
  strategy:
    type: RollingUpdate
  revisionHistoryLimit: 1
  replicas: 1
  template:
    metadata:
      labels:
        app: pdf-generator
    spec:
      restartPolicy: Always
      containers:
        - image: yochbee.azurecr.io/images/pdf-generator:latest
          name: pdf-generator
          ports:
            - containerPort: 8080
          env:
            - name: PORT
              value: '8080'
          resources: {}
---
apiVersion: v1
kind: Service
metadata:
  name: pdf-generator-service
spec:
  ports:
    - name: "http"
      port: 8080
      targetPort: 8080
  selector:
    app: pdf-generator
