# Yochbee dev configurations

These are necessary setups.

## ETCD contents

For key `config.bus.yochbee.mobile`:

```json
{
  "connectionString": "amqp://guest:guest@yochbee-backend:5672",
  "exchange": "yochbee",
  "exchangeType": "direct",
  "durable": true,
  "autoDelete": false
}
```

For key `config.bus.yochbee.signals`:

```json
{
  "connectionString": "amqp://guest:guest@yochbee-backend:5672",
  "exchange": "yochbee-signals",
  "exchangeType": "topic",
  "durable": true,
  "autoDelete": false
}
```

For key `config.database.mongodb.connection_string`:

```text
mongodb+srv://root:<EMAIL>/?retryWrites=true&w=majority
```

For key `config.database.redis`:

```json
{
  "host": "*********",
  "port": 6379,
  "password": "",
  "db": 0
}
```

For key `config.treezor`:

```json
{
  "clientId": "...",
  "clientSecret": "...",
  "coreConnectApiBaseUrl": "...",
  "tariffId": 244,
  "webhookSecret": "...",
  "isScaEnabled": "..."
}
```

For key `config.hipay`:

```json
{
  "baseUrl": "https://...",
  "privateToken": {
    "username": "...",
    "password": "..."
  },
  "publicToken": {
    "username": "...",
    "password": "..."
  }
}
```

For key `notifications.sms.twilio`:

```json
{
  "accountSid": "**********************************",
  "authToken": "1330b3abf99e8efecd55043f1aed35e6",
  "phoneNumber": "+***********"
}
```

For key `storage.default`:

```json
{
  "accountName": "yochbeestaging",
  "accountKey": "..."
}
```

For key `notifications.emails.noreply` (this is an example of Gmail, but it should be something different):

```json
{
  "host": "smtp.gmail.com",
  "port": 465,
  "from": "<EMAIL>",
  "username": "...",
  "password": "...",
  "insecure": false
}
```

## MongoDB

Ensure to use MongoDB Atlas instead of managing this by yourself (which is not worth it).

To create a user the microservice user:

```
use admin
db.createUser({
    user: "microservices",
    pwd: "q5TOWaSJVR2aIgaSALhbN",
    roles: [
        { role: "dbOwner", db: "yochbee" }
    ],
    mechanisms: [ "SCRAM-SHA-1", "SCRAM-SHA-256" ]
})
```
