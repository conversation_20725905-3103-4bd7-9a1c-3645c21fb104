# Run using the following command:
# > docker compose --file gitpod.docker-compose.yaml --project-name yochbee-dev up -d
#
# NOTE: There is no MongoDB because we're using MongoDB Atlas directly. Similar to ETCD.

services:
  rabbitmq-mb:
    image: rabbitmq:3-management
    restart: always
    ports:
      - 127.0.0.1:15672:15672
      - *********:15672:15672
      - 127.0.0.1:5672:5672
      - *********:5672:5672

  redis:
    image: redis
    restart: always
    command: [ "redis-server", "--appendonly", "yes" ]
    hostname: redis
    ports:
      - 127.0.0.1:6379:6379
      - *********:6379:6379
    volumes:
      - redis-data:/data

  seq:
    image: datalust/seq
    ports:
      - 127.0.0.1:5341:80
      - *********:5341:80
    environment:
      - ACCEPT_EULA=Y
      - SEQ_FIRSTRUN_ADMINPASSWORDHASH=FI33s4+PklYoclTY7o5M7/2mnUAzE1Cp7mUOWkbWb+lhsoO1rQ== # defaults to '12345678'
    volumes:
      - seq_data:/data

  # etcd:
  #   image: gcr.io/etcd-development/etcd:v3.5.0
  #   volumes:
  #     - etcd_data:/etcd-data
  #   command:
  #     - /usr/local/bin/etcd
  #     - --data-dir=/etcd-data
  #     - --name=node1
  #     - --initial-advertise-peer-urls=http://0.0.0.0:2380
  #     - --listen-peer-urls=http://0.0.0.0:2380
  #     - --advertise-client-urls=http://0.0.0.0:2379
  #     - --listen-client-urls=http://0.0.0.0:2379
  #     - --initial-cluster=node1=http://0.0.0.0:2380
  #   ports:
  #     - 127.0.0.1:2379:2379
  #     - 127.0.0.1:2380:2380

volumes:
  redis-data:
  seq_data:
  etcd_data:
