POST https://dev-treezor-webhook-hctbzktfsb4iczk7.requestcatcher.com/webhook
Content-Type: application/json

{}

###

POST https://25d5-8-36-216-116.eu.ngrok.io/webhook
Content-Type: application/json

{}

###

# ----- Set the Webhook URL to which Treezor events will be sent to
# NOTE: This will require a visitation of an URL that was sent to the Webhook for confirmation
POST {{HookManagementEndpoint}}/settings/hooks
Content-Type: application/json
Authorization: Bearer {{JwtToken}}

{
  "url": "https://staging-treezor-webhook.yochbee.com/webhook"
}

###

# ----- List of Webhooks and their status
GET {{HookManagementEndpoint}}/settings/hooks
Content-Type: application/json
Authorization: Bearer {{JwtToken}}

###

# ----- Delete an existing Webhook URL
DELETE {{HookManagementEndpoint}}/settings/hooks/2be652c3-fc47-4577-9374-102f23f1d46e
Content-Type: application/json
Authorization: Bearer {{JwtToken}}

###
