static_resources:
  listeners:
    - address:
        socket_address:
          address: 0.0.0.0 # to be forwarded to VSCode programmer
          port_value: 8000
      filter_chains:
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                codec_type: AUTO
                stat_prefix: ingress_http
                use_remote_address: true
                request_timeout: 20s
                route_config:
                  name: pixel_routes
                  virtual_hosts:
                    - name: pixel
                      domains:
                        - "*"
                      routes:
                        - match: { prefix: /auth/ }
                          route: { cluster: accounts-service, prefix_rewrite: /auth/, timeout: 900s }
                        - match: { prefix: /account/ }
                          route: { cluster: accounts-service, prefix_rewrite: /account/, timeout: 900s }
                        - match: { prefix: /debug/accounts/ }
                          route: { cluster: accounts-service, prefix_rewrite: /debug-accounts/, timeout: 900s }
                        - match: { prefix: /kyc/ }
                          route: { cluster: kyc-service, prefix_rewrite: /kyc/, timeout: 900s }
                        - match: { prefix: /banking/cards-acquiring/ }
                          route: { cluster: banking-services, prefix_rewrite: /acquiring/, timeout: 900s }
                        - match: { prefix: /banking/beneficiaries/ }
                          route: { cluster: banking-services, prefix_rewrite: /beneficiaries/, timeout: 900s }
                        - match: { prefix: /cards/ }
                          route: { cluster: banking-services, prefix_rewrite: /cards/, timeout: 900s }
                        - match: { prefix: /banking/payouts/ }
                          route: { cluster: banking-services, prefix_rewrite: /payouts/, timeout: 900s }
                        - match: { prefix: /banking/transactions/ }
                          route: { cluster: banking-services, prefix_rewrite: /transactions/, timeout: 900s }
                        - match: { prefix: /banking/operations/ }
                          route: { cluster: banking-services, prefix_rewrite: /operations/, timeout: 900s }
                        - match: { prefix: /banking/wallet/ }
                          route: { cluster: banking-services, prefix_rewrite: /wallet/, timeout: 900s }
                        - match: { prefix: /banking/assets/ }
                          route: { cluster: banking-services, prefix_rewrite: /assets/, timeout: 900s }
                        - match: { prefix: /banking/pdf/ }
                          route: { cluster: banking-services, prefix_rewrite: /pdf/, timeout: 900s }
                        - match: { prefix: /business/ }
                          route: { cluster: business-services, prefix_rewrite: /business/, timeout: 900s }
                        - match: { prefix: /notifications/ }
                          route: { cluster: notifications-service, prefix_rewrite: /notifications/, timeout: 900s } # New route
                          # These only exists in the dev environment
                        - match: { prefix: /banking/html/ }
                          route: { cluster: banking-services, prefix_rewrite: /html/, timeout: 900s }
                        - match: { prefix: /_bus/ }
                          route: { cluster: kyc-service, prefix_rewrite: /_bus/, timeout: 900s }

                http_filters:
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                access_log:
                  - name: envoy.access_loggers.file
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog
                      path: /dev/stdout

  clusters:
    - name: accounts-service
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: accounts-service
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: yochbee-backend
                      port_value: 8080
    - name: kyc-service
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: kyc-service
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: yochbee-backend
                      port_value: 8084
    - name: banking-services
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: banking-services
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: yochbee-backend
                      port_value: 8086
    - name: business-services
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: business-services
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: yochbee-backend
                      port_value: 8088
    - name: notifications-service # New cluster
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: notifications-service
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: yochbee-backend
                      port_value: 8090

admin:
  address:
    socket_address:
      address: ********* # yochbee-backend
      port_value: 8001

layered_runtime:
  layers:
    - name: static_layer_0
      static_layer:
        envoy:
          resource_limits:
            listener:
              default_listener_name:
                connection_limit: 10000
        overload:
          global_downstream_max_connections: 50000
