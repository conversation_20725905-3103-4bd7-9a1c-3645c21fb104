# Webhook dev

## Need to know

- Upon registration, an initial message will be sent from Treezor containing an URL that needs to be visited.
  - I **did not see** this in the documentation, just found out myself during testing
  - The URL is in the `SubscribeURL` field of the JSON
- The Webhook will stay in `PENDING` status until this URL is visited

## Testing using NGrok

```shell
# 1. Run the treezor service locally (it will be on port 8082, same during deployment)
# 2. Execute
ngrok http 8082
```

## Documentation references

- Management of the URLs that <PERSON><PERSON> will call: https://docs.treezor.com/guide/webhooks/subscription-management.html#declare-a-post-route-in-your-backend