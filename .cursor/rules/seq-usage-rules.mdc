---
alwaysApply: false
---

# SEQ Logging System

## 🔐 Authentication & Configuration

### Environment Variables (.env.seq only accessible via bash, not read tools)
- **SEQ_QUERY_API_KEY**: Read-only API key for querying logs
- **SEQ_URL**: SEQ server URL (https://logs.yochbee.com/)

### Loading Environment Variables
```bash
# Load SEQ environment variables from project root
source .env.seq

# Verify variables are loaded
echo "SEQ URL: $SEQ_URL"
echo "API Key loaded: ${SEQ_QUERY_API_KEY:0:8}..."
```

## 📊 Essential SEQ Commands

### 🚨 **Most Reliable Pattern (Use This)**
```bash
# Load environment variables first (it exists, but tools don't see it)
source .env.seq

# Get recent logs and filter client-side (more reliable than server-side filters)
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=50" | \
jq '.[] | select(.Level == "Error") | {
  timestamp: .Timestamp,
  message: .MessageTemplateTokens[0].Text,
  caller: (.Properties[] | select(.Name=="caller") | .Value // "N/A"),
  logger: (.Properties[] | select(.Name=="logger") | .Value // "N/A")
}'
```

### 🔍 **Quick Health Check**
```bash
source .env.seq
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=20" | \
jq -r '.[] | "\(.Timestamp) [\(.Level)] \(.MessageTemplateTokens[0].Text)"' | head -10
```

### 📋 **Property Analysis**
```bash
# List available properties in recent logs
source .env.seq
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=10" | \
jq -r '.[] | "\(.Timestamp) [\(.Level)] \(.MessageTemplateTokens[0].Text)\nProperties: \(.Properties | map(.Name) | join(", "))\n---"'
```

## 🚨 Critical Troubleshooting

### **❌ Known Issues & Solutions**

**Problem**: Complex server-side filters fail with "Malformed input"
```bash
# ❌ FAILS: curl "$SEQ_URL/api/events?filter=@Level='Error' and @Timestamp>Now()-30m"
# ✅ WORKS: Use client-side filtering
curl "$SEQ_URL/api/events?count=50" | jq '.[] | select(.Level == "Error")'
```

**Problem**: Server-side filters return empty results even when data exists
```bash
# ❌ UNRELIABLE: curl "$SEQ_URL/api/events?filter=@Level='Error'"
# ❌ UNRELIABLE: curl "$SEQ_URL/api/events?filter=sddr"
# ✅ RELIABLE: curl "$SEQ_URL/api/events?count=100" | jq '.[] | select(.Level == "Error")'
```

**Problem**: Search terms may be in response payloads, not just message text
```bash
# ❌ LIMITED: jq '.[] | select(.MessageTemplateTokens[0].Text | test("sddr"; "i"))'
# ✅ COMPREHENSIVE: Save to file and use grep for full-text search
curl "$SEQ_URL/api/events?count=500&from=2025-07-01T00:00:00Z" > /tmp/logs.json
cat /tmp/logs.json | jq -r '.[] | "\(.Timestamp) [\(.Level)] \(.MessageTemplateTokens[0].Text) \(.Properties // [] | map(select(.Name and .Value)) | map("\(.Name)=\(.Value)") | join(" "))"' | grep -i "search_term"
```

**Problem**: jq syntax errors with nested quotes
```bash
# ❌ FAILS: jq -r '.[] | "\(.Properties[] | select(.Name==\"caller\"))"'
# ✅ WORKS: jq -r '.[] | "\(.Properties[] | select(.Name=="caller") | .Value)"'
```

### **⚡ Performance Best Practices**
- Start with `count=20-50` for exploration
- Use `count=100` for detailed analysis
- Use `count=500-1000` with time ranges for specific term searches
- Use client-side filtering instead of server-side filters
- **For specific term searches**: Use broader time ranges (`from=2025-07-01T00:00:00Z`) + grep

## 📋 Common Log Properties

### **Standard Properties**
- `caller` - Source code location (e.g., "treezor/treezorapi.go:140")
- `logger` - Logger name (e.g., "Treezor", "Banking", "Accounts")
- `service` - Microservice name (accounts, banking, kyc, etc.)
- `treezorUserId` - Treezor user identifier
- `ActivityId` - Activity correlation ID

### **HTTP Request Properties**
- `request` - HTTP request path
- `status` - HTTP status code
- `time` - Request duration
- `request_id` - Unique request identifier

## 🎯 Quick Reference Commands

### **Get Recent Errors**
```bash
source .env.seq
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=50" | \
jq '.[] | select(.Level == "Error") | "\(.Timestamp) - \(.MessageTemplateTokens[0].Text)"'
```

### **Service-Specific Analysis**
```bash
# Filter by service property client-side
source .env.seq
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=100" | \
jq '.[] | select(.Properties[] | select(.Name=="service" and .Value=="banking"))'
```

### **File-Based Analysis for Complex Tasks**
```bash
# Save logs for multiple operations
source .env.seq
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=100" > /tmp/recent_logs.json
cat /tmp/recent_logs.json | jq '.[] | select(.Level == "Error")' > /tmp/errors.json
# Clean up: rm /tmp/recent_logs.json /tmp/errors.json
```

### **Full-Text Search Pattern (For Specific Terms)**
```bash
# When searching for specific terms that might be in response payloads
source .env.seq
curl -H "X-Seq-ApiKey: $SEQ_QUERY_API_KEY" "$SEQ_URL/api/events?count=500&from=2025-07-01T00:00:00Z" > /tmp/search_logs.json
cat /tmp/search_logs.json | jq -r '.[] | "\(.Timestamp) [\(.Level)] \(.MessageTemplateTokens[0].Text) \(.Properties // [] | map(select(.Name and .Value)) | map("\(.Name)=\(.Value)") | join(" "))"' | grep -i "sddr"
# Clean up: rm /tmp/search_logs.json
```

## 🤖 AI Agent Guidelines

### **When to Use SEQ:**
- ✅ User asks about errors or system issues
- ✅ Debugging production problems
- ✅ Monitoring system health
- ✅ Investigating performance issues

### **Workflow:**
1. **Load environment variables** with `source .env.seq`
2. **For recent exploration**: Start with basic queries (`count=20-50`)
3. **For specific searches**: Use time ranges + higher counts (500-1000) + full-text grep
4. **Use client-side filtering** (more reliable than server-side)
5. **Remember**: Search terms may be in response payloads, not just message text
6. **Combine with codebase search** for complete analysis

### **Security:**
- ✅ API key is READ-ONLY - safe for AI agents
- ✅ Never expose API key in responses
- ⚠️ Do not commit API keys to version control

## 💡 Key Takeaways

- **ALWAYS** load environment variables first: `source .env.seq`
- **PREFER** client-side jq filtering over server-side filters
- **START** with simple queries, build complexity gradually
- **FOCUS** on actionable insights from error logs
- **COMBINE** log analysis with codebase search for solutions

- Test environment loading before running queries
- Combine log analysis with codebase search for complete solutions
