
# MongoDB Database Access & Debugging

## 🚨 **CRITICAL: AI Agent Database Access**

### **⚡ Fast Access Checklist**
1. **Find environment file**: `find . -name "*.env*" -type f 2>/dev/null | grep mongo`
2. **Load environment**: `source .env.mongodb`
3. **Test connection**: `mongosh "$PROD_MONGODB_CONNECTION_STRING" --eval "db.runCommand({ping: 1})"`
4. **Switch database**: `db = db.getSiblingDB('yochbee')`

### **❌ Common Mistakes to AVOID**
- **DON'T** try `env | grep -i mongo` first (env files not loaded)
- **DON'T** try to read `.env.mongodb` with `read_file` tool (use `source`)
- **DON'T** use `db.listCollectionNames()` (use `db.getCollectionNames()`)
- **DON'T** use `use yochbee;` in `--eval` (causes syntax errors)

## 🎯 Quick Reference

### **Standard Collections**
- `accounts`, `treezor-payins`, `treezor-payouts`, `banking-transactions`, `treezor-users`

### **Critical Fields Needing Unique Indexes**
- `treezorPayinId`, `treezorPayoutId`, `email`, `mobile`

### **Duplicate Investigation Pattern**
1. Check error logs for problematic field
2. Find duplicates with aggregation
3. Clean duplicates (keep most recent)
4. Add unique index to prevent future duplicates

## 🤖 AI Agent Guidelines

### **When to Use MongoDB**
- Data inconsistencies, duplicate errors, production debugging

### **Safety Rules**
- Always use `.limit()` to prevent resource exhaustion
- Never expose connection strings
- Avoid write operations during investigations
- Check for duplicates in critical collections first
