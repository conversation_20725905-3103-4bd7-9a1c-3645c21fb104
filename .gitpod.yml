image:
  file: .gitpod.Dockerfile

tasks:
  - name: Setup & Services
    init: |
      pushd src
      go mod download
      go install golang.org/x/tools/cmd/stringer@latest
      popd

    command: |
      # Longer git credential cache
      git config --global credential.helper 'cache --timeout=36000'

      # Add the final Git remote
      git remote add azure 'https://yochbeeorganisation.visualstudio.com/Yochbee/_git/BackendGolang'

      # Azure credentials
      if [ -n "${ZED_YOCHBEE_BACKEND_AZURE_STATE}" ]; then
        echo "$ZED_YOCHBEE_BACKEND_AZURE_STATE" | base64 -d - | tar xjf - -P
        echo "$ZED_YOCHBEE_BACKEND_CONTAINER_STATE" | base64 -d - | tar xjf - -P
        echo "Restored previous Azure & Kubernetes & Docker credentials"
      else
        az login --use-device-code
        az account set --subscription 7e9d31aa-c867-4320-8027-5d83be33d4aa
        az aks get-credentials --resource-group Staging --name Yo<PERSON><PERSON>StagingKubernetes
        az aks get-credentials --resource-group Production --name YochbeeProdK8s

        kubelogin convert-kubeconfig -l azurecli
        az acr login --name yochbee

        # Store the credentials for future re-use
        gp env ZED_YOCHBEE_BACKEND_AZURE_STATE="$(tar cjf - ~/.azure -P | base64 -)"
        gp env ZED_YOCHBEE_BACKEND_CONTAINER_STATE="$(tar cjf - ~/.kube ~/.docker -P | base64 -)"
        echo "Saved Azure & Kubernetes & Docker credentials for future reuse"
      fi

      # RabbitMQ
      pushd deployments/dev
      docker compose --file gitpod.docker-compose.yaml --project-name yochbee-dev up -d
      popd

      # Virtual hosts that maps to *********
      sudo bash -c "echo '********* mongodb-srv.databases.svc.cluster.local' >> /etc/hosts"
      sudo bash -c "echo '********* bus-service' >> /etc/hosts"
      sudo bash -c "echo '********* seq-logging-service' >> /etc/hosts"
      sudo bash -c "echo '********* redis-locks' >> /etc/hosts"
      sudo bash -c "echo '********* yochbee-backend' >> /etc/hosts"

      gp sync-done setup
      exit

  - name: Mobile (envoy proxy)
    command: |
      sudo bash -c "echo '********* accounts-service' >> /etc/hosts"
      sudo bash -c "echo '********* kyc-service' >> /etc/hosts"
      sudo bash -c "echo '********* banking-services' >> /etc/hosts"
      cd deployments/dev/envoy-gateways
      sudo envoy --base-id 1 -c gitpod.pixel.yaml # /dev/stdout error unless run as root

  # By default, the STAGING database will be used during debugging
  # deployments/dev/yochbee/gitpod.docker-compose.yaml can be used to start an isolated MongoDB 5 for debugging/testing purposes (not started by default)
  - name: "FW: ETCD (staging)"
    command: |
      gp sync-await setup
      while true; do kubectl port-forward services/etcd-service 2379:2379 --address 0.0.0.0; sleep 2; done

  - name: "FW: RabbitMQ WebUI (staging)"
    command: |
      gp sync-await setup
      while true; do kubectl port-forward services/bus-service 25672:15672 --address 0.0.0.0; sleep 2; done

  - name: Tailscale daemon
    command: |
      if [ -n "${TAILSCALE_STATE_YOCHBEE_BACKEND}" ]; then
        # restore the tailscale state from gitpod user's env vars
        sudo mkdir -p /var/lib/tailscale
        echo "${TAILSCALE_STATE_YOCHBEE_BACKEND}" | sudo tee /var/lib/tailscale/tailscaled.state > /dev/null
      fi
      sudo tailscaled
  - name: Tailscale
    command: |
      if [ -n "${TAILSCALE_STATE_YOCHBEE_BACKEND}" ]; then
        sudo -E tailscale up
      else
        sudo -E tailscale up --hostname "gitpod-${GITPOD_GIT_USER_NAME// /-}-$(echo ${GITPOD_WORKSPACE_CONTEXT} | jq -r .repository.name)"
        # store the tailscale state into gitpod user
        gp env TAILSCALE_STATE_YOCHBEE_BACKEND="$(sudo cat /var/lib/tailscale/tailscaled.state)"
      fi
      exit

  - name: Warm-up & Reminder
    command: |
      # Warm-up the compiler
      pushd src/cmd/microservices/kyc
      go build -o /dev/null .
      popd

      sudo apt-get update

      clear
      echo
      echo
      echo
      echo "Please install the Static Checking tool which is at the bottom of VSCode"
      echo
      echo "Browser: Disable the Back button"

ports:
  - name: Envoy Mobile Gateway
    port: 8000
    visibility: public

  - name: Treezor WS
    port: 8082
    visibility: public

  - name: STAGING RabbitMQ UI
    port: 25672

  - name: DEV RabbitMQ UI
    port: 15672

vscode:
  extensions:
    - golang.go

    - akosyakov.gitpod-monitor
    - mhutchie.git-graph
    - thinker.copy-as-snippet
    - pkief.material-icon-theme

    - github.copilot
#
# See https://www.gitpod.io/docs/references/gitpod-yml for reference
