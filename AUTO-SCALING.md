# Yochbee auto-scaling

The Yochbee platform is resting on Kubernetes, a well known and widely accepted orchestration platform well suited 
for microservices. Amongst the inherited benefits of using Kubernetes is the fact that it has a built-in 
auto-scaling features.

There are 2 parts in the auto-scaling, first is on Microsoft Azure's side, the second is the deployment of the pods 
to take into account the resource usage.

## Azure

The master and agent nodes are hosted on Microsoft Azure, the master is obviously managed by Azure. Thus, the 
**auto-scaling must be enabled on Azure** for nodes to automatically be scaled up or down, based on the load.

NOTE: The Azure interface for creating a new Kubernetes cluster is locked to a predefined type of virtual machines. 
If you wish to start a cluster with an even smaller virtual machine type, you'll need to use the Azure CLI tool.

## Kubernetes

The second part is Kubernetes itself. We use the built-in `HorizontalPodAutoscaler` for that. Depending on the 
version being used, it can use different parameters to gauge if it should scale at any given moment, and whether to 
scale up or down.

In the basic microservice example, Treezor has an API that is called by Pixel, in its function, Treezor will do a very
heavy task that will occupy the CPU for 2 seconds. As a consequence, the CPU usage will quickly go up when the 
service is highly solicited. The  `HorizontalPodAutoscaler` (aka. HPA) will see the CPU usage rising, and will 
create additional pods in order to direct the loads to. When the load is reduced, HPA will see the trend and will 
automatically remove the pods. If the resource of the current node is no longer sufficient (whether it be memory, or 
number of pods, or CPU), and new node will automatically be provisioned for new pods.

Here is the manifest for the HPA that is used on staging. It will try to have about 50 percent CPU usage maximum for 
each pods, and it will have a minimum of 1 pod and a maximum of 200:

```yaml
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: treezor
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: treezor
  minReplicas: 1
  maxReplicas: 200
  targetCPUUtilizationPercentage: 50
```

In a real-world application, it may not be economical to have a target CPU utilisation at just 50%. However, it is 
always wise to leave some headroom and not fully saturate resources, whether it is CPU, memory, network, etc...

For extensive information on different ways to influence the auto-scaling feature of Kubernetes, please see the 
official documentation: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/

As the complexity of any given application and its number of user rises, it may be necessary to look into custom 
scaling where you can programmatically tell Kubernetes about the parameters of your application that will be used 
for the auto-scaling.
