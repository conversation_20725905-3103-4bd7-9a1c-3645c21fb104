package main

import (
	"errors"
	"time"
	"yochbee/_base"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	json2 "yochbee/_base/json"
	"yochbee/_base/web"
	"yochbee/common/accounts"
	"yochbee/common/notifications"

	"github.com/labstack/echo/v4"
)

func registerRestServices(busrpc bus2.RpcClient, e *echo.Echo) {
	// Add busrpc to the context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("busrpc", busrpc)
			return next(c)
		}
	})

	e.POST("/business/contact/:accountId", func(c echo.Context) error {
		request := struct {
			Subject string `json:"subject" validate:"required,min=1,max=100"`
			Message string `json:"message" validate:"required,min=1,max=10000"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			return err // HTTP 500
		}

		// Retrieve the account
		account := c.Get("account").(*accounts.Account)

		// Send an email using the `notifications` microservice
		result, err := bus2.CallMethodMU[notifications.SendEmailFromResponse](busrpc, notifications.Method_SendEmailFrom, &notifications.SendEmailFromRequest{
			ToEmailAddresses: []string{"<EMAIL>"}, // FIXME: Use a different email address
			FromEmailAddress: account.Email,
			Subject:          request.Subject,
			ContentType:      "text/plain",
			Body: "Message d'un utilisateur de l'application :\n\n-----------------\n" +
				"Nom & prénom : " + (account.FirstName + " " + account.LastName) + "\n" +
				"Email : " + account.Email + "\n" +
				"Téléphone : " + account.PhoneNumber + "\n" +
				"-----------------\n\n" +
				request.Message,
		}, 10*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		if !result.Ok {
			return errors.New("error sending email") // HTTP 500
		}
		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)
}

func main() {
	config.Microservice = "business"
	web.ServerEndpoint = ":8088"

	app := _base.SetupApp("Business",
		registerRestServices,
	)

	app.Run()
}
