package main

import (
	"encoding/json"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func registerBusMethods(rpc bus2.RpcClient, documentsColl dbnosql.DataCollection, progressColl dbnosql.DataCollection) error {

	// ---------------
	// ╔═╗┬─┐┌─┐┌─┐┌┬┐┌─┐  ╔═╗╦═╗╔═╗╔═╗╦═╗╔═╗╔═╗╔═╗  ┌─┐┌─┐┬─┐  ╔═╗┌─┐┌─┐┌─┐┬ ┬┌┐┌┌┬┐
	// ║  ├┬┘├┤ ├─┤ │ ├┤   ╠═╝╠╦╝║ ║║ ╦╠╦╝║╣ ╚═╗╚═╗  ├┤ │ │├┬┘  ╠═╣│  │  │ ││ ││││ │
	// ╚═╝┴└─└─┘┴ ┴ ┴ └─┘  ╩  ╩╚═╚═╝╚═╝╩╚═╚═╝╚═╝╚═╝  └  └─┘┴└─  ╩ ╩└─┘└─┘└─┘└─┘┘└┘ ┴
	if err := rpc.Register(kyc.Method_CreateProgressForAccount, func(payload []byte) ([]byte, error) {
		L := logging.L.Named("CreateProgressForAccount")
		L.Info("Started ...")
		defer L.Info("Finished")

		request := kyc.CreateProgressForAccountRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			<PERSON>.<PERSON>r("Failed to unmarshal request", zap.Error(err))
			return nil, err
		}

		// Verify that there isn't one yet
		progress, err := getProgressByAccount(progressColl, request.Account.ID)
		if err != nil {
			L.Error("Failed to verify existing progress", zap.Error(err))
			return nil, err
		}
		if progress == nil {
			progress = &kyc.Progress{
				ID:               progressColl.GenerateUniqueId(),
				AccountId:        request.Account.ID,
				TreezorUserId:    request.Account.TreezorUserId,
				TreezorKYCLevel:  kyc.KL_None,
				TreezorKYCReview: kyc.KR_None,
				CreatedAt:        time.Now(),
			}

			// Insert a new one
			if _, err := progressColl.Insert(progress); err != nil {
				L.Error("Failed to insert progress", zap.Error(err), zap.Any("progress", progress))
				return nil, err
			}
			L.Info("New Progress inserted", zap.Any("progress", progress), zap.Any("account", request.Account))
		} else {
			L.Info("Skipping creation of Progress: it already exists", zap.Any("progress", progress))
		}

		response := kyc.CreateProgressForAccountResponse{
			Progress: progress,
		}
		return json.Marshal(response)
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╦ ╦┌─┐┌─┐┬ ┬╔╗ ┌─┐┌─┐  ╦═╗┌─┐┬  ┬┬┌─┐┬ ┬  ╔═╗┌─┐┌┬┐┌─┐┬  ┌─┐┌┬┐┌─┐┌┬┐
	// ╚╦╝│ ││  ├─┤╠╩╗├┤ ├┤   ╠╦╝├┤ └┐┌┘│├┤ │││  ║  │ ││││├─┘│  ├┤  │ ├┤  ││
	//  ╩ └─┘└─┘┴ ┴╚═╝└─┘└─┘  ╩╚═└─┘ └┘ ┴└─┘└┴┘  ╚═╝└─┘┴ ┴┴  ┴─┘└─┘ ┴ └─┘─┴┘
	if err := rpc.Register(kyc.Method_YochbeeTeamReviewCompleted, func(payload []byte) ([]byte, error) {
		request := kyc.YochbeeTeamReviewCompletedRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		// Build a list of validated documents
		documents, err := getDocumentsForAccount(documentsColl, request.AccountId)
		if err != nil {
			return nil, err
		}
		validatedDocuments := make([]*kyc.Document, 0, len(documents))
		for _, d := range documents {
			invalidated := false
			for _, id := range request.RejectedDocumentIDs {
				if id == d.ID {
					invalidated = true
					if err := updateDocumentStatus(documentsColl, d, kyc.DSY_Rejected); err != nil {
						return nil, err
					}
					break
				}
			}
			if invalidated {
				continue
			}
			for _, id := range request.ValidatedDocumentIDs {
				if id == d.ID {
					validatedDocuments = append(validatedDocuments, d)
					break // next document
				}
			}
		}

		// Get the progress object
		progress, err := getProgressByAccount(progressColl, request.AccountId)
		if err != nil {
			return nil, err
		}

		if request.OKForTreezor {
			// Upload to Treezor and Mark as sent to Treezor
			for _, document := range validatedDocuments {
				if document.Type == kyc.DT_TaxStatement || document.Type == kyc.DT_TaxExemptionStatement {
					// TODO: await confirmation if this needs to be covered
					// Currently, we're following: https://treezor.zendesk.com/hc/fr/articles/************-Users-KYC-Review
				}

				// ... upload
				rb, err := rpc.CallM(treezor.Method_UploadDocument, &treezor.UploadDocumentRequest{
					ResidenceId: 0, // TODO: await confirmation if this needs to be covered
					Document:    document,
				}, 10*time.Second)
				if err != nil {
					return nil, err
				}
				uploadResult := treezor.UploadDocumentResponse{}
				if err = json.Unmarshal(rb, &uploadResult); err != nil {
					return nil, err
				}

				// ... mark, with the Treezor Document ID
				newStatus := kyc.DSY_SentToTreezor
				if err := syncDocumentWithIncomingTreezorStatus(documentsColl, document, uploadResult.TreezorDocument, newStatus); err != nil {
					return nil, err
				}
			}

			// Uploads successful, now: Request a review
			_, err = rpc.CallM(treezor.Method_StartKYCReview, &treezor.StartKYCReviewRequest{
				Progress: progress,
			}, 10*time.Second)
			if err != nil {
				return nil, err
			}
			// After this, a Webhook will be generated, and it will be intercepted
			// TODO: Both `accounts` and `kyc` microservices will react on that event.
		} else {
			// Just mark the validated documents
			for _, d := range validatedDocuments {
				if err := updateDocumentStatus(documentsColl, d, kyc.DSY_Validated); err != nil {
					return nil, err
				}
			}
		}

		// This user is no longer under review by Yochbee. She'll need to request a Review again.
		progress.IsReviewRequested = false
		now := time.Now()
		progress.UpdatedAt = &now
		progress.TreezorKYCReviewComment = request.Remarks
		if err = progressColl.UpdateById(progress.ID, progress); err != nil {
			return nil, err
		}

		return bus2.OK()
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╔╦╗┌─┐┌─┐┬  ┌─┐┬─┐┌─┐┌┬┐┬┬  ┬┌─┐  ┬─┐┌─┐┌─┐┌┬┐┬┌┐┌┌─┐┌─┐┌─┐
	// ║ ║├─┘ ││├─┤ │ ├┤    ║║├┤ │  │  ├─┤├┬┘├─┤ │ │└┐┌┘├┤   ├┬┘├┤ ├─┤ ││││││├┤ └─┐└─┐
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ═╩╝└─┘└─┘┴─┘┴ ┴┴└─┴ ┴ ┴ ┴ └┘ └─┘  ┴└─└─┘┴ ┴─┴┘┴┘└┘└─┘└─┘└─┘
	if err := rpc.Register(kyc.Method_UpdateDeclarativeInfoReadiness, func(payload []byte) ([]byte, error) {
		L := logging.L.Named("UpdateDeclarativeInfoReadiness")
		L.Info("Starting ...", zap.ByteString("payload", payload))
		defer L.Info("Done")

		request := kyc.UpdateDeclarativeInfoReadinessRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			L.Error("Failed to unmarshal request", zap.Error(err))
			return nil, err
		}

		// Get the progress object
		progress, err := getProgressByAccount(progressColl, request.UpdatedAccount.ID)
		if err != nil {
			L.Error("Failed to get progress", zap.Error(err), zap.String("accountId", request.UpdatedAccount.ID))
			return nil, err
		}

		// Update
		if progress.HasRequiredDeclarativeKYCInfo != request.IsReady {
			progress.HasRequiredDeclarativeKYCInfo = request.IsReady
			now := time.Now()
			progress.UpdatedAt = &now
			if err = progressColl.UpdateById(progress.ID, progress); err != nil {
				L.Error("Failed to update progress", zap.Error(err), zap.String("accountId", request.UpdatedAccount.ID), zap.String("progressId", progress.ID))
				return nil, err
			}
			L.Info("Updated progress", zap.String("accountId", request.UpdatedAccount.ID), zap.Bool("isReady", request.IsReady), zap.Any("progress", progress))
		}

		return bus2.OK()
	}, true); err != nil {
		return err
	}

	//
	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╔═╗┬─┐┌─┐┌─┐┬─┐┌─┐┌─┐┌─┐
	// ║ ║├─┘ ││├─┤ │ ├┤   ╠═╝├┬┘│ ││ ┬├┬┘├┤ └─┐└─┐
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ╩  ┴└─└─┘└─┘┴└─└─┘└─┘└─┘
	//
	if err := bus2.RegisterUM(kyc.Method_UpdateProgressForAccount, rpc, func(i *kyc.UpdateProgressForAccountRequest) (*kyc.UpdateProgressForAccountResponse, error) {
		L := logging.L.Named("UpdateProgressForAccount")
		L.Info("Started ...")
		defer L.Info("Finished")

		progress, err := getProgressByAccount(progressColl, i.Account.ID)
		if err != nil {
			L.Error("Failed on getProgressByAccount", zap.Error(err))
			return nil, err
		}

		// Simply add the missing TreezorUserId
		if progress.TreezorUserId == 0 {
			progress.TreezorUserId = i.Account.TreezorUserId
			if err = progressColl.UpdateById(progress.ID, progress); err != nil {
				L.Error("Failed on progressColl.UpdateById", zap.Error(err))
				return nil, err
			}
			L.Info("Updated progress.TreezorUserId", zap.Any("progress", progress))
		}

		return &kyc.UpdateProgressForAccountResponse{Progress: progress}, nil
	}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌─┐┌┬┐  ╔═╗╦═╗╔═╗╔═╗╦═╗╔═╗╔═╗╔═╗  ┌┐ ┬ ┬  ╔═╗┌─┐┌─┐┌─┐┬ ┬┌┐┌┌┬┐╦╔╦╗
	// ║ ╦├┤  │   ╠═╝╠╦╝║ ║║ ╦╠╦╝║╣ ╚═╗╚═╗  ├┴┐└┬┘  ╠═╣│  │  │ ││ ││││ │ ║ ║║
	// ╚═╝└─┘ ┴   ╩  ╩╚═╚═╝╚═╝╩╚═╚═╝╚═╝╚═╝  └─┘ ┴   ╩ ╩└─┘└─┘└─┘└─┘┘└┘ ┴ ╩═╩╝
	//
	if err := bus2.RegisterUM(kyc.Method_GetProgressByAccountId, rpc, func(i *kyc.GetProgressByAccountIdRequest) (*kyc.GetProgressByAccountIdResponse, error) {
		L := logging.L.Named("GetProgressByAccountId")
		L.Info("Started ...")
		defer L.Info("Finished")

		progress, err := getProgressByAccount(progressColl, i.Account.ID)
		if err != nil {
			L.Error("Failed on getProgressByAccount", zap.Error(err))
			return nil, err
		}

		return &kyc.GetProgressByAccountIdResponse{Progress: progress}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
