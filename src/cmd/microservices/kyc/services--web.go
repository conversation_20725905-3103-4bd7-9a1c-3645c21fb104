package main

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/lock"
	"yochbee/_base/random"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/azure"
	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

const (
	maxKycDocumentFileSize = 10 * 1024 * 1024
	azureBlobContainerName = "kyc-documents"
)

func registerRESTAPIs(dlocker *lock.DistributedLocker, busrpc bus2.RpcClient, signal bus2.Client, e *echo.Echo, storage *azure.BlobService,
	documentsColl dbnosql.DataCollection,
	progressColl *kyc.ProgressCollection) error {
	if _, err := storage.EnsureContainer(context.Background(), azureBlobContainerName); err != nil {
		return err
	}

	genericRequirements1 := func(c echo.Context) (accountId string, account *accounts.Account, progress *kyc.Progress, err error, abort bool) {
		// Verify that it's really the actual user with the right JWT token
		accountId = strings.TrimSpace(c.Param("accountId"))
		if !accounts.IsRequestReallyFromUser(c, accountId) {
			return accountId, nil, nil, common.ReturnInvalidJWTTokenResponse(c), true
		}

		// Get the associated account
		account, err = accounts.Get(busrpc, accountId)
		if err != nil {
			return accountId, nil, nil, err, true // HTTP 500
		}
		if account == nil {

			return accountId, nil, nil, web.Error(c, common.ErrorCodeAccountNotFound, "Account not found"), true
		}

		// Get the progress for this user
		progress, err = getProgressByAccount(progressColl, accountId)
		if err != nil {
			return accountId, nil, nil, err, true // HTTP 500
		}
		if progress == nil {
			return accountId, nil, nil, web.Error(c, common.ErrorCodeAssociatedAccountDataNotFound, "Missing KYC progress"), true
		}
		return
	}

	// --------------
	e.POST("/kyc/documents/upload/:accountId", func(c echo.Context) error {
		if DoKYCLiveness {
			return common.ReturnLivenessMethodOnly(c)
		}

		// Verify parameters
		docTypeStr := strings.TrimSpace(c.QueryParam("type"))
		if docTypeStr == "" {
			return common.ReturnInvalidAPICallResponse(c)
		}
		docTypeI64, err := strconv.ParseInt(docTypeStr, 10, 8)
		if err != nil {
			return common.ReturnInvalidAPICallResponse(c)
		}
		if !kyc.IsSupportedDocumentType(uint8(docTypeI64)) {
			return common.ReturnInvalidAPICallResponse(c)
		}
		docType := kyc.DocumentType(docTypeI64)

		// Verify that it's really the actual user with the right JWT token
		accountId := strings.TrimSpace(c.Param("accountId"))
		if !accounts.IsRequestReallyFromUser(c, accountId) {
			return common.ReturnInvalidJWTTokenResponse(c)
		}

		// Get the associated account
		account, err := accounts.Get(busrpc, accountId)
		if err != nil {
			return err // HTTP 500
		}
		if account == nil {
			return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
		}

		// Analyze the uploaded file
		fh, err := c.FormFile("document")
		if err != nil {
			return err // HTTP 500
		}
		if fh.Size > maxKycDocumentFileSize {
			return web.Error(c, common.ErrorCodeUploadFileSizeUnacceptable, "Document size too big, the limit is 10MB")
		}
		if fh.Size <= 512 {
			return web.Error(c, common.ErrorCodeUploadFileSizeUnacceptable, "Document size too small")
		}
		// This is a buffered stream from Cloudflare, meaning that only complete request will be sent to us
		// in a very short time, very quickly. So, we can safely buffer it all into memory and clear it after
		// upload to Azure, also very quickly. Documents are also not going to be 10MB every-time.
		fc, err := fh.Open()
		if err != nil {
			return err // HTTP 500
		}
		defer fc.Close()
		content, err := io.ReadAll(fc)
		if err != nil {
			return err // HTTP 500
		}
		contentType := http.DetectContentType(content)
		fileext := "jpg"
		switch contentType {
		case "image/png":
			fileext = "png"
		case "image/jpg":
			fileext = "jpg"
		case "image/tiff":
			fileext = "tiff"
		case "image/svg+xml":
			fileext = "svg"
		case "application/pdf":
			fileext = "pdf"
		default:
			return web.Error(c, common.ErrorCodeUploadFileInvalidType, "Only Image (JPEG, PNG, TIFF) and PDF files are supported")
		}

		// Upload the file to Azure Blob
		filename := fmt.Sprintf("%s.%s", random.Strings(128), fileext)
		fu, err := storage.UploadInContainer(context.Background(), azureBlobContainerName, filename, contentType, bytes.NewReader(content))
		if err != nil {
			return err // HTTP 500
		}

		// Insert the new document
		doc := &kyc.Document{
			ID:                documentsColl.GenerateUniqueId(),
			AccountId:         accountId,
			TreezorUserId:     account.TreezorUserId,
			TreezorDocumentId: -1,
			TreezorStatusCode: "600001", // pending
			Type:              docType,
			TypeText:          docType.String(),
			Filename:          filename,
			OriginalFilename:  fh.Filename,
			Size:              fh.Size,
			Status:            kyc.DSY_Uploaded,
			StatusText:        kyc.DSY_Uploaded.String(),
			SecretUrl:         fu.String(),
			CreatedAt:         time.Now(),
		}
		if _, err = documentsColl.Insert(doc); err != nil {
			return err // HTTP 500
		}

		raw, err := doc.ToMobileJSON()
		if err != nil {
			return err // HTTP 500
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"document": json.RawMessage(raw),
		})
	}, accounts.MiddlewareRequireAccountId)

	e.GET("/kyc/review/progress/:accountId", func(c echo.Context) error {
		accountId, _, progress, err, abort := genericRequirements1(c)
		if err != nil || abort {
			return err // error that's ready for the web
		}

		var status *kyc.ComputedStatus
		if DoKYCLiveness {
			status, err = progress.ComputeProgressStatusWithLiveness()
			if err != nil {
				return err // HTTP 500
			}
		} else {
			// Compute the status fields
			documents, err := getDocumentsForAccount(documentsColl, accountId)
			if err != nil {
				return err // HTTP 500
			}
			status, err = progress.ComputeProgressStatus(documents)
			if err != nil {
				return err // HTTP 500
			}
		}

		return c.JSON(http.StatusOK, status)
	}, accounts.MiddlewareRequireAccountId)

	e.POST("/kyc/review/request-start/:accountId", func(c echo.Context) error {
		L := logging.L.Named("kyc-review-request-start").With(zap.String("accountId", c.Param("accountId")))
		L.Info("Starting request for KYC review to start on Treezor", zap.String("accountId", c.Param("accountId")))
		defer L.Info("Done", zap.String("accountId", c.Param("accountId")))

		// Lock the progress by the account
		unlockFunc, err := progressColl.GetDLockMutexByAccountId(dlocker, c.Param("accountId"))
		if err != nil {
			return err
		}
		defer unlockFunc()

		_, account, progress, err, abort := genericRequirements1(c)
		if err != nil || abort {
			return err // error that's ready for the web
		}

		// Certain users can no longer start KYC
		if progress.TreezorKYCLevel == kyc.KL_Refused && progress.TreezorKYCReview == kyc.KR_Refused {
			return web.Error(c, common.ErrorCodeKYCCannotStartPermanently, "KYC has been refused permanently")
		}

		if err := requestReviewForAccount(documentsColl, busrpc, signal, account, progress); err != nil {
			if errors.Is(err, ErrCannotStartReview) {
				return web.Error(c, common.ErrorCodeKYCCannotStartReview, "Cannot start KYC review")
			}
			return err // HTTP 500
		}

		if err = progressColl.UpdateById(progress.ID, progress); err != nil {
			return err // HTT 500
		}
		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId)

	e.POST("/kyc/liveness/start/:accountId", func(c echo.Context) error {
		L := logging.L.Named("kyc-liveness-start")
		L.Info("Starting KYC liveness request ...", zap.String("accountId", c.Param("accountId")))
		defer L.Info("Finished KYC liveness request")

		// Lock the progress by the account
		unlockFunc, err := progressColl.GetDLockMutexByAccountId(dlocker, c.Param("accountId"))
		if err != nil {
			return err
		}
		defer unlockFunc()

		_, account, progress, err, abort := genericRequirements1(c)
		if err != nil || abort {
			return err // error that's ready for the web
		}
		L = L.With(zap.Any("account", account), zap.Any("progress", progress))
		if account.TreezorUserId == 0 {
			L.Warn("Cannot start KYC liveness without a Payment Provider user", zap.String("accountId", c.Param("accountId")))
			return web.Error(c, common.ErrorCodeKYCCannotStartReview, "Cannot start KYC liveness without a Payment Provider user")
		}

		// Certain users can no longer start KYC
		if progress.TreezorKYCLevel == kyc.KL_Refused {
			L.Warn("KYC has been refused permanently", zap.String("accountId", c.Param("accountId")))
			return web.Error(c, common.ErrorCodeKYCCannotStartPermanently, "KYC has been refused permanently")
		}

		// Require declarative information
		if !progress.HasRequiredDeclarativeKYCInfo {
			L.Warn("KYC cannot start unless all required information is provided", zap.String("accountId", c.Param("accountId")))
			return web.Error(c, common.ErrorCodeKYCCannotStartReview, "KYC cannot start unless all required information is provided")
		}

		// Ask the `treezor` microservice to start the KYC liveness for this user.
		// A URL will be returned, and the Mobile user will need to open it in a browser.
		result, err := bus2.CallMethodMU[treezor.StartKYCLivenessResponse](busrpc, treezor.Method_StartKYCLiveness, &treezor.StartKYCLivenessRequest{
			Account: account,
		}, 10*time.Second)
		if err != nil {
			L.Error("Error calling treezor.StartKYCLiveness", zap.Error(err))
			return err // HTTP 500
		}

		// Get the Treezor User object
		result2, err := bus2.CallMethodMU[treezor.GetTreezorUserResponse](busrpc, treezor.Method_GetTreezorUser, &treezor.GetTreezorUserRequest{
			TreezorUserId: account.TreezorUserId,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}
		if result2.User == nil {
			L.Error("Payment Provider user not found", zap.String("accountId", c.Param("accountId")))
			return web.Error(c, common.ErrorCodeTreezorUserNotFound, "Payment Provider user not found")
		}

		// Update the progress of this User
		progress.TreezorUserId = account.TreezorUserId
		// We don't update the KYC levels from the Treezor User, because it might not reflect the data from user.kycreview.
		// This is a bug from Treezor, and we only have to cope with it.
		progress.TreezorKYCLivenessId = result.LivenessLink.ID
		progress.TreezorKYCLivenessUrl = result.LivenessLink.IdentificationUrl
		progress.IsReviewRequested = true
		now := time.Now()
		progress.UpdatedAt = &now
		if err = progressColl.UpdateById(progress.ID, progress); err != nil {
			return err // HTT 500
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"kycLivenessUrl": progress.TreezorKYCLivenessUrl,
		})
	}, accounts.MiddlewareRequireAccountId)

	return nil
}
