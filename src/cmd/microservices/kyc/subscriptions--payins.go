package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/lock"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

const (
	RequiredInitialPayment = 10 // in euros
)

func subscribeToTreezorPayins(dlocker *lock.DistributedLocker, busrpc bus2.RpcClient, signaling bus2.Client, documentsColl dbnosql.DataCollection, progressColl *kyc.ProgressCollection) error {
	payinsQueue, err := signaling.DeclareSubscriptionQueue("KYC.Subscriptions-payins", true, false,
		[]string{
			"treezor.payin.create",
			"treezor.payin.update",
		})
	if err != nil {
		return err
	}

	if err = signaling.SubscribeToEventForever(payinsQueue, "kyc/subscriptions--payins.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.payin.{create,update})").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		tpayins, err := treezor.UnwrapPayinsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping payins from webhook event", zap.Error(err))
			return
		}

		// The goal here is to find-out if the user still needs to make an initial payment (transfer to be exact) that
		// will fully unlock their account. The rule is that it has to be a bank transfer, meaning that the user is
		// already validated from another bank, and he'll need to send a transfer from that bank, bearing his own name
		// as well, to be fully validated. So basically, it makes us feel easier knowing that another bank already
		// trusts this user, and that he's not a fraudster.

		for _, tpayin := range tpayins {
			// We'll only process Bank transfers, not top-ups or anything else as that's the business requirements
			switch tpayin.PaymentMethodID {
			case 2, 4, 5, 20, 27:
				// See also the treezor/model--payins.go
			default:
				L.Warn("Ignoring a non Bank Transfer Payin as that's the only acceptable for KYC validation", zap.Any("treezorPayin", tpayin))
				continue
			}

			// FIXME: We need to check if the transfer is from a bank account of the same user, meaning that the names match.

			// Get the matching Account by the primary Wallet ID
			account, err := accounts.GetByTreezorPrimaryWalletId(busrpc, tpayin.TreezorWalletID)
			if err != nil {
				L.Error("Error while getting matching Account (by Primary Wallet ID)", zap.Error(err), zap.Any("treezorPayin", tpayin))
				continue
			}
			if account == nil {
				L.Error("No matching Account found (by Primary Wallet ID)", zap.Any("treezorPayin", tpayin))
				continue
			}

			// Lock the Progress for writing
			unlockFunc, err := progressColl.GetDLockMutexByAccountId(dlocker, account.ID)
			if err != nil {
				L.Error("Error while locking the Progress", zap.Error(err), zap.Any("treezorPayin", tpayin))
				continue
			}

			// Wrap the whole operation in a function, so we can easily use defer
			func() {
				defer unlockFunc()

				// Get the progress of the user
				progress, err := progressColl.GetByTreezorUserId(tpayin.TreezorUserID)
				if err != nil {
					L.Error("Error while getting progress", zap.Error(err), zap.Any("treezorPayin", tpayin))
					return
				}
				if progress == nil {
					// It is possible that we'll need to lookup via the Wallet ID ... the Treezor User ID is not always
					// corresponding to our expectation, it could be == 3, especially in production
					progress, err = progressColl.GetByTreezorWalletId(busrpc, tpayin.TreezorWalletID)
					if err != nil {
						L.Error("Error while getting progress via Wallet ID", zap.Error(err), zap.Any("treezorPayin", tpayin))
						return
					}
					if progress == nil {
						L.Error("No progress found for the user that received the payment, even via Wallet ID 🛑", zap.Any("treezorPayin", tpayin))
						return
					}
					L.Info("Found progress via Wallet ID", zap.Any("treezorPayin", tpayin), zap.Any("progress", progress))
				} else {
					L.Info("Found progress via User ID", zap.Any("treezorPayin", tpayin), zap.Any("progress", progress))
				}

				// Update the progress
				if progress.IsInitialPaymentReceived == false && tpayin.PayinStatus == "VALIDATED" {
					progress.TotalInitialPaymentReceived += tpayin.Amount
					if progress.TotalInitialPaymentReceived < RequiredInitialPayment {
						L.Warn("Total Initial payment is still too small", zap.Float64("amount", tpayin.Amount), zap.Float64("total", progress.TotalInitialPaymentReceived))
					} else {
						progress.IsInitialPaymentReceived = true
					}
					progress.UpdatedAt = dbnosql.PNow()
					if err = progressColl.Update(progress); err != nil {
						L.Error("Error while updating progress", zap.Error(err), zap.Any("treezorPayin", tpayin))
						return
					}
					L.Info("Initial payment fully received", zap.Float64("amount", tpayin.Amount), zap.Float64("total", progress.TotalInitialPaymentReceived))

					// Can we request a review on Treezor's side now?
					if progress.IsInitialPaymentReceived {
						if _, err := tryToRequestTreezorReview(L, busrpc, signaling, account, documentsColl, progressColl, progress); err != nil {
							L.Error("Error while trying to request a review on Treezor's side", zap.Error(err), zap.Any("treezorPayin", tpayin))
							return
						}
					}
				}
			}()
		}

		// Don't forget to ack the event, after success
		_ = ack()
	}); err != nil {
		return err
	}

	return nil
}
