package main

import (
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToDocumentEvents(busrpc bus2.RpcClient, signaling bus2.Client, documentsColl dbnosql.DataCollection, progressColl *kyc.ProgressCollection) error {
	documentsQueue, err := signaling.DeclareSubscriptionQueue("KYC.Subscriptions-documents", true, false,
		[]string{
			"treezor.document.create",
			"treezor.document.update",
			"treezor.document.cancel",
		})
	if err != nil {
		return err
	}

	if err := signaling.SubscribeToEventForever(documentsQueue, "treezor.document.*", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.document.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		documents, err := treezor.UnwrapDocumentsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Documents from webhook event", zap.Error(err))
			return
		}

		// Care should be taken here not to confuse the Treezor Documents with the ones from our database in this
		// microservice. One is kyc.Document, and the other is treezor.Document.
		for _, tdoc := range documents {
			doc, err := updateDocumentWithIncomingTreezorDocument(documentsColl, tdoc)
			if err != nil {
				L.Error("Error while syncing with incoming Treezor Document", zap.Error(err))
				continue
			}

			account, err := accounts.GetByTreezorUser(busrpc, tdoc.TreezorUserId)
			if err != nil {
				L.Error("Error while getting Account by Treezor user", zap.Error(err))
				continue
			}
			if account == nil {
				L.Error("Account does not exist anymore", zap.Int64("treezorUserId", tdoc.TreezorUserId))
				continue
			}
			progress, err := getProgressByAccount(progressColl, account.ID)
			if err != nil {
				L.Error("Error while getting Progress by Account", zap.Error(err))
				continue
			}
			if progress == nil {
				L.Error("Progress does not exist !", zap.String("accountId", account.ID))
				continue
			}

			if doc == nil {
				// Create a new document
				doc = &kyc.Document{
					ID:                documentsColl.GenerateUniqueId(),
					AccountId:         account.ID,
					TreezorUserId:     account.TreezorUserId,
					TreezorDocumentId: tdoc.TreezorDocumentId,
					TreezorStatusCode: tdoc.CodeStatus,
					Type:              kyc.DocumentType(tdoc.DocumentTypeId),
					TypeText:          kyc.DocumentType(tdoc.DocumentTypeId).String(),
					Filename:          tdoc.FileName,
					OriginalFilename:  tdoc.FileName,
					Size:              tdoc.FileSize,
					Status:            kyc.DSY_Uploaded,
					StatusText:        kyc.DSY_Uploaded.String(),
					SecretUrl:         tdoc.TemporaryUrl,
					CreatedAt:         time.Now(),
				}
				if _, err = documentsColl.Insert(doc); err != nil {
					L.Error("Error while inserting a New document", zap.Error(err))
					continue
				}
			}
			L.Info("Synced with incoming Treezor Document",
				zap.String("id", doc.ID), zap.Int64("treezorId", tdoc.TreezorDocumentId),
				zap.String("treezorStatus", doc.TreezorStatus))

			L = L.With(zap.String("accountId", account.ID), zap.String("documentId", doc.ID), zap.Int64("treezorDocumentId", doc.TreezorDocumentId))
			L.Info("KYC Liveness was successful, documents have sent and received at Treezor")

			// Update the progress now that we have the documents at Treezor's side
			progress.IsDocumentsSentToTreezor = true
			if err = progressColl.Update(progress); err != nil {
				L.Error("Error while updating Progress", zap.Error(err))
				continue
			}

			// Start the KYC review on Treezor's side
			if _, err := tryToRequestTreezorReview(L, busrpc, signaling, account, documentsColl, progressColl, progress); err != nil {
				continue // error message already logged
			}
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}
	return nil
}
