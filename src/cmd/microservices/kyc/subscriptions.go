package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/lock"
	"yochbee/common/kyc"
)

func registerSubscriptions(dlocker *lock.DistributedLocker, busrpc bus2.RpcClient, signaling bus2.Client, progressColl *kyc.ProgressCollection, documentsColl dbnosql.DataCollection) error {
	var err error

	if err = subscribeToDocumentEvents(busrpc, signaling, documentsColl, progressColl); err != nil {
		return err
	}

	if err = subscribeToUserEvents(dlocker, busrpc, signaling, progressColl); err != nil {
		return err
	}

	if err = subscribeToLivenessStatusEvents(dlocker, busrpc, signaling, progressColl, documentsColl); err != nil {
		return err
	}

	if err = subscribeToTreezorPayins(dlocker, busrpc, signaling, documentsColl, progressColl); err != nil {
		return err
	}

	return nil
}
