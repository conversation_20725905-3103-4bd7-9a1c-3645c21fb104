package main

import (
	"errors"
	"fmt"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"
)

var ErrCannotStartReview = errors.New("cannot start KYC review")

func getProgressByAccount(progressColl dbnosql.DataCollection, accountId string) (*kyc.Progress, error) {
	q := progressColl.GetQueryBuilder()
	err := q.Set(&dbnosql.Condition{Field: "accountId", Comparator: "==", Value: accountId})
	if err != nil {
		return nil, err
	}
	docs, err := progressColl.Find(q)
	if err != nil {
		return nil, err
	}
	if len(docs) == 0 {
		return nil, nil
	}
	return docs[0].(*kyc.Progress), nil
}

func requestReviewForAccount(documentsColl dbnosql.DataCollection, rpc bus2.RpcClient, signaling bus2.Client, account *accounts.Account, progress *kyc.Progress) error {
	// Associate all valid documents for the review
	documents, err := getDocumentsForAccount(documentsColl, account.ID)
	if err != nil {
		return err
	}

	// Require a Ready for Review status
	status, err := progress.ComputeProgressStatus(documents)
	if err != nil {
		return err
	}
	if status.Status != kyc.PS_ReviewCanStart {
		return ErrCannotStartReview
	}

	allValidDocuments := make([]*kyc.Document, 0, len(documents))
	for _, d := range documents {
		if d.IsAcceptable() {
			allValidDocuments = append(allValidDocuments, d)
		}
	}

	// Ask Treezor to start the review
	_, err = rpc.CallM(treezor.Method_StartKYCReview, &treezor.StartKYCReviewRequest{
		Progress: progress,
	}, 10*time.Second)
	if err != nil {
		return err
	}

	// Send an event on the bus about this even
	if err = signaling.PublishEvent(&bus2.Event{
		Topic: kyc.Event_KYCReviewStartRequestedTopic,
		Data: &kyc.Event_KYCReviewStartRequested{
			Account:        account,
			ValidDocuments: allValidDocuments,
			ProgressObject: progress,
		},
		Time: time.Now(),
	}); err != nil {
		return err
	}

	return nil
}

func requestReviewForAccountAfterLiveness(rpc bus2.RpcClient, signaling bus2.Client, account *accounts.Account, progress *kyc.Progress) error {
	// Require a Ready for Review status
	status, err := progress.ComputeProgressStatusWithLiveness()
	if err != nil {
		return err
	}
	if status.Status == kyc.PS_PermanentlyRefused {
		return ErrCannotStartReview
	}

	// Ask Treezor to start the review
	_, err = rpc.CallM(treezor.Method_StartKYCReview, &treezor.StartKYCReviewRequest{
		Progress: progress,
	}, 10*time.Second)
	if err != nil {
		return err
	}

	// Send an event on the bus about this even
	if err = signaling.PublishEvent(&bus2.Event{
		Topic: kyc.Event_KYCReviewStartRequestedTopic,
		Data: &kyc.Event_KYCReviewStartRequested{
			Account:        account,
			ProgressObject: progress,
		},
		Time: time.Now(),
	}); err != nil {
		return err
	}

	return nil
}

func syncProgressWithIncomingTreezorUser(progressColl *kyc.ProgressCollection, tuser *treezor.User) (progress *kyc.Progress, err error) {
	progress, err = dbnosql.GetOneByField[*kyc.Progress](progressColl, "treezorUserId", tuser.TreezorUserId)
	if err != nil {
		return nil, err
	}

	if progress == nil {
		return nil, fmt.Errorf("no progress found for Treezor user %d", tuser.TreezorUserId)
	}

	progress.TreezorUserId = tuser.TreezorUserId
	progress.TreezorKYCLevel = kyc.KycLevel(tuser.KYCLevel)
	progress.TreezorKYCReview = kyc.KycReview(tuser.KYCReview)
	progress.TreezorKYCReviewComment = tuser.KYCReviewComment
	if progress.TreezorKYCLevel == kyc.KL_Refused ||
		progress.TreezorKYCReview == kyc.KR_None ||
		progress.TreezorKYCReview == kyc.KR_Refused ||
		progress.TreezorKYCReview == kyc.KR_Validated {
		// If there has been a review in progress, it is now over
		progress.IsTreezorReviewStarted = false
		progress.IsReviewRequested = false
	}
	if progress.TreezorKYCLevel == kyc.KL_Refused ||
		progress.TreezorKYCReview == kyc.KR_Refused {
		// If the last review is refused
		progress.LastKYCLivenessScore = 0
		progress.LastKYCLivenessWebhookTimestamp = 0
	}
	now := time.Now()
	progress.UpdatedAt = &now
	if err = progressColl.UpdateById(progress.ID, progress); err != nil {
		return nil, err
	}

	return progress, nil
}
