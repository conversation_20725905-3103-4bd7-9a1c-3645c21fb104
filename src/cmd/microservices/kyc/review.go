package main

import (
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/accounts"
	"yochbee/common/kyc"

	"go.uber.org/zap"
)

func tryToRequestTreezorReview(L *zap.Logger, rpc bus2.RpcClient, signaling bus2.Client, account *accounts.Account, documentsColl dbnosql.DataCollection, progressColl *kyc.ProgressCollection, progress *kyc.Progress) (bool, error) {
	L = L.Named("tryToRequestTreezorReview()").With(zap.String("accountId", progress.AccountId), zap.String("progressId", progress.ID))
	if progress.TreezorKYCReview == kyc.KR_Validated {
		L.Info("Already validated by Treezor")
		return true, nil
	}
	if progress.IsTreezorReviewStarted || progress.TreezorKYCReview == kyc.KR_Pending || progress.TreezorKYCReview == kyc.KR_Investigating {
		L.Info("Already requested for Treezor review")
		return true, nil
	}
	if progress.TreezorKYCLevel == kyc.KL_Refused {
		L.Info("Review is permanently refused by Treezor")
		return true, nil
	}

	// - User has provided all required declarative information
	// - Liveness has been completed with success
	// - Documents have been sent to Treezor
	// - User has paid the initial amount
	//
	// NOTE: We don't require a payment here yet.
	canRequest := progress.HasRequiredDeclarativeKYCInfo &&
		progress.LastKYCLivenessWebhookTimestamp != 0 && progress.LastKYCLivenessScore == 1 &&
		progress.IsDocumentsSentToTreezor &&
		progress.IsInitialPaymentReceived

	if canRequest {
		// ╔═╗┌┬┐┌─┐┬─┐┌┬┐  ╔╦╗╦═╗╔═╗╔═╗╔═╗╔═╗╦═╗  ╦╔═╦ ╦╔═╗  ╦═╗┌─┐┬  ┬┬┌─┐┬ ┬
		// ╚═╗ │ ├─┤├┬┘ │    ║ ╠╦╝║╣ ║╣ ╔═╝║ ║╠╦╝  ╠╩╗╚╦╝║    ╠╦╝├┤ └┐┌┘│├┤ │││
		// ╚═╝ ┴ ┴ ┴┴└─ ┴    ╩ ╩╚═╚═╝╚═╝╚═╝╚═╝╩╚═  ╩ ╩ ╩ ╚═╝  ╩╚═└─┘ └┘ ┴└─┘└┴┘

		if DoKYCLiveness {
			if err := requestReviewForAccountAfterLiveness(rpc, signaling, account, progress); err != nil {
				L.Error("Could not Request KYC review by Treezor (after Liveness)", zap.Error(err))
				return false, err
			}
		} else {
			if err := requestReviewForAccount(documentsColl, rpc, signaling, account, progress); err != nil {
				L.Error("Could not Request KYC review by Treezor", zap.Error(err))
				return false, err
			}
		}

		// Update the progress
		now := time.Now()
		progress.UpdatedAt = &now
		progress.IsReviewRequested = false
		progress.IsTreezorReviewStarted = true
		progress.TreezorKYCLivenessId = ""
		progress.TreezorKYCLivenessUrl = ""
		if err := progressColl.Update(progress); err != nil {
			L.Error("Could not update Progress !!! Progress review has been requested!", zap.Error(err))
			return false, err
		}
		L.Info("Treezor KYC review has been requested")
		return true, nil
	} else {
		L.Info("Not yet ready for final Treezor KYC review")
		return false, nil
	}
}
