package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/_base/lock"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToUserEvents(dlocker *lock.DistributedLocker, busrpc bus2.RpcClient, signaling bus2.Client, progressColl *kyc.ProgressCollection) error {
	kycReviewsQueue, err := signaling.DeclareSubscriptionQueue("KYC.Subscriptions-kycReviews", true, false,
		[]string{
			// Only the kycreview seems to be transmitting the correct status of the KYC review. The user.update event
			// does not seem to be transmitting the correct status of the KYC review, usually, it's not immediate.
			// (see https://docs.treezor.com/guide/users/faking-operations.html#users-kyc-validation)
			"treezor.user.kycreview",
			"treezor.user.kycrequest",
		})
	if err != nil {
		return err
	}

	if err = signaling.SubscribeToEventForever(kycReviewsQueue, "kyc/subscriptions--users.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.user.{kycreview})").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		// We expect to receive updates to the kycLevel and kycReview fields of the user.
		tusers, err := treezor.UnwrapUsersFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping users from webhook event", zap.Error(err))
			return
		}

		for _, tuser := range tusers {
			before := *tuser

			// Get the account matching the Treezor User ID
			account, err := accounts.GetByTreezorUser(busrpc, tuser.TreezorUserId)
			if err != nil {
				L.Error("Error while getting matching account by Treezor User ID", zap.Error(err))
				continue
			}
			if account == nil {
				L.Warn("No matching account found by Treezor User ID", zap.Int64("treezorUserId", tuser.TreezorUserId))
				continue
			}

			// Lock the Progress for writing, by Account ID (that's why we need the account)
			unlockFunc, err := progressColl.GetDLockMutexByAccountId(dlocker, account.ID)
			if err != nil {
				L.Error("Error while locking the Progress for writing", zap.Error(err))
				continue
			}

			// Perform the write operation
			_, err = syncProgressWithIncomingTreezorUser(progressColl, tuser)
			if err != nil {
				unlockFunc()
				L.Error("Error while syncing with incoming Treezor User", zap.Error(err))
				continue
			}
			unlockFunc()

			// We get the updated:
			// - KYC Level
			// - KYC Review
			// - KYC Review comment (if any)
			L.Info("Updated KYC status",
				zap.Int64("treezorUserId", tuser.TreezorUserId),
				zap.Int("kycLevelBefore", before.KYCLevel),
				zap.Int("kycLevelAfter", tuser.KYCLevel),
				zap.Int("kycReviewBefore", before.KYCReview),
				zap.Int("kycReviewAfter", tuser.KYCReview),
				zap.String("kycReviewCommentBefore", before.KYCReviewComment),
				zap.String("kycReviewCommentAfter", tuser.KYCReviewComment))
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}
	return nil
}
