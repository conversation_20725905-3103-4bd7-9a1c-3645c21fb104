package main

import (
	"fmt"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/lock"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToLivenessStatusEvents(dlocker *lock.DistributedLocker, busrpc bus2.RpcClient, signaling bus2.Client, progressColl *kyc.ProgressCollection, documentsColl dbnosql.DataCollection) error {
	kycLivenessQueue, err := signaling.DeclareSubscriptionQueue("KYC.Subscriptions-kycLiveness", true, false,
		[]string{
			"treezor.kycliveness.create",
			"treezor.kycliveness.update",
		})
	if err != nil {
		return err
	}

	return signaling.SubscribeToEventForever(kycLivenessQueue, "treezor.kycliveness.*", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.kycliveness.{create,update})").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		L = L.With(zap.String("topic", event.Topic))
		status, err := treezor.UnwrapLivenessStatusFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Could not unwrap the LivenessStatus object from event", zap.Error(err))
			return
		}
		L = L.With(zap.Int64("treezorUserId", status.TreezorUserId), zap.String("status", status.Status))

		// Get the account that is associated with this status
		account, err := accounts.GetByTreezorUser(busrpc, status.TreezorUserId)
		if err != nil {
			L.Error("Could not get the Account object from the DB", zap.Error(err))
			return
		}
		if account == nil {
			L.Warn("Could not find any Account matching the Liveness status", zap.Int64("treezorUserId", status.TreezorUserId))
			return
		}
		L = L.With(zap.String("accountId", account.ID))

		// Lock the progress by the account
		unlockFunc, err := progressColl.GetDLockMutexByAccountId(dlocker, account.ID)
		if err != nil {
			L.Error("Could not lock the Progress object", zap.Error(err))
			return
		}
		defer unlockFunc()

		// Retrieve the Progress object that is associated with the User mentioned in this event (same locked object)
		progress, err := progressColl.GetByTreezorUserId(account.TreezorUserId)
		if err != nil {
			L.Error("Could not get the Progress object from the DB", zap.Error(err))
			return
		}
		if progress == nil {
			L.Error("Could not find the Progress object in the DB", zap.Int64("treezorUserId", status.TreezorUserId))
			return
		}
		L = L.With(zap.String("progressId", progress.ID))
		oldProgress := *progress

		//
		// We process both the kycliveness.create and kycliveness.update below
		//

		// Check if this is the most recent kycliveness.{create,update} that we have received for the progress
		if progress.LastKYCLivenessWebhookTimestamp != 0 && progress.LastKYCLivenessWebhookTimestamp > event.WebhookTimestamp {
			L.Warn("Received a kycliveness.{create,update} event that is older than the last one we received. Skipping.",
				zap.Int64("lastWebhookTimestamp", progress.LastKYCLivenessWebhookTimestamp),
				zap.Int64("currentWebhookTimestamp", event.WebhookTimestamp))

			// We do ACK it however, in order to avoid blocking the queue
			_ = ack()

			return
		}
		progress.LastKYCLivenessWebhookTimestamp = event.WebhookTimestamp
		progress.LastKYCLivenessScore = status.Score
		L.Info("Progress is being updated: {score}, {webhookTimestamp}", zap.Int("score", status.Score), zap.Int64("webhookTimestamp", event.WebhookTimestamp))

		switch status.Status {
		case "initiated", "processing":
			// We don't really need to do anything here ...
			// Just wait for the status to become processed
			L.Info("KYC Liveness result processing")
			progress.IsReviewRequested = true
			progress.IsTreezorReviewStarted = false

		case "processed":
			if status.Score == 1 {
				//
				// ╔═╗┌─┐┌┐┌┌┬┐  ╔╦╗╔═╗╔═╗╦ ╦╔╦╗╔═╗╔╗╔╔╦╗╔═╗  ┌┬┐┌─┐  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐
				// ╚═╗├┤ │││ ││   ║║║ ║║  ║ ║║║║║╣ ║║║ ║ ╚═╗   │ │ │   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘
				// ╚═╝└─┘┘└┘─┴┘  ═╩╝╚═╝╚═╝╚═╝╩ ╩╚═╝╝╚╝ ╩ ╚═╝   ┴ └─┘   ╩ ┴└─└─┘└─┘└─┘└─┘┴└─
				//
				L.Info("KYC Liveness processed successfully, triggering documents sending to Treezor ...", zap.Int("score", status.Score))
				result, err := bus2.CallMethodMU[treezor.TriggerKYCLivenessDocumentsSendingTreezorResponse](busrpc, treezor.Method_TriggerKYCLivenessDocumentsSendingTreezor, &treezor.TriggerKYCLivenessDocumentsSendingTreezorRequest{
					TreezorUserId: account.TreezorUserId,
				}, 10*time.Second)
				if err != nil || !result.Ok {
					L.Error("Could not trigger the KYC Liveness documents sending to Treezor", zap.Error(err))
					return
				}
				progress.IsDocumentsSentToTreezor = true
				progress.TreezorKYCLivenessId = ""
				progress.TreezorKYCLivenessUrl = ""
				L.Info("KYC Liveness documents sending to Treezor triggered successfully ⛳️")
			} else {
				L.Info("KYC Liveness processed successfully, but is not good for Treezor Review: score = {score}", zap.Int("score", status.Score))

				// Progress status has moved to `processed` and its `score` is not 1 ...
				progress.Downgrade()
				L.Info("Progress has been downgraded")
			}

		case "expired", "aborted":
			L.Info("KYC Liveness has expired or been aborted", zap.String("status", status.Status))

			// Downgrade the progress
			progress.Downgrade()
			L.Info("Progress has been downgraded")

		default:
			panic(fmt.Sprintf("unexpected KYC liveness status = %s, Treezor API has changed without warning", status.Status))
		}

		// Update the progress object in the DB
		if err = progressColl.UpdateById(progress.ID, progress); err != nil {
			L.Error("Failed to update the Progress on the DB", zap.Error(err),
				zap.Int64("treezorUserId", status.TreezorUserId),
				zap.Any("oldProgress", &oldProgress),
				zap.Any("updatedProgress", progress))
			return
		}
		L.Info("Progress updated", zap.Int64("treezorUserId", status.TreezorUserId),
			zap.Any("oldProgress", &oldProgress),
			zap.Any("updatedProgress", progress))

		// Don't forget to ...
		_ = ack()
	})
}
