package main

import (
	"yochbee/_base"
	"yochbee/_base/bus"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"
	"yochbee/_base/lock"
	"yochbee/_base/web"
	"yochbee/common/kyc"

	"github.com/eliezedeck/gobase/azure"
	"github.com/labstack/echo/v4"
)

func registerAllServices(dlocker *lock.DistributedLocker, rpc bus2.RpcClient, signaling bus2.Client, e *echo.Echo, storage *azure.BlobService, db dbnosql.Database, progressCollection *kyc.ProgressCollection) error {
	// ---------------------------------
	documentsCollection, err := setupDocumentsCollection(db)
	if err != nil {
		return err
	}

	// ---------------------------------
	if err = registerRESTAPIs(dlocker, rpc, signaling, e, storage, documentsCollection, progressCollection); err != nil {
		return err
	}

	// ---------------------------------
	if err := registerBusMethods(rpc, documentsCollection, progressCollection); err != nil {
		return err
	}

	// ---------------------------------
	if err := registerSubscriptions(dlocker, rpc, signaling, progressCollection, documentsCollection); err != nil {
		return err
	}

	e.POST("/_bus/call-method/:methodName", bus.RegisterBusMethodCallingFromREST(rpc))

	return nil
}

var (
	DoKYCLiveness = true
)

func main() {
	config.Microservice = "kyc"
	web.ServerEndpoint = ":8084"

	app := _base.SetupApp("KYC",
		registerAllServices)
	app.Run()
}
