package main

import (
	"errors"
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/common/kyc"
	"yochbee/common/treezor"
)

var (
	ErrDocumentNotFound = errors.New("document not found")
)

func setupDocumentsCollection(db dbnosql.Database) (dbnosql.DataCollection, error) {
	documentsColl := db.DeclareCollection("kyc-documents", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &kyc.Document{}
	})

	if err := documentsColl.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "accountId", Order: dbnosql.OrderASC},
		},
		Name: "accountId",
	}); err != nil {
		return nil, err
	}

	if err := documentsColl.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
		},
		Name: "createdAt",
	}); err != nil {
		return nil, err
	}

	return documentsColl, nil
}

func getDocumentsForAccount(documentsColl dbnosql.DataCollection, accountId string) (result []*kyc.Document, err error) {
	q := documentsColl.GetQueryBuilder()
	err = q.Set(&dbnosql.Condition{"accountId", "==", accountId})
	if err != nil {
		return nil, err
	}
	q.SetOrderByField("createdAt", dbnosql.OrderDESC)

	docs, err := documentsColl.Find(q)
	if err != nil {
		return nil, err
	}

	for _, d := range docs {
		result = append(result, d.(*kyc.Document))
	}
	return result, nil
}

func updateDocumentStatus(documentsColl dbnosql.DataCollection, doc *kyc.Document, status kyc.DocumentStatus) error {
	doc.Status = status
	now := time.Now()
	doc.UpdatedAt = &now
	return documentsColl.UpdateById(doc.ID, doc)
}

func syncDocumentWithIncomingTreezorStatus(documentsColl dbnosql.DataCollection, doc *kyc.Document, tdoc *treezor.Document, status kyc.DocumentStatus) error {
	now := time.Now()
	doc.UpdatedAt = &now
	doc.TreezorStatusCode = tdoc.CodeStatus
	doc.TreezorStatus = kyc.GetDocumentSummarizedStatusFromCode(tdoc.CodeStatus).String()
	doc.TreezorDocumentId = tdoc.TreezorDocumentId
	doc.Status = status
	return documentsColl.UpdateById(doc.ID, doc)
}

func updateDocumentWithIncomingTreezorDocument(documentsColl dbnosql.DataCollection, tdoc *treezor.Document) (*kyc.Document, error) {
	// Get the document that corresponds to this Treezor document
	doc, err := dbnosql.GetOneByField[*kyc.Document](documentsColl, "treezorDocumentId", tdoc.TreezorDocumentId)
	if err != nil {
		return nil, err
	}
	if doc == nil {
		return nil, nil
	}
	now := time.Now()
	doc.UpdatedAt = &now
	doc.TreezorStatusCode = tdoc.CodeStatus
	doc.TreezorStatus = kyc.GetDocumentSummarizedStatusFromCode(tdoc.CodeStatus).String()
	return doc, documentsColl.UpdateById(doc.ID, doc)
}
