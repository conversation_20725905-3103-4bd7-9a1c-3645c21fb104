package main

import (
	"net/http"
	"strings"
	bus2 "yochbee/_base/bus"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func registerJobsAPIs(
	rpc bus2.RpcClient,
	e *echo.Echo,
	accountsColl *accounts.AccountsCollection,
) {
	// This service is not accessible from the outside ... only available from the inside of the Kubernetes cluster
	e.POST("/jobs/sync-sca-wallets", func(c echo.Context) error {
		L := logging.L.Named("sync-sca-wallets")
		L.Info("Started")
		defer L.Info("Finished")

		// Get all accounts
		laccounts, err := accountsColl.GetAccountsWithoutSCAWallet()
		if err != nil {
			L.Error("Failed to get accounts without SCA Wallet", zap.Error(err))
			return err
		}

		// Create a map to store the results
		resultMap := make(map[string]interface{})

		// Iterate over all accounts
		for _, account := range laccounts {
			// Check if the account has an associated SCA Wallet
			if account.TreezorSCAWalletId == "" {
				L.Info("Creating SCA Wallet for account", zap.String("accountId", account.ID), zap.Int64("treezorUserId", account.TreezorUserId))

				// Create an SCA Wallet for the account
				resp, err := treezor.CreateSCAWalletForUser(rpc, account.TreezorUserId, "Auto-created")
				if err != nil {
					return err
				}

				// Update the account with the SCA Wallet ID
				account.TreezorSCAWalletId = strings.TrimSpace(resp.TreezorSCAWallet.ID)
				if err := accountsColl.UpdateById(account.ID, account); err != nil {
					return err
				}

				// Add the result to the map
				resultMap[account.ID] = resp
			}
		}

		L.Info("Job done", zap.Any("resultMap", resultMap), zap.Int("numAccounts", len(laccounts)))
		return c.JSON(http.StatusOK, resultMap)
	})
}
