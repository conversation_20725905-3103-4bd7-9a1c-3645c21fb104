package main

import (
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"

	"github.com/labstack/echo/v4"
)

const (
	IPLogEvent_AccountRegistration           = "AccountRegistration"
	IPLogEvent_AccountSignIn                 = "AccountSignIn"
	IPLogEvent_AccountUpdate                 = "AccountUpdate"
	IPLogEvent_AccountPasswordResetRequested = "AccountPasswordResetRequested"
	IPLogEvent_AccountPasswordReset          = "AccountPasswordReset"
	IPLogEvent_Account2FAStart               = "Account2FAStart"
	IPLogEvent_Account2FAVerify              = "Account2FAVerify"

	IPLogEvent_TreezorUserCreated   = "TreezorUserCreated"
	IPLogEvent_TreezorWalletCreated = "TreezorWalletCreated"
)

type IPLog struct {
	ID             string    `bson:"_id" json:"id"`
	AccountId      string    `bson:"accountId" json:"accountId"`
	Address        string    `bson:"address" json:"address"`
	CountryIso3166 string    `bson:"countryIso3166" json:"countryIso3166"`
	Event          string    `bson:"event" json:"event"`
	EventTime      time.Time `bson:"eventTime" json:"eventTime"`
	CreatedAt      time.Time `bson:"createdAt" json:"createdAt"`
}

type IPLogsCollection struct {
	dbnosql.DataCollection
}

func setupIPLogsCollection(db dbnosql.Database) (*IPLogsCollection, error) {
	coll := db.DeclareCollection("accounts-ip-logs", func(account interface{}) interface{} {
		return account
	}, func() (destination interface{}) {
		return &IPLog{}
	})
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "accountId", Order: dbnosql.OrderASC},
		},
		Name: "accountId",
	}); err != nil {
		return nil, err
	}
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "address", Order: dbnosql.OrderASC},
		},
		Name: "address",
	}); err != nil {
		return nil, err
	}
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "event", Order: dbnosql.OrderASC},
		},
		Name: "event",
	}); err != nil {
		return nil, err
	}
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "eventTime", Order: dbnosql.OrderDESC},
			{Name: "createdAt", Order: dbnosql.OrderDESC},
		},
		Name: "timeQuery",
	}); err != nil {
		return nil, err
	}
	return &IPLogsCollection{coll}, nil
}

func recordIPAddressForEvent(c echo.Context, ipLogsCollection *IPLogsCollection, accountId, event string) error {
	now := time.Now()
	if _, err := ipLogsCollection.Insert(&IPLog{
		ID:             ipLogsCollection.GenerateUniqueId(),
		AccountId:      accountId,
		Address:        c.RealIP(),
		CountryIso3166: web.GetCloudflareCountryCode(c),
		Event:          event,
		EventTime:      now,
		CreatedAt:      now,
	}); err != nil {
		return err // HTTP 500
	}
	return nil
}
