package main

import (
	"errors"
	"unicode"

	passwordvalidator "github.com/wagslane/go-password-validator"
)

const (
	requiredPasswordEntropyBits = 55
	requiredPasswordLength      = 10
)

var (
	ErrorPasswordNotAcceptable = errors.New("password not acceptable")
	ErrorPasswordTooWeak       = errors.New("password too weak")
)

func PasswordSecurityIsAcceptable(pw string) error {
	// Requires at least 1 uppercase letter
	hasUpper := false
	// Requires at least 1 lowercase letter
	hasNumber := false
	// Requires at least 1 symbol char
	hasSpecialChar := false

	for _, r := range pw {
		if !hasUpper && unicode.IsUpper(r) {
			hasUpper = true
		}
		if !hasNumber && (r >= 48 && r <= 57) {
			hasNumber = true
		}
		if !hasSpecialChar &&
			((r >= 33 && r <= 47) || (r >= 58 && r <= 64) || (r >= 91 && r <= 96) || (r >= 123 && r <= 126)) {
			hasSpecialChar = true
		}
	}

	if !hasUpper || !hasNumber || !hasSpecialChar || len(pw) < requiredPasswordLength {
		return ErrorPasswordNotAcceptable
	}

	ep := GetPasswordEntropy(pw)
	if ep < requiredPasswordEntropyBits {
		return ErrorPasswordTooWeak
	}
	return nil // acceptable password
}

func GetPasswordEntropy(pw string) float64 {
	return passwordvalidator.GetEntropy(pw)
}
