package main

import (
	"yochbee/_base"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"
	"yochbee/common/accounts"

	"github.com/eliezedeck/gobase/azure"
	zconfig "github.com/eliezedeck/gobase/config"
	"github.com/labstack/echo/v4"
	goredislib "github.com/redis/go-redis/v9"
)

func registerAllServices(
	rpc bus2.RpcClient,
	e *echo.Echo,
	cache *goredislib.Client,
	abs *azure.BlobService,
	db dbnosql.Database,
	accountsColl *accounts.AccountsCollection,
	twoFaColl *accounts.TwoFactorAuthCollection,
) error {
	onboardingTrackersCollection, err := setupOnboardingTrackersCollection(db)
	if err != nil {
		return err
	}
	ipLogsCollection, err := setupIPLogsCollection(db)
	if err != nil {
		return err
	}

	// ---------------------------------
	registerRESTAPIs(rpc, e, cache, abs, accountsColl, onboardingTrackersCollection, ipLogsCollection, twoFaColl)

	// ---------------------------------
	if err = registerBusMethods(rpc, accountsColl); err != nil {
		return err
	}

	// ---------------------------------
	// If the environment variable DISABLE_AUTO_ONBOARDING_TRACKERS_CLEANUP is equal to true, do not run this
	if zconfig.GetEnvValueOrDefault("DISABLE_AUTO_ONBOARDING_TRACKERS_CLEANUP", "false") != "true" {
		go beginAutomaticOnboardingTrackersCleanup(onboardingTrackersCollection)
	}

	e.POST("/_bus/call-method/:methodName", bus2.RegisterBusMethodCallingFromREST(rpc))

	return nil
}

func main() {
	config.Microservice = "accounts"
	web.ServerEndpoint = ":8080"

	app := _base.SetupApp("Accounts",
		accounts.ConfigureJWT,
		registerAllServices,
		registerJobsAPIs)

	app.Run()
}
