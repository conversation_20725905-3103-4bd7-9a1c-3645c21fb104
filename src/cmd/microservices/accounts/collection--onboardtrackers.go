package main

import (
	"time"
	"yochbee/_base/dbnosql"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type OnboardingTracker struct {
	ID string `json:"-" bson:"_id"`

	PhoneNumber           string     `json:"phoneNumber"           bson:"phoneNumber"`
	VerificationToken     string     `json:"verificationToken"     bson:"verificationToken"`
	VerificationStartTime *time.Time `json:"verificationStartTime" bson:"verificationStartTime"`
	ValidatedUntil        *time.Time `json:"validatedUntil"        bson:"validatedUntil"`

	OneTimePassword string `json:"-" bson:"oneTimePassword"`
}

type OnboardingTrackersCollection struct {
	dbnosql.DataCollection
}

func setupOnboardingTrackersCollection(db dbnosql.Database) (*OnboardingTrackersCollection, error) {
	coll := db.DeclareCollection("accounts-onboarding-trackers", func(tracker interface{}) interface{} {
		return tracker
	}, func() (destination interface{}) {
		return &OnboardingTracker{}
	})

	// Indexes
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "phoneNumber", Order: dbnosql.OrderASC},
			{Name: "verificationStartTime", Order: dbnosql.OrderDESC},
		},
		Name: "query",
	}); err != nil {
		return nil, err
	}

	// Setup unique indexes
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "verificationToken", Order: dbnosql.OrderASC},
			{Name: "validatedUntil", Order: dbnosql.OrderASC},
		},
		Name: "verification",
	}); err != nil {
		return nil, err
	}

	return &OnboardingTrackersCollection{coll}, nil
}

func beginAutomaticOnboardingTrackersCleanup(trackersColl *OnboardingTrackersCollection) {
	L := logging.L.Named("beginAutomaticOnboardingTrackersCleanup")
	L.Info("Starting automatic cleanup")

	qIncomplete := trackersColl.GetQueryBuilder()
	if err := qIncomplete.SetAnd(&dbnosql.Condition{
		Field:      "validatedUntil",
		Comparator: "==",
		Value:      nil,
	}, &dbnosql.Condition{
		Field:      "verificationStartTime",
		Comparator: "<",
		Value:      time.Now().Add(-6 * time.Hour),
	}); err != nil {
		panic("invalid query")
	}

	qCompleted := trackersColl.GetQueryBuilder()
	if err := qCompleted.SetAnd(&dbnosql.Condition{
		Field:      "validatedUntil",
		Comparator: "!=",
		Value:      nil,
	}, &dbnosql.Condition{
		Field:      "validatedUntil",
		Comparator: "<",
		Value:      time.Now(),
	}); err != nil {
		panic("invalid query")
	}

	for {
		if n, err := trackersColl.Delete(qIncomplete); err != nil {
			L.Error("Failed deleting incomplete OnboardingTracker entries", zap.Error(err))
		} else if n > 0 {
			L.Info("Deleted incomplete OnboardingTracker entries", zap.Int64("count", n))
		}

		if n, err := trackersColl.Delete(qCompleted); err != nil {
			L.Error("Failed deleting completed OnboardingTracker entries", zap.Error(err))
		} else if n > 0 {
			L.Info("Deleted completed OnboardingTracker entries", zap.Int64("count", n))
		}

		time.Sleep(5 * time.Minute)
	}
}
