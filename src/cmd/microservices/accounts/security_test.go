package main

import "testing"

func TestPasswordsAcceptance(t *testing.T) {
	passwords := map[string]bool{
		"1234567890":        false,
		"1234567890a":       false,
		"1234567890A":       false,
		"1234567890!":       false,
		"1234567890a!":      false,
		"1234567890A!":      false, // score too low
		"1234567890aA,":     false, // score too low
		"1234567890aA!@":    false, // score too low
		"Treezor123456789,": true,
	}

	for pw, acceptable := range passwords {
		err := PasswordSecurityIsAcceptable(pw)
		if err != nil && acceptable {
			t.<PERSON><PERSON>("Password %s is not acceptable but should be", pw)
		}
		if err == nil && !acceptable {
			t.<PERSON>rf("Password %s is acceptable but should not be", pw)
		}
	}
}
