package main

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"
	"yochbee/_base/random"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/notifications"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/azure"
	"github.com/eliezedeck/gobase/logging"
	webgobase "github.com/eliezedeck/gobase/web"
	"github.com/golang-jwt/jwt"
	"github.com/labstack/echo/v4"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

var (
	jwtTokenDuration             = 1 * time.Hour
	jwt2FATemporaryTokenDuration = 5 * time.Minute
	jwtSCATemporaryTokenDuration = 5 * time.Minute
)

func registerRESTAPIs(
	busrpc bus2.RpcClient,
	e *echo.Echo,
	cache *goredislib.Client,
	abs *azure.BlobService,
	accountsColl *accounts.AccountsCollection,
	onboardingTrackersColl *OnboardingTrackersCollection,
	ipLogsCollection *IPLogsCollection,
	twoFAsColl *accounts.TwoFactorAuthCollection,
) {
	// Add common requirements in the context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("busrpc", busrpc)
			c.Set("cache", cache)
			c.Set("abs", abs)
			return next(c)
		}
	})

	// Common: Verify that the phone number was really verified within the 6 hours period given
	ensurePhoneNumberHasBeenVerified := func(c echo.Context, intlPhoneNumber, verificationToken string) (abort bool, err error) {
		qp := onboardingTrackersColl.GetQueryBuilder()
		_ = qp.Set(&dbnosql.Condition{Field: "verificationToken", Comparator: "==", Value: verificationToken})
		trackerdocs, err := onboardingTrackersColl.Find(qp)
		if err != nil {
			return true, err // HTTP 500
		}
		if len(trackerdocs) == 0 ||
			trackerdocs[0].(*OnboardingTracker).ValidatedUntil == nil ||
			trackerdocs[0].(*OnboardingTracker).ValidatedUntil.Before(time.Now()) ||
			trackerdocs[0].(*OnboardingTracker).PhoneNumber != intlPhoneNumber {
			return true, c.JSON(http.StatusOK, map[string]interface{}{
				"error": "Phone number needs verification",
				"code":  common.ErrorCodePhoneNumberNeedsVerification,
			})
		}
		return false, nil
	}

	allowAccountsDeletion := strings.TrimSpace(os.Getenv("ALLOW_ACCOUNTS_DELETION")) == "true"

	// Add common dependencies into the context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("accountsColl", accountsColl)
			c.Set("onboardingTrackersColl", onboardingTrackersColl)
			c.Set("ipLogsColl", ipLogsCollection)
			return next(c)
		}
	})

	e.POST("/auth/start-phone-number-verification", func(c echo.Context) error {
		L := logging.L.Named("start-phone-number-verification")
		L.Info("Request received")
		defer L.Info("Finished")

		req := struct {
			PhoneNumber string `json:"phoneNumber"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get a standardized and validated phone number
		intlPhoneNumber, err := web.GetInternationalPhoneNumber(c, busrpc, req.PhoneNumber)
		if err != nil {
			return common.ReturnInvalidPhoneNumberResponse(c)
		}

		// Do not allow too frequent verification ... 1 verification per minute, per phone number
		q := onboardingTrackersColl.GetQueryBuilder()
		_ = q.Set(&dbnosql.Condition{Field: "phoneNumber", Comparator: "==", Value: intlPhoneNumber})
		q.SetOrderByField("verificationStartTime", dbnosql.OrderDESC)
		q.SetLimit(1)
		list, err := onboardingTrackersColl.Find(q)
		if err != nil {
			return err // HTTP 500
		}
		now := time.Now()
		if len(list) > 0 {
			// Check if one is still too recent
			existing := list[0].(*OnboardingTracker)
			if now.Sub(*existing.VerificationStartTime) < 60*time.Second {
				// Resend the verification token for the user to follow-up and validate with the already sent SMS
				return c.JSON(http.StatusOK, map[string]interface{}{
					"verificationToken": existing.VerificationToken,
					"error":             "SMS has been recently sent, check the code from your SMS and enter it to validate",
					"code":              common.ErrorCodeSMSRecentlySent,
				})
			}
		}

		// Assign a random token to this request, it will be returned to the user
		token := random.Strings(33) // yes, odd number to prevent manual hacking that assumes 32 chars just by the looks of the string
		otp, err := random.GenerateSecureNumberSequence(4)
		if err != nil {
			return err // HTTP 500
		}
		if config.IsDebugMode() {
			L.Info("DEBUG: Generated OTP", zap.String("otp", otp), zap.String("token", token), zap.String("phoneNumber", intlPhoneNumber))
		}

		// Send the SMS to the user
		if _, err := busrpc.CallM(notifications.Method_SendSMS, notifications.SendSMSRequest{
			ToPhoneNumber: intlPhoneNumber,
			Body:          fmt.Sprintf("Yochbee : voici votre code de vérification\n%s", otp),
		}, 10*time.Second); err != nil {
			return err // HTTP 500
		}

		// Create a tracker
		tracker := &OnboardingTracker{
			ID:                    onboardingTrackersColl.GenerateUniqueId(),
			PhoneNumber:           intlPhoneNumber,
			VerificationToken:     token,
			VerificationStartTime: &now,
			OneTimePassword:       otp,
		}
		if _, err := onboardingTrackersColl.Insert(tracker); err != nil {
			return err // HTTP 500
		}

		// Return the unique token that corresponds to this tracker
		return c.JSON(http.StatusOK, map[string]interface{}{
			"verificationToken": token,
		})
	})

	e.POST("/auth/validate-phone-number", func(c echo.Context) error {
		req := struct {
			VerificationToken string `json:"verificationToken"`
			PhoneNumber       string `json:"phoneNumber"`
			OneTimePassword   string `json:"oneTimePassword"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get a standardized and validated phone number
		intlPhoneNumber, err := web.GetInternationalPhoneNumber(c, busrpc, req.PhoneNumber)
		if err != nil {
			return common.ReturnInvalidPhoneNumberResponse(c)
		}

		// Retrieve a tracker by the unique token
		q := onboardingTrackersColl.GetQueryBuilder()
		_ = q.SetAnd(
			&dbnosql.Condition{Field: "verificationToken", Comparator: "==", Value: req.VerificationToken},
			&dbnosql.Condition{Field: "validatedUntil", Comparator: "==", Value: nil})
		list, err := onboardingTrackersColl.Find(q)
		if err != nil {
			return err // HTTP 500
		}

		// Verify correctness
		if len(list) != 1 {
			return web.Error(c, common.ErrorCodeSMSCodeIncorrect, "Tracking not found/expired")
		}
		existing := list[0].(*OnboardingTracker)
		if existing.OneTimePassword != req.OneTimePassword || existing.PhoneNumber != intlPhoneNumber {
			// The OTP code as well as the phone number (repeated) must match the previous tracker
			return web.Error(c, common.ErrorCodeSMSCodeIncorrect, "Incorrect OTP code")
		}

		// Verified: mark the tracker as validated for the next 6 hours during which period, the user must complete the
		// registration process, or he/she will have to re-verify phone number.
		// The same verificationToken will need to be provided during the registration process and the phone number will
		// have to also match this tracker.
		in6hours := time.Now().Add(6 * time.Hour)
		existing.ValidatedUntil = &in6hours
		existing.OneTimePassword = ""
		if err = onboardingTrackersColl.UpdateById(existing.ID, existing); err != nil {
			return err // HTTP 500
		}

		return web.OK(c)
	})

	// Log in, only if the phone number has already been verified
	e.POST("/auth/sign-in", func(c echo.Context) error {
		L := logging.L.Named("auth-sign-in")

		// JSON unmarshalling
		req := struct {
			EmailOrPhoneNumber string `json:"emailOrPhoneNumber" validate:"required"`
			Password           string `json:"password"           validate:"required,min=8"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			L.Warn("Invalid request", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		badAuthMsg := map[string]interface{}{
			"error": "Invalid login",
			"code":  common.ErrorCodeBadAuth,
		}

		id := req.EmailOrPhoneNumber
		if !strings.Contains(req.EmailOrPhoneNumber, "@") {
			// Get a standardized and validated phone number
			intlPhoneNumber, err := web.GetInternationalPhoneNumber(c, busrpc, req.EmailOrPhoneNumber)
			if err != nil {
				L.Warn("Failed to get the International phone number", zap.Error(err), zap.String("emailOrPhoneNumber", req.EmailOrPhoneNumber))
				return common.ReturnInvalidPhoneNumberResponse(c)
			}
			id = intlPhoneNumber
		}

		// Verify on the database that the user exists, and retrieve the encrypted password
		q := accountsColl.GetQueryBuilder()
		_ = q.SetOr(
			&dbnosql.Condition{Field: "email", Comparator: "==", Value: id},
			&dbnosql.Condition{Field: "phoneNumber", Comparator: "==", Value: id})
		docs, err := accountsColl.Find(q)
		if err != nil {
			return err // HTTP 500
		}

		if len(docs) == 0 {
			L.Warn("User not found", zap.String("emailOrPhoneNumber", req.EmailOrPhoneNumber))
			return c.JSON(http.StatusUnauthorized, badAuthMsg)
		}
		account := docs[0].(*accounts.Account)

		// Needs a phone verification
		if !account.IsPhoneNumberVerified {
			return c.JSON(http.StatusUnauthorized, map[string]interface{}{
				"accountId": account.ID,
				"error":     "You need to verify your phone number",
				"code":      common.ErrorCodeRequirePhoneVerification,
			})
		}

		// Verify the password
		if err := bcrypt.CompareHashAndPassword([]byte(account.HashedPassword), []byte(req.Password)); err != nil {
			L.Warn("Invalid password", zap.Error(err))
			return c.JSON(http.StatusUnauthorized, badAuthMsg)
		}

		// Block login if the account is blocked
		if account.IsBlocked() {
			L.Warn("Account is blocked", zap.String("accountId", account.ID))
			return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeAccountBlocked, "Your account is blocked")
		}

		if err = recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_AccountSignIn); err != nil {
			return err // HTTP 500
		}

		pjson, err := account.ToMobileJSON()
		if err != nil {
			return err // HTTP 500
		}

		// Check if this user needs to do a 2FA verification (after 90 days from the last 2FA)
		if account.Is2FARequired() {
			// Generate a JWT with a very short lifetime
			tokenString, err := account.GenerateJWTToken(accountsColl, jwt2FATemporaryTokenDuration)
			if err != nil {
				return err // HTTP 500
			}

			L.Warn("2FA required after 90 days from the last 2FA", zap.String("accountId", account.ID))
			return c.JSON(http.StatusOK, map[string]interface{}{
				"token":       tokenString,
				"account":     json.RawMessage(pjson),
				"2faRequired": true,
			})
		}

		// Check if this user needs to do a SCA verification (after 180 days from the last SCA)
		if account.IsSCARequired() {
			// Generate a JWT with a very short lifetime
			tokenString, err := account.GenerateJWTToken(accountsColl, jwtSCATemporaryTokenDuration)
			if err != nil {
				return err // HTTP 500
			}

			L.Warn("SCA required after 180 days from the last SCA", zap.String("accountId", account.ID))
			return c.JSON(http.StatusOK, map[string]interface{}{
				"token":       tokenString,
				"account":     json.RawMessage(pjson),
				"scaRequired": true,
			})
		}

		// Generate a JWT
		tokenString, err := account.GenerateJWTToken(accountsColl, jwtTokenDuration)
		if err != nil {
			return err // HTTP 500
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"token":   tokenString,
			"account": json.RawMessage(pjson),
		})
	})

	// Refresh token, before the previous one expires
	e.GET("/auth/refresh-token/:accountId", func(c echo.Context) error {
		L := logging.L.Named("auth-refresh-token")

		account := c.Get("account").(*accounts.Account)

		// Block login if the account is blocked
		if account.IsBlocked() {
			L.Warn("Account is blocked", zap.String("accountId", account.ID))
			return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeAccountBlocked, "Your account is blocked")
		}

		// Verify if the account needs to do a 2FA verification
		if account.Is2FARequired() {
			// Generate a JWT with a very short lifetime
			tokenString, err := account.GenerateJWTToken(accountsColl, jwt2FATemporaryTokenDuration)
			if err != nil {
				return err // HTTP 500
			}

			L.Warn("2FA required after 90 days from the last 2FA", zap.String("accountId", account.ID))
			return c.JSON(http.StatusUnauthorized, map[string]interface{}{
				"token":       tokenString,
				"account":     account,
				"2faRequired": true,
			})
		}

		// Verify if the account needs to do a SCA verification
		if account.IsSCARequired() {
			// Generate a JWT with a very short lifetime
			tokenString, err := account.GenerateJWTToken(accountsColl, jwtSCATemporaryTokenDuration)
			if err != nil {
				return err // HTTP 500
			}

			L.Warn("SCA required after 180 days from the last SCA", zap.String("accountId", account.ID))
			return c.JSON(http.StatusUnauthorized, map[string]interface{}{
				"token":       tokenString,
				"account":     account,
				"scaRequired": true,
			})
		}

		tokenString, err := account.GenerateJWTToken(accountsColl, jwtTokenDuration)
		if err != nil {
			return err // HTTP 500
		}

		return web.MapOfMobileJsoner(c, map[string]interface{}{
			"token":   tokenString,
			"account": account,
		})
	}, accounts.MiddlewareRetrieveAccount,
		// Specialized middleware that verifies the JWT token, but still allows the refresh if the signature of the given
		// JWT matches the last one issued for the account, even if the JWT is expired.
		// This allows the user to refresh the token, even after a while without logging in. FaceID will likely work.
		func(next echo.HandlerFunc) echo.HandlerFunc {
			return func(c echo.Context) error {
				L := logging.L.Named("auth-refresh-token-middleware")

				accountMatchingURL := c.Get("account").(*accounts.Account)

				ra := c.Request().Header.Get("Authorization")
				splits := strings.Split(ra, " ")
				if len(splits) < 2 {
					L.Warn("Authorization header is invalid")
					return common.ReturnInvalidJWTTokenResponse(c)
				}

				// Get information from the token
				token := strings.TrimSpace(splits[len(splits)-1])
				jtoken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
					return accounts.JwtSecret, nil
				})
				if err != nil {
					var jerr *jwt.ValidationError
					if ok := errors.As(err, &jerr); ok {
						switch jerr.Errors {
						case jwt.ValidationErrorSignatureInvalid, jwt.ValidationErrorMalformed, jwt.ValidationErrorUnverifiable:
							return common.ReturnInvalidJWTTokenResponse(c)
						case jwt.ValidationErrorExpired:
							// Can continue
						default:
							return common.ReturnInvalidJWTTokenResponse(c)
						}
					} else {
						return common.ReturnInvalidJWTTokenResponse(c)
					}
				}
				claims := jtoken.Claims.(jwt.MapClaims)
				realAccountId := claims["sub"].(string)
				if realAccountId != accountMatchingURL.ID {
					L.Warn("JWT token is not owned by the account", zap.String("offenderAccountId", realAccountId), zap.String("targetAccountId", accountMatchingURL.ID))
					return common.ReturnInvalidJWTTokenResponse(c)
				}

				if !jtoken.Valid {
					L.Info("JWT token is expired?", zap.Any("claims", claims))

					// We only allow refresh to happen if the token is not older than 30 days
					if time.Since(time.Unix(int64(claims["exp"].(float64)), 0)) > 30*24*time.Hour {
						L.Warn("JWT token is too old")
						return common.ReturnInvalidJWTTokenResponse(c)
					}

					// Get the signature of the current token
					token := strings.TrimSpace(splits[len(splits)-1])
					if len(token) == 0 {
						L.Warn("JWT token is empty")
						return common.ReturnInvalidJWTTokenResponse(c)
					}
					hasher := sha256.New()
					hasher.Write([]byte(token))
					latestSHASUM256 := hex.EncodeToString(hasher.Sum(nil))

					// Verify if the provided JWT is the last one
					if latestSHASUM256 != accountMatchingURL.LastJWTSignature {
						// Not allowed to be renewed because it's not the last issued token
						L.Warn("JWT token is not the last one", zap.String("accountId", accountMatchingURL.ID))
						return common.ReturnInvalidJWTTokenResponse(c)
					}
				}

				// Set the accountId in the context
				c.Set("accountId", accountMatchingURL.ID)

				return next(c)
			}
		})

	e.POST("/auth/check-password-entropy", func(c echo.Context) error {
		req := struct {
			Utf8Password string `json:"utf8Password"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return common.ReturnInvalidAPICallResponse(c)
		}

		perr := PasswordSecurityIsAcceptable(req.Utf8Password)
		entropy := GetPasswordEntropy(req.Utf8Password)

		return c.JSON(http.StatusOK, map[string]interface{}{
			"entropy":      entropy,
			"isAcceptable": perr == nil,
		})
	})

	// Sign-up for a new account - it still requires a phone number verification (which should be the next step)
	e.POST("/auth/sign-up", func(c echo.Context) error {
		L := logging.L.Named("auth-sign-up")
		L.Info("Sign-up request started ...")
		defer L.Info("Sign-up request ended")

		// JSON unmarshalling
		req := struct {
			Email               string `json:"email"                validate:"required,email"    mod:"lcase"` // Unique
			PhoneNumber         string `json:"phoneNumber"          validate:"required,min=6"    mod:"trim"`  // Unique; Starts with + (country code) (phone number)
			Password            string `json:"password"             validate:"required,min=10"`
			Password2           string `json:"password2"            validate:"required,min=10"`
			DesiredReferralCode string `json:"desiredReferralCode"  validate:"min=2,max=10"      mod:"trim"`
			ParentReferralCode  string `json:"parentReferralCode"   validate:"max=10"            mod:"trim"`

			ReceiveYochbeeNewsByEmail          bool `json:"receiveYochbeeNewsByEmail"`
			ReceiveYochbeeSpecialOffersByEmail bool `json:"receiveYochbeeSpecialOffersByEmail"`

			VerifiedPhoneNumberToken string `json:"verifiedPhoneNumberToken"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			L.Warn("Invalid request", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		if req.Password2 != req.Password {
			L.Warn("Both passwords must match")
			return c.JSON(http.StatusOK, map[string]interface{}{
				"error": "Both passwords must match",
				"code":  common.ErrorCodeUnmatchedPasswords,
			})
		}

		// Check the strength of the password
		if err := PasswordSecurityIsAcceptable(req.Password); err != nil {
			switch err {
			case ErrorPasswordTooWeak:
				L.Warn("Password is too weak", zap.Error(err))
				return c.JSON(http.StatusOK, map[string]interface{}{
					"error": "Password is too weak",
					"code":  common.ErrorCodeWeakPassword,
				})
			default:
				L.Warn("Password is not acceptable", zap.Error(err))
				return c.JSON(http.StatusOK, map[string]interface{}{
					"error": "Password is not acceptable",
					"code":  common.ErrorCodeUnacceptablePassword,
				})
			}
		}

		// Get a standardized and validated phone number
		intlPhoneNumber, err := web.GetInternationalPhoneNumber(c, busrpc, req.PhoneNumber)
		if err != nil {
			L.Warn("Invalid phone number", zap.Error(err), zap.String("phoneNumber", req.PhoneNumber))
			return common.ReturnInvalidPhoneNumberResponse(c)
		}

		// Verify that there is no other user that already has this username / email / phone number / referral code
		q := accountsColl.GetQueryBuilder()
		conditions := []*dbnosql.Condition{
			{Field: "email", Comparator: "==", Value: req.Email},
			{Field: "phoneNumber", Comparator: "==", Value: intlPhoneNumber},
		}
		if req.DesiredReferralCode != "" {
			conditions = append(conditions, &dbnosql.Condition{Field: "referralCode", Comparator: "==", Value: req.DesiredReferralCode})
		}
		_ = q.SetOr(conditions...)
		docs, err := accountsColl.Find(q)
		if err != nil {
			return err // HTTP 500
		}
		if len(docs) >= 1 {
			L.Warn("Account already exists", zap.Error(err), zap.String("email", req.Email), zap.String("phoneNumber", intlPhoneNumber))
			return c.JSON(http.StatusOK, map[string]interface{}{
				"error": "Account already exists",
				"code":  common.ErrorCodeUsernameAlreadyExists,
			})
		}

		// Verify that the phone number was really verified within the 6 hours period given
		if abort, err := ensurePhoneNumberHasBeenVerified(c, intlPhoneNumber, req.VerifiedPhoneNumberToken); err != nil || abort {
			return err // HTTP 500 or Error message in this particular case
		}

		// Prepare the new Account
		personalTopic, err := random.CryptoStrings(13)
		if err != nil {
			return err // HTTP 500
		}
		account := &accounts.Account{
			ID:                    accountsColl.GenerateUniqueId(),
			Email:                 req.Email,
			PhoneNumber:           intlPhoneNumber,
			IsPhoneNumberVerified: true,

			// from the initial IP address of the registration
			IpAddressCountryIso3166: web.GetCloudflareCountryCode(c),

			// Initial preferences
			Preferences: &accounts.Preferences{
				ReceiveYochbeeNewsByEmail:          req.ReceiveYochbeeNewsByEmail,
				ReceiveYochbeeSpecialOffersByEmail: req.ReceiveYochbeeSpecialOffersByEmail,
			},

			PersonalTopic: personalTopic,

			CreatedAt: time.Now(),
		}
		if err = account.SetNewPassword(req.Password); err != nil {
			return err // HTTP 500
		}
		if req.DesiredReferralCode != "" {
			// This is out first chance to set this. The other one is during profile update.
			account.ReferralCode = req.DesiredReferralCode
		}
		req.ParentReferralCode = strings.ToUpper(strings.TrimSpace(req.ParentReferralCode))
		if req.ParentReferralCode != "" {
			// Find the account matching the parent referral code
			parentAccount, err := dbnosql.GetOneByField[*accounts.Account](accountsColl, "referralCode", req.ParentReferralCode)
			if err != nil {
				return err // HTTP 500
			}
			if parentAccount != nil {
				account.RefererParentAccountId = parentAccount.ID
				logging.L.Info("Parent account has referred a new user", zap.String("parentAccountId", parentAccount.ID), zap.String("newAccountId", account.ID))
			} else {
				logging.L.Warn("Parent referral code used, but the parent account does not exist", zap.String("referralCode", req.ParentReferralCode), zap.String("newAccountId", account.ID))
			}
		}

		// Save the new user account on the DB
		if _, err = accountsColl.Insert(account); err != nil {
			msg := err.Error()
			if strings.Contains(msg, "duplicate key error") {
				if strings.Contains(msg, "referralCode:") {
					L.Warn("Referral code already taken", zap.Error(err))
					return web.Error(c, common.ErrorCodeReferralCodeAlreadyTaken, "Referral code already taken")
				}
			}
			return err // HTTP 500
		}

		if err = recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_AccountRegistration); err != nil {
			return err // HTTP 500
		}

		// Create support data
		if _, err := busrpc.CallM(kyc.Method_CreateProgressForAccount, &kyc.CreateProgressForAccountRequest{
			Account: account,
		}, 5*time.Second); err != nil {
			return err // HTTP 500
		}

		// Send back an initial JWT token
		initialToken, err := account.GenerateJWTToken(nil, jwtTokenDuration)
		if err != nil {
			return err // HTTP 500
		}

		L.Info("Account created", zap.String("accountId", account.ID))
		return c.JSON(http.StatusOK, map[string]interface{}{
			"accountId": account.ID,
			"token":     initialToken,
		})
	})

	e.PUT("/account/profile-update/:accountId", func(c echo.Context) error {
		L := logging.L.Named("profile-update")

		// Check if this account is already KYC refused permanently
		progress := c.Get("progress").(*kyc.Progress)
		if progress.IsKYCPermanentlyRefused() {
			return web.Error(c, common.ErrorCodeKYCCannotStartPermanently, "KYC refused permanently")
		}

		req := struct {
			FirstName           string          `json:"firstName"           validate:"required,min=2" mod:"trim"`
			LastName            string          `json:"lastName"            validate:"required,min=2" mod:"trim"`
			PhysicalAddress     string          `json:"physicalAddress"     validate:"required,min=5" mod:"trim"`
			ExtraAddressInfo    string          `json:"extraAddressInfo"                              mod:"trim"`
			PostalCode          string          `json:"postalCode"          validate:"required,min=2" mod:"trim"`
			City                string          `json:"city"                validate:"required,min=2" mod:"trim"`
			Country             string          `json:"country"             validate:"required,min=2" mod:"trim"`
			CountryCode         string          `json:"countryCode"         validate:"max=2"          mod:"trim"`
			Gender              string          `json:"gender"              validate:"required,min=1" mod:"trim"`
			Birthday            json2.BirthDate `json:"birthday"            validate:"required"`
			DesiredReferralCode string          `json:"desiredReferralCode" validate:"max=10" mod:"trim"`

			Email             string `json:"email" validate:"omitempty,email" mod:"lcase"` // optional
			PhoneNumber       string `json:"phoneNumber"                      mod:"trim"`  // optional
			VerificationToken string `json:"verificationToken"`                            // optional

			ReceiveYochbeeNewsByEmail          bool `json:"receiveYochbeeNewsByEmail"          validate:"required"`
			ReceiveYochbeeSpecialOffersByEmail bool `json:"receiveYochbeeSpecialOffersByEmail" validate:"required"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			L.Error("Invalid request", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Retrieve the existing account
		account := c.Get("account").(*accounts.Account)

		// Special treatment for fields that are sensitive: require a re-validation
		npn := strings.TrimSpace(req.PhoneNumber)
		if npn != "" {
			// Get a standardized and validated phone number
			intlPhoneNumber, err := web.GetInternationalPhoneNumber(c, busrpc, req.PhoneNumber)
			if err != nil {
				if allowAccountsDeletion {
					// It is allowed for the user to send in an invalid phone number, so that they can delete their account afterwards
					intlPhoneNumber = npn
				} else {
					L.Error("Invalid phone number", zap.Error(err))
					return common.ReturnInvalidPhoneNumberResponse(c)
				}
			}

			// Phone number really changed
			if account.PhoneNumber != intlPhoneNumber {
				if !allowAccountsDeletion {
					// Verify that the phone number was really verified within the 6 hours period given
					if abort, err := ensurePhoneNumberHasBeenVerified(c, intlPhoneNumber, req.VerificationToken); err != nil || abort {
						L.Error("Unverified phone number", zap.Error(err),
							zap.String("newIntlPhoneNumber", intlPhoneNumber),
							zap.String("oldPhoneNumber", account.PhoneNumber),
							zap.String("verificationToken", req.VerificationToken))
						return err // HTTP 500 or Error message in this particular case
					}

					account.PhoneNumber = intlPhoneNumber
				} else {
					account.PhoneNumber = intlPhoneNumber
				}
			}
		}

		// Update (just in-memory for now)
		if allowAccountsDeletion && strings.TrimSpace(req.Email) != "" {
			newEmail := strings.TrimSpace(req.Email)
			account.Email = newEmail
		}
		account.FirstName = req.FirstName
		account.LastName = req.LastName
		account.PhysicalAddress = req.PhysicalAddress
		account.ExtraAddressInfo = req.ExtraAddressInfo
		account.PostalCode = req.PostalCode
		account.City = req.City
		account.Country = req.Country
		account.CountryCode = req.CountryCode
		account.Gender = req.Gender
		account.Preferences.ReceiveYochbeeNewsByEmail = req.ReceiveYochbeeNewsByEmail
		account.Preferences.ReceiveYochbeeSpecialOffersByEmail = req.ReceiveYochbeeSpecialOffersByEmail
		bd := time.Time(req.Birthday)
		account.Birthday = &bd
		now := time.Now()
		account.UpdatedAt = &now

		// If account is not yet associated with Treezor, require the user to do a 2FA verification, or it's a new account (less than 2 hours from creation)
		if account.TreezorUserId == 0 || account.TreezorPrimaryWalletId == 0 || account.TreezorSCAWalletId == "" || account.CreatedAt.Add(2*time.Hour).After(time.Now()) {
			L.Info("Account is not Treezor associated yet, changes require 2FA token to be present")
			if responded, _, err := account.HasVerified2FATokenFromRequest(L, c); err != nil || responded {
				return err // HTTP 500 or Error message in this particular case
			}
		}

		// ReferralCode processing ...
		if req.DesiredReferralCode != "" && req.DesiredReferralCode != account.ReferralCode {
			// This is our second chance to set this. The other one is during registration.
			account.ReferralCode = req.DesiredReferralCode
		}
		// ... it is kept as it was if it was already set, otherwise, one will be automatically set
		if account.ReferralCode == "" {
			// At this point, if the referral code is still empty, we need to generate one
			for {
				account.ReferralCode = accounts.GenerateReferralCode(account.FirstName, account.LastName)
				existing, err := accountsColl.GetByReferralCode(account.ReferralCode)
				if err != nil {
					L.Error("Error while checking existing account by referral code", zap.Error(err))
					return err // HTTP 500
				}
				if existing == nil {
					break
				}
			}
		}

		// Synchronize the information with Treezor
		if account.TreezorUserId == 0 {
			// Create an account on Treezor
			rresp, err := busrpc.CallM(treezor.Method_CreateUser, account, 20*time.Second)
			if err != nil {
				L.Error("Error while creating the Treezor user", zap.Error(err))
				return err // HTTP 500
			}
			resp := treezor.CreateUserResponse{}
			if err = json.Unmarshal(rresp, &resp); err != nil {
				L.Error("Error while unmarshalling the Treezor user creation response", zap.Error(err))
				return err // HTTP 500
			}
			// Associate with the Treezor account ID
			account.TreezorUserId = resp.TreezorUserId
			if err = accountsColl.UpdateById(account.ID, account); err != nil {
				L.Error("Error while updating the account with the Treezor user ID", zap.Error(err))
				return err // HTTP 500
			}

			if err = recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_TreezorUserCreated); err != nil {
				L.Error("Error while recording the IP address for the Treezor user creation event", zap.Error(err))
				return err // HTTP 500
			}
		} else {
			if _, err := busrpc.CallM(treezor.Method_UpdateUser, account, 20*time.Second); err != nil {
				L.Error("Error while updating the Treezor user", zap.Error(err))
				return err // HTTP 500
			}
		}

		// Create a wallet on Treezor
		if account.TreezorPrimaryWalletId == 0 {
			rr, err := busrpc.CallM(treezor.Method_CreateWallet, account, 20*time.Second)
			if err != nil {
				L.Error("Error while creating the Treezor wallet", zap.Error(err))
				return err // HTTP 500
			}
			response := treezor.CreateWalletResponse{}
			if err = json.Unmarshal(rr, &response); err != nil {
				L.Error("Error while unmarshalling the Treezor wallet creation response", zap.Error(err))
				return err // HTTP 500
			}

			// Associate the Wallet ID (which is the primary) to the account
			// Can the user have multiple wallets, a question for another day ;) I don't know yet, what's the point?
			account.TreezorPrimaryWalletId = response.WalletId
			if err = accountsColl.UpdateById(account.ID, account); err != nil {
				L.Error("Error while updating the account with the Treezor wallet ID", zap.Error(err))
				return err // HTTP 500
			}

			if err = recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_TreezorWalletCreated); err != nil {
				L.Error("Error while recording the IP address for the Treezor wallet creation event", zap.Error(err))
				return err // HTTP 500
			}
		}

		// Update on the DB
		if err := accountsColl.UpdateById(account.ID, account); err != nil {
			msg := err.Error()
			if strings.Contains(msg, "duplicate key error") {
				if strings.Contains(msg, "referralCode:") {
					L.Error("Referral code already taken", zap.Error(err))
					return web.Error(c, common.ErrorCodeReferralCodeAlreadyTaken, "Referral code already taken")
				} else if strings.Contains(msg, "email:") {
					L.Error("Email already taken", zap.Error(err))
					return web.Error(c, common.ErrorCodeEmailAlreadyTaken, "Email already taken")
				}
			}
			return err // HTTP 500
		}

		if err := recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_AccountUpdate); err != nil {
			L.Error("Error while recording the IP address for the account update event", zap.Error(err))
			return err // HTTP 500
		}

		// Sync the Treezor User ID
		if _, err := bus2.CallMethodMU[kyc.UpdateProgressForAccountResponse](busrpc, kyc.Method_UpdateProgressForAccount, &kyc.UpdateProgressForAccountRequest{
			Account: account,
		}, 5*time.Second); err != nil {
			L.Error("Error while updating the KYC progress for the account", zap.Error(err), zap.Any("account", account))
			return err // HTTP 500
		}

		pjson, err := account.ToMobileJSON()
		if err != nil {
			L.Error("Error while marshalling the response to JSON", zap.Error(err))
			return err // HTTP 500
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"account": json.RawMessage(pjson),
		})
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId, treezor.MiddlewareRequireAndCacheSCAProof)

	e.POST("/account/reset-password-step1", func(c echo.Context) error {
		L := logging.L.Named("reset-password-step1").With(zap.String("ip", c.RealIP()))

		req := struct {
			Email string `json:"email" validate:"required,email"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			L.Warn("Invalid request", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		account, err := dbnosql.GetOneByField[*accounts.Account](accountsColl, "email", req.Email)
		if err != nil {
			return err // HTTP 500
		}
		if account == nil {
			L.Warn("Account not found (OK response sent for security)", zap.String("email", req.Email))
			return web.OK(c) // always return success, even if the account does not exist
		}
		L = L.With(zap.String("accountId", account.ID))

		// We only allow a user to request a password reset once every minute
		if account.LastPasswordResetRequested != nil && time.Since(*account.LastPasswordResetRequested) < time.Minute {
			L.Warn("User tried to reset password too often", zap.String("email", req.Email))
			return web.Error(c, common.ErrorCodePasswordResetEmailRecentlySent, "Password reset email recently sent")
		}

		if err = recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_AccountPasswordResetRequested); err != nil {
			return err // HTTP 500
		}

		// Prepare an email with an OTP code to be sent to the user
		otpCode, err := random.GenerateSecureNumberSequence(8)
		if err != nil {
			return err // HTTP 500
		}
		emailBody := "\n\nVoici votre code de réinitialisation de mot de passe : " + otpCode + "\n\n"
		if _, err = bus2.CallMethodMU[notifications.SendEmailNoReplyResponse](busrpc, notifications.Method_SendEmailNoReply, &notifications.SendEmailNoReplyRequest{
			ToEmailAddresses: []string{req.Email},
			Subject:          "Yochbee - Réinitialisation de votre mot de passe",
			Body:             emailBody,
			ContentType:      "text/plain",
		}, 20*time.Second); err != nil {
			return err // HTTP 500
		}

		// Store the OTP code in the DB for reverification
		now := time.Now()
		account.PasswordResetOTPCode = otpCode
		account.LastPasswordResetRequested = &now
		if err = accountsColl.UpdateById(account.ID, account); err != nil {
			return err // HTTP 500
		}

		// Debug
		if config.IsDebugMode() {
			L.Info("DEBUG: OTP code sent to email address", zap.String("otpCode", otpCode))
		}

		return web.OK(c)
	})

	e.POST("/account/reset-password-step2", func(c echo.Context) error {
		L := logging.L.Named("reset-password-step2").With(zap.String("ip", c.RealIP()))

		req := struct {
			Email        string `json:"email" validate:"required,email"`
			OTPCode      string `json:"otpCode" validate:"required"`
			NewPassword  string `json:"newPassword" validate:"required"`
			NewPassword2 string `json:"newPassword2" validate:"required"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			L.Warn("Invalid API call", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		if req.NewPassword != req.NewPassword2 {
			L.Warn("Passwords do not match")
			return web.Error(c, common.ErrorCodeUnmatchedPasswords, "Passwords do not match")
		}
		account, err := dbnosql.GetOneByField[*accounts.Account](accountsColl, "email", req.Email)
		if err != nil {
			return err // HTTP 500
		}
		if account == nil {
			L.Warn("Account not found, but OK response was sent for security", zap.String("email", req.Email))
			return web.OK(c) // always return success, even if the account does not exist
		}
		L = L.With(zap.String("accountId", account.ID))

		// Verify that the OTP code matches the one in the DB
		key := "accounts:passwordResetOtpFailedAttemptsCount:" + account.ID
		if account.PasswordResetOTPCode != req.OTPCode {
			// Count the number of failed attempts (stored on Redis) in an atomic way
			pipe := cache.TxPipeline()
			incr := pipe.Incr(context.Background(), key)
			pipe.Expire(context.Background(), key, 24*time.Hour)
			if _, err = pipe.Exec(context.Background()); err != nil {
				return err // HTTP 500
			}

			// Block the account if there has been too many failed attempts
			if incr.Val() >= 5 {
				account.BlockedReason = "Too many failed password reset attempts"
				account.UpdatedAt = dbnosql.PNow()
				if err = accountsColl.UpdateById(account.ID, account); err != nil {
					return err // HTTP 500
				}

				L.Warn("Account blocked: too many incorrect OTP codes", zap.String("email", req.Email))
				return web.Error(c, common.ErrorCodeAccountBlocked, "Account has been blocked")
			}

			return web.Error(c, common.ErrorCodeUnmatchedPasswords, "Le code OTP est incorrect")
		}

		// Remove the failed attempts counter
		if _, err = cache.Del(context.Background(), key).Result(); err != nil {
			return err // HTTP 500
		}

		if err = recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_AccountPasswordReset); err != nil {
			return err // HTTP 500
		}
		if err = account.SetNewPassword(req.NewPassword); err != nil {
			return err // HTTP 500
		}
		account.PasswordResetOTPCode = ""
		account.LastPasswordResetRequested = nil
		if err = accountsColl.UpdateById(account.ID, account); err != nil {
			return err // HTTP 500
		}
		L.Info("Password reset successfully", zap.String("email", req.Email))
		return web.OK(c)
	})

	e.POST("/account/upload-profile-photo/:accountId", func(c echo.Context) error {
		// Ensure this isn't a KYC refused account
		progress := c.Get("progress").(*kyc.Progress)
		if progress.IsKYCPermanentlyRefused() {
			return web.Error(c, common.ErrorCodeKYCCannotStartPermanently, "KYC was refused permanently")
		}

		account := c.Get("account").(*accounts.Account)

		// Get the file and upload to Azure Storage with a random name
		purl, err := webgobase.UploadImageToAzureBlob(c, abs, "profile-photos", "photo", 10*1024*1024)
		if err != nil {
			return err // HTTP 500
		}

		// Delete the old photo to reclaim space
		if account.ProfilePhotoUrl != "" {
			if err = abs.DeleteByUrl(context.Background(), account.ProfilePhotoUrl); err != nil {
				// Don't fail because of this error
				logging.L.Warn("Failed to delete old profile photo",
					zap.String("accountId", account.ID), zap.String("old-url", account.ProfilePhotoUrl), zap.Error(err))
			}
		}

		// Update the account with the new profile photo
		now := time.Now()
		account.ProfilePhotoUrl = purl
		account.UpdatedAt = &now
		if err = accountsColl.UpdateById(account.ID, account); err != nil {
			return err // HTTP 500
		}
		return web.SingleEntityForMobile(c, account, "account")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId)

	e.PUT("/account/offer-update/:accountId", func(c echo.Context) error {
		// Verify that it's really the actual user with the right JWT token
		accountId := strings.TrimSpace(c.Param("accountId"))
		if !accounts.IsRequestReallyFromUser(c, accountId) {
			return common.ReturnInvalidJWTTokenResponse(c)
		}

		req := struct {
			OfferType string `json:"offerType" validate:"required,oneof=classic student"`
			Period    string `json:"period" validate:"required,oneof=annual monthly"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get the account from the context (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		// Update
		account.CurrentOfferPeriod = req.Period
		account.CurrentOfferType = req.OfferType
		account.UpdatedAt = dbnosql.PNow()
		if err := accountsColl.UpdateById(accountId, account); err != nil {
			return err // HTTP 500
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.GET("/account/info/:accountId", func(c echo.Context) error {
		// Verify that it's really the actual user with the right JWT token
		accountId := strings.TrimSpace(c.Param("accountId"))
		if !accounts.IsRequestReallyFromUser(c, accountId) {
			return common.ReturnInvalidJWTTokenResponse(c)
		}

		account_, err := accountsColl.GetById(accountId)
		if err != nil {
			return err // HTTP 500
		}
		account, ok := account_.(*accounts.Account)
		if !ok || account == nil {
			return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"account": account,
		})
	}, accounts.MiddlewareRequireAccountId)

	e.PUT("/account/kyc-info/:accountId", func(c echo.Context) error {
		L := logging.L.Named("kyc-info")

		input := treezor.DeclarativeInformationDTO{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &input); err != nil {
			L.Warn("Invalid JSON", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get the associated account
		account := c.Get("account").(*accounts.Account) // thanks to the middleware
		accountId := account.ID
		oldAccount := *account

		// If this user is blocked from KYC, don't allow them to update their KYC info
		progress := c.Get("progress").(*kyc.Progress)
		if progress.IsKYCPermanentlyRefused() {
			return web.Error(c, common.ErrorCodeKYCCannotStartPermanently, "KYC refused permanently")
		}

		// Ask the `treezor` microservice to send this update to Treezor
		if _, err := busrpc.CallM(treezor.Method_UpdateDeclarativeInformation, &treezor.UpdateDeclarativeInformationRequest{
			UserId: account.TreezorUserId,
			Info:   input,
		}, 10*time.Second); err != nil {
			return err // HTTP 500
		}

		// Update the account locally
		account.Title = input.Title
		account.PlaceOfBirth = input.PlaceOfBirth
		account.BirthCountry = input.BirthCountry
		account.Nationality = input.Nationality
		account.SpecifiedUSPerson = input.SpecifiedUSPerson
		account.FrenchTaxResident = input.FrenchTaxResident
		account.IncomeRange = input.IncomeRange
		account.PersonalAssets = input.PersonalAssets
		account.Occupation = input.Occupation

		// Update on the DB
		if err := accountsColl.UpdateById(accountId, account); err != nil {
			return err // HTTP 500
		}

		if err := recordIPAddressForEvent(c, ipLogsCollection, account.ID, IPLogEvent_AccountUpdate); err != nil {
			return err // HTTP 500
		}

		// Tell `kyc` microservice that this user is ready for KYC
		if _, err := bus2.CallMethodMU[bus2.BoolResult](busrpc, kyc.Method_UpdateDeclarativeInfoReadiness, &kyc.UpdateDeclarativeInfoReadinessRequest{
			OldAccount:     &oldAccount,
			UpdatedAccount: account,
			IsReady:        true,
		}, 5*time.Second); err != nil {
			return err // HTTP 500
		}

		pjson, err := account.ToMobileJSON()
		if err != nil {
			return err // HTTP 500
		}
		return c.JSON(http.StatusOK, map[string]interface{}{
			"account": json.RawMessage(pjson),
		})
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId)

	// -----------------------
	// 2FA implementation

	e.POST("/account/2fa/:accountId/start", func(c echo.Context) error {
		accountId := strings.TrimSpace(c.Param("accountId"))
		L := logging.L.Named("account.2fa.start").With(zap.String("accountId", accountId))
		L.Info("Starting ...")
		defer L.Info("Done")

		// Require the user to enter their password
		payload := struct {
			Password string `json:"password" validate:"required,min=8"`
		}{}
		readable, err := web.DebugPayload(L, c)
		if err != nil {
			return err // HTTP 500
		}
		if _, err := json2.ValidateBodyStrictInto(readable, &payload); err != nil {
			L.Warn("Invalid JSON", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		account := c.Get("account").(*accounts.Account) // via middleware

		// Verify the password
		if err := bcrypt.CompareHashAndPassword([]byte(account.HashedPassword), []byte(payload.Password)); err != nil {
			L.Warn("Invalid password", zap.Error(err))
			return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeBadAuth, "Invalid password")
		}

		// Generate a unique OTP secret (this is not a Time-based OTP)
		otp, err := random.GenerateSecureNumberSequence(8)
		if err != nil {
			return err // HTTP 500
		}

		// Record an encrypted secret in the DB (`accounts-2fa` collection that is not exposed to the outside world)
		if _, err := twoFAsColl.Set2FAForAccount(c.Get("accountId").(string), payload.Password, otp); err != nil {
			if errors.Is(err, accounts.Err2FATemporaryBlocked) {
				L.Warn("Too many attempts within the last hour", zap.String("accountId", c.Get("accountId").(string)))
				return web.Error(c, common.ErrorCode2FATemporaryBlocked, "Too many attempts, please try again later")
			}
			L.Error("Error while setting up encrypted 2FA", zap.Error(err))
			return err // HTTP 500
		}

		// Record the event
		if err := recordIPAddressForEvent(c, ipLogsCollection, accountId, IPLogEvent_Account2FAStart); err != nil {
			return err // HTTP 500
		}

		// Send the secret to the user via SMS
		if _, err := bus2.CallMethodMU[notifications.SendSMSRequest](busrpc, notifications.Method_SendSMS, &notifications.SendSMSRequest{
			ToPhoneNumber: account.PhoneNumber,
			Body:          fmt.Sprintf("Votre code de vérification YoChBee est : %s\n", otp),
		}, 10*time.Second); err != nil {
			L.Error("Error while sending SMS", zap.Error(err))
			return err // HTTP 500
		}

		// TODO: Record the IP address of the user
		L.Info("2FA started and SMS sent ✉️", zap.String("accountId", account.ID), zap.String("phoneNumber", account.PhoneNumber))

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.POST("/account/2fa/:accountId/verify", func(c echo.Context) error {
		accountId := strings.TrimSpace(c.Param("accountId"))
		L := logging.L.Named("account.2fa.verify").With(zap.String("accountId", accountId))
		L.Info("Starting ...")
		defer L.Info("Done")

		// Record the event
		if err := recordIPAddressForEvent(c, ipLogsCollection, accountId, IPLogEvent_Account2FAVerify); err != nil {
			return err // HTTP 500
		}

		// Require the user to enter the password + OTP code
		payload := struct {
			Password string `json:"password" validate:"required,min=8"`
			OTP      string `json:"otp"      validate:"required,min=8,max=8"`
		}{}
		readable, err := web.DebugPayload(L, c)
		if err != nil {
			return err // HTTP 500
		}
		if _, err := json2.ValidateBodyStrictInto(readable, &payload); err != nil {
			L.Warn("Invalid JSON", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Verify that the OTP code is correct
		tfaId, err := twoFAsColl.Challenge2FAForAccount(accountId, payload.Password, payload.OTP)
		if err != nil {
			if errors.Is(err, accounts.Err2FARequired) {
				return web.Error(c, common.ErrorCode2FARequired, "2FA required")
			}
			if errors.Is(err, accounts.Err2FAIncorrect) {
				return web.Error(c, common.ErrorCode2FAIncorrect, "OTP code incorrect")
			}
			L.Error("Error while verifying 2FA", zap.Error(err))
			return err // HTTP 500
		}

		// Generate a new JWT token with the `2fa` claim set to `true`
		account := c.Get("account").(*accounts.Account) // via middleware
		jwt, err := account.Generate2FAJWTToken(cache, nil)
		if err != nil {
			L.Error("Error while generating 2FA JWT token", zap.Error(err))
			return err // HTTP 500
		}

		// Update the account with the timestamp of the last 2FA verification
		account.Last2FATime = dbnosql.PNow()
		if err := accountsColl.TouchLast2FATime(account.ID, account.Last2FATime); err != nil {
			L.Error("Error while updating account's last 2FA time", zap.Error(err))
			return err // HTTP 500
		}

		L.Info("2FA verified 👍", zap.String("tfaId", tfaId))

		// Send the new JWT token to the user
		return web.MapOfMobileJsoner(c, map[string]interface{}{
			"jwt2fa":  jwt,
			"account": account,
		})
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// -----------------------
	// Preferences

	e.PUT("/account/preferences/:accountId", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account) // via middleware
		L := logging.L.Named("account.preferences").With(zap.String("accountId", account.ID))

		// Read the payload
		payload := struct {
			Preferences *accounts.Preferences `json:"preferences"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &payload); err != nil {
			L.Warn("Invalid JSON", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Update the account preferences
		if err := accountsColl.UpdatePreferences(account.ID, payload.Preferences); err != nil {
			return err // HTTP 500
		}

		// Update the account (in memory)
		account.Preferences = payload.Preferences
		return web.SingleEntityForMobile(c, account, "account")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// --------------------------------------------------------------------------------------------

	// Integration of the Treezor's SCA mechanism
	//

	e.POST("/account/sca/:accountId/check-wallet", func(c echo.Context) error {
		accountId := strings.TrimSpace(c.Param("accountId"))
		L := logging.L.Named("account.sca.start").With(zap.String("accountId", accountId))
		L.Info("Starting ...")
		defer L.Info("Done")

		// Get the associated account (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		// Ensure that the account is already connected to Treezor
		if account.TreezorUserId == 0 {
			L.Warn("Account is not yet connected to Treezor")
			return web.Error(c, common.ErrorCodeAccountNotConnectedToTreezor, "Account not yet connected to Payment provider, please update the profile")
		}

		// Require the user to enter their password
		payload := struct {
			WalletTag string `json:"walletTag" validate:"required"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &payload); err != nil {
			L.Warn("Invalid JSON", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get the SCA Wallet information from Treezor (a fresh copy)
		var wallet *treezor.SCAWallet
		rr1, err := treezor.GetFreshSCAWalletByTreezorId(busrpc, account.TreezorSCAWalletId)
		if err != nil {
			L.Error("Error while getting the fresh Treezor SCA wallet", zap.Error(err), zap.Any("account", account))
			return err
		}
		// Sometimes, the wallet is deleted without our knowledge, so we need to handle this case
		if rr1 == nil || rr1.TreezorSCAWallet == nil {
			L.Warn("SCA Wallet not found on Treezor, creating a new one", zap.Any("account", account))
			wallet = nil
		} else {
			wallet = rr1.TreezorSCAWallet
		}

		if wallet != nil {
			// Already activated
			if wallet.Status == "ACTIVE" {
				// Return the SCA Wallet information
				return web.MapOfMobileJsoner(c, map[string]interface{}{
					"scaWallet":     wallet,
					"treezorUserId": account.TreezorUserId,
				})
			}

			// Already created, but needs activation on the Mobile side
			// - but only if the Activation Code is not yet expired
			if wallet.Status == "CREATED" {
				if wallet.ActivationCodeExpiryDate.Add(5 * time.Minute).After(time.Now()) {
					// Return the SCA Wallet information, it's still valid
					return web.MapOfMobileJsoner(c, map[string]interface{}{
						"scaWallet":     wallet,
						"treezorUserId": account.TreezorUserId,
					})
				}

				// Delete this SCA Wallet before creating a new one below
				if _, err := treezor.DeleteSCAWallet(busrpc, account.TreezorSCAWalletId); err != nil {
					// If the error message contains "already deleted", it's OK
					if !strings.Contains(err.Error(), "already deleted") {
						L.Error("Error while deleting the Treezor SCA wallet", zap.Error(err), zap.Any("account", account))
						return err // HTTP 500
					} else {
						L.Warn("SCA Wallet was already deleted", zap.Error(err), zap.Any("account", account))
					}
				} else {
					L.Info("Deleted the Treezor SCA wallet", zap.String("treezorWalletId", account.TreezorSCAWalletId), zap.Any("account", account))
				}
			}

			// It is deleted
			if wallet.Status == "DELETED" {
				L.Warn("SCA Wallet deleted on Treezor, creating a new one", zap.Any("account", account))
				wallet = nil
			}
		}

		// Create an SCA Wallet on Treezor
		rr, err := treezor.CreateSCAWalletForUser(busrpc, account.TreezorUserId, payload.WalletTag)
		if err != nil {
			L.Error("Error while creating the Treezor SCA wallet", zap.Error(err))
			return err // HTTP 500
		}
		wallet = rr.TreezorSCAWallet

		// Update on the DB
		account.TreezorSCAWalletId = rr.TreezorSCAWallet.TreezorId
		account.UpdatedAt = dbnosql.PNow()
		if err := accountsColl.UpdateById(account.ID, account); err != nil {
			L.Error("Error while updating the account with the Treezor SCA wallet ID", zap.Error(err))
			return err // HTTP 500
		}

		// Return the SCA Wallet, including the activation code
		return web.MapOfMobileJsoner(c, map[string]interface{}{
			"scaWallet":     wallet,
			"treezorUserId": account.TreezorUserId,
		})
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// Exchange sca-proof for a JWT (specific) token
	e.POST("/account/sca/:accountId/exchange-sca-proof", func(c echo.Context) error {
		accountId := c.Param("accountId")
		L := logging.L.Named("account.sca.exchange-sca-proof").With(zap.String("accountId", accountId))
		L.Info("Starting ...")
		defer L.Info("Done")

		// Get the associated account (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		// Parse the JWS from the request req
		var req struct {
			ScaProof string `json:"sca" validate:"required,min=30"`
		}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			L.Warn("Invalid JSON", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Exchange it for a JWT - calling Treezor
		if _, err := treezor.ExchangeSCAProof(busrpc, account.TreezorUserId, req.ScaProof); err != nil {
			L.Error("Error while getting the JWT from Treezor", zap.Error(err))
			return err // HTTP 500
		}

		// Update the account with the timestamp of the last SCA verification
		if err := accountsColl.TouchLastSCATime(account.ID, dbnosql.PNow()); err != nil {
			L.Error("Error while updating account's last SCA time", zap.Error(err))
			return err // HTTP 500
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// --------------------------------------------------------------------------------------------

	// Enable a special API to delete a user account, but only if we have the ALLOW_ACCOUNTS_DELETION env var set
	if allowAccountsDeletion {
		logging.L.Info("Allowing account deletion (env ALLOW_ACCOUNTS_DELETION=true)")

		e.GET("/debug-accounts/test", func(c echo.Context) error {
			time.Sleep(2 * time.Second)

			// Put a break point here ... then stop the debugging session
			os.Stderr.WriteString("This should not be printed to the console")
			os.Stderr.Sync()

			return web.OK(c)
		})

		e.DELETE("/account/:accountId", func(c echo.Context) error {
			accountId := strings.TrimSpace(c.Param("accountId"))
			if accountId == "" {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
			}

			// Get the account
			account, err := dbnosql.GetOneByID[*accounts.Account](accountsColl, accountId)
			if err != nil {
				return err // HTTP 500
			}
			if account == nil {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
			}

			if account.TreezorUserId != 0 {
				// Ask the Treezor service to shift and cancel the account so that it doesn't clash with a new one
				if _, err = bus2.CallMethodMU[treezor.ShiftUserInfoResponse](busrpc, treezor.Method_ShiftUserInfo, &treezor.ShiftUserInfoRequest{
					TreezorUserId: account.TreezorUserId,
					PhoneNumber:   account.PhoneNumber,
					DoCancel:      true,
				}, 10*time.Second); err != nil {
					return err // HTTP 500
				}
			}

			// Delete the account
			if err := accountsColl.DeleteById(accountId); err != nil {
				return err // HTTP 500
			}
			return web.OK(c)
		})

		e.GET("/debug-accounts/generateJwtToken", func(c echo.Context) error {
			accountId := strings.TrimSpace(c.QueryParam("accountId"))
			if accountId == "" {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found: no accountId")
			}

			// Get the account
			account, err := dbnosql.GetOneByID[*accounts.Account](accountsColl, accountId)
			if err != nil {
				return err // HTTP 500
			}
			if account == nil {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
			}

			// Generate a new JWT token
			jwt, err := account.GenerateJWTToken(accountsColl, 7*24*time.Hour)
			if err != nil {
				return err // HTTP 500
			}

			return web.MapOfMobileJsoner(c, map[string]interface{}{
				"jwt":     jwt,
				"account": account,
			})
		})

		e.GET("/debug-accounts/generate2FAJwtToken", func(c echo.Context) error {
			accountId := strings.TrimSpace(c.QueryParam("accountId"))
			if accountId == "" {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found: no accountId")
			}

			// Get the account
			account, err := dbnosql.GetOneByID[*accounts.Account](accountsColl, accountId)
			if err != nil {
				return err // HTTP 500
			}
			if account == nil {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
			}

			// Generate a new JWT token
			jwt, err := account.Generate2FAJWTToken(cache, []string{"oldTransactions"})
			if err != nil {
				return err // HTTP 500
			}

			return web.MapOfMobileJsoner(c, map[string]interface{}{
				"jwt2fa":  jwt,
				"account": account,
			})
		})

		e.GET("/debug-accounts/setDatesToTrigger2FA", func(c echo.Context) error {
			accountId := strings.TrimSpace(c.QueryParam("accountId"))
			if accountId == "" {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found: no accountId")
			}

			// Get the account
			account, err := dbnosql.GetOneByID[*accounts.Account](accountsColl, accountId)
			if err != nil {
				return err // HTTP 500
			}
			if account == nil {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
			}

			// Move back the account's creation date to more than 90 days
			account.CreatedAt = time.Now().Add(-91 * 24 * time.Hour)
			// Move back the account's last 2FA verification date to more than 90 days (if any)
			if account.Last2FATime != nil {
				past := time.Now().Add(-91 * 24 * time.Hour)
				account.Last2FATime = &past
			}

			// Update the account
			if err := accountsColl.UpdateById(accountId, account); err != nil {
				return err // HTTP 500
			}

			return web.OK(c)
		})

		e.POST("/debug-accounts/sendInitialTransferToAccount", func(c echo.Context) error {
			// Get the account by the provided phoneNumber
			payload := struct {
				PhoneNumber   string  `json:"phoneNumber"   mod:"trim"`
				MessageToUser string  `json:"messageToUser" mod:"trim"`
				Amount        float64 `json:"amount" validate:"gt=0"`
			}{}
			if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &payload); err != nil {
				return web.ErrorWithStatusAndCode(c, http.StatusBadRequest, -1, err.Error())
			}

			// Get the account
			account, err := dbnosql.GetOneByField[*accounts.Account](accountsColl, "phoneNumber", payload.PhoneNumber)
			if err != nil {
				return err // HTTP 500
			}
			if account == nil {
				return web.Error(c, common.ErrorCodeAccountNotFound, "No account found with that Phone number")
			}

			// The account needs to already be associated with Treezor
			if account.TreezorUserId == 0 || account.TreezorPrimaryWalletId == 0 {
				return web.Error(c, common.ErrorCodeAccountNotFound, "The account is not yet associated with Treezor, please update the Profile information first")
			}

			// Call Treezor to send the initial transfer
			result, err := bus2.CallMethodMU[treezor.SendSCTRToUserResponse](busrpc, treezor.Method_SendSCTRToUser, &treezor.SendSCTRToUserRequest{
				TreezorUserId:   account.TreezorUserId,
				TreezorWalletId: account.TreezorPrimaryWalletId,
				PaymentMethodId: 20, // SCTR
				MessageToUser:   payload.MessageToUser,
				Amount:          payload.Amount,
			}, 10*time.Second)
			if err != nil {
				return err // HTTP 500
			}

			return web.MapOfMobileJsoner(c, map[string]interface{}{
				"payin": result.Payin,
			})
		})
	}

	if config.IsDebugMode() {
		e.GET("/auth/debug/generate-jwt/:accountId", func(c echo.Context) error {
			accountId := c.Param("accountId")
			account, err := dbnosql.GetOneByField[*accounts.Account](accountsColl, "_id", accountId)
			if err != nil {
				return err // HTTP 500
			}
			if account == nil {
				return c.JSON(http.StatusNotFound, map[string]interface{}{
					"error": "Account not found",
					"code":  common.ErrorCodeAccountNotFound,
				})
			}
			tokenString, err := account.GenerateJWTToken(nil, 30*24*time.Hour) // don't update latest JWT signature
			if err != nil {
				return err // HTTP 500
			}
			return c.JSON(http.StatusOK, map[string]interface{}{
				"token": tokenString,
			})
		})
		logging.L.Info("Debug: `GET /auth/debug/generate-jwt/:accountId` is available")

		// Special API to create or sync Treezor information to the account.
		// - Required when the card was created outside Yochbee
		// - Required when the account was created outside Yochbee
		e.POST("/debug-accounts/treezor-sync-exceptional", func(c echo.Context) error {
			L := logging.L.Named("treezor-sync-exceptional")

			// JSON unmarshalling
			req := struct {
				TreezorUserId          int64  `json:"treezorUserId" validate:"required"`
				AccountId              string `json:"accountId"     mod:"trim"`
				TreezorPrimaryWalletId int64  `json:"treezorPrimaryWalletId"`
			}{}
			if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
				L.Warn("Invalid request", zap.Error(err))
				return common.ReturnInvalidAPICallResponse(c)
			}

			createPrimaryWallet := func(account *accounts.Account) error {
				rr, err := busrpc.CallM(treezor.Method_CreateWallet, account, 20*time.Second)
				if err != nil {
					L.Error("Error while creating the Treezor wallet", zap.Error(err))
					return err // HTTP 500
				}
				response := treezor.CreateWalletResponse{}
				if err = json.Unmarshal(rr, &response); err != nil {
					L.Error("Error while unmarshalling the Treezor wallet creation response", zap.Error(err))
					return err // HTTP 500
				}

				// Associate the Wallet ID (which is the primary) to the account
				// Can the user have multiple wallets, a question for another day ;) I don't know yet, what's the point?
				account.TreezorPrimaryWalletId = response.WalletId
				if err = accountsColl.UpdateById(account.ID, account); err != nil {
					L.Error("Error while updating the account with the Treezor wallet ID", zap.Error(err))
					return err // HTTP 500
				}
				return nil
			}

			// Get the account from Treezor, using the Treezor User ID
			resp, err := bus2.CallMethodMU[treezor.GetTreezorUserResponse](busrpc, treezor.Method_GetTreezorUser, treezor.GetTreezorUserRequest{
				TreezorUserId: req.TreezorUserId,
			}, 5*time.Second)
			if err != nil {
				return err // HTTP 500
			}
			tuser := resp.User
			if tuser == nil {
				return web.Error(c, common.ErrorCodeAccountNotFound, "Treezor Account not found")
			}
			L.Info("Treezor user found", zap.String("email", tuser.Email), zap.Any("tuser", tuser))

			var (
				account      *accounts.Account
				password     string
				initialToken string
			)

			if req.AccountId != "" {
				// Merge information to the existing Yochbee account, from Treezor
				//

				account, err = dbnosql.GetOneByID[*accounts.Account](accountsColl, req.AccountId)
				if err != nil {
					return err // HTTP 500
				}
				if account == nil {
					return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
				}

				// Sync the account information
				account.Email = tuser.Email
				account.PhoneNumber = tuser.Mobile
				account.IsPhoneNumberVerified = true
				account.FirstName = tuser.FirstName
				account.LastName = tuser.LastName
				account.Country = tuser.Country
				account.BirthCountry = tuser.BirthCountry
				account.Nationality = tuser.Nationality
				account.Title = tuser.Title
				account.PlaceOfBirth = tuser.PlaceOfBirth
				account.SpecifiedUSPerson = 0
				account.FrenchTaxResident = 1
				account.IncomeRange = tuser.IncomeRange
				account.PersonalAssets = tuser.PersonalAssets
				account.Occupation = tuser.Occupation

				account.TreezorUserId = req.TreezorUserId
				if req.TreezorPrimaryWalletId != 0 {
					L.Info("Updating the primary wallet ID", zap.Int64("treezorPrimaryWalletId", req.TreezorPrimaryWalletId))
					account.TreezorPrimaryWalletId = req.TreezorPrimaryWalletId
				} else {
					// Create a primary wallet
					err2 := createPrimaryWallet(account)
					if err2 != nil {
						return err2
					}
					L.Info("Created a new primary wallet", zap.Int64("treezorPrimaryWalletId", account.TreezorPrimaryWalletId))
				}
				if err = accountsColl.UpdateById(account.ID, account); err != nil {
					return err // HTTP 500
				}
			} else {
				// Create a new Yochbee account, from Treezor
				//

				// Prepare the new Account
				account = &accounts.Account{
					ID:                    accountsColl.GenerateUniqueId(),
					Email:                 tuser.Email,
					PhoneNumber:           tuser.Mobile,
					IsPhoneNumberVerified: true,

					// from the initial IP address of the registration
					IpAddressCountryIso3166: web.GetCloudflareCountryCode(c),

					// Initial preferences
					Preferences: &accounts.Preferences{
						ReceiveYochbeeNewsByEmail:          true,
						ReceiveYochbeeSpecialOffersByEmail: true,
						ReceivePushNotifications:           true,
						IsBiometricAuthEnabled:             true,
					},

					CreatedAt: time.Now(),
				}
				password, err = random.GenerateSecureNumberSequence(10)
				if err != nil {
					return err // HTTP 500
				}
				if err = account.SetNewPassword(password); err != nil {
					return err // HTTP 500
				}

				// Sync the account information
				account.Email = tuser.Email
				account.PhoneNumber = tuser.Mobile
				account.IsPhoneNumberVerified = true
				account.FirstName = tuser.FirstName
				account.LastName = tuser.LastName
				account.BirthCountry = tuser.BirthCountry
				account.Nationality = tuser.Nationality
				account.Title = tuser.Title
				account.PlaceOfBirth = tuser.PlaceOfBirth
				account.SpecifiedUSPerson = 0
				account.FrenchTaxResident = 1
				account.IncomeRange = tuser.IncomeRange
				account.PersonalAssets = tuser.PersonalAssets
				account.Occupation = tuser.Occupation

				// Save the new user account on the DB
				if _, err = accountsColl.Insert(account); err != nil {
					msg := err.Error()
					if strings.Contains(msg, "duplicate key error") {
						if strings.Contains(msg, "referralCode:") {
							L.Warn("Referral code already taken", zap.Error(err))
							return web.Error(c, common.ErrorCodeReferralCodeAlreadyTaken, "Referral code already taken")
						}
					}
					return err // HTTP 500
				}

				// Create a primary wallet (if not already created) just like during the profile update
				if req.TreezorPrimaryWalletId == 0 {
					err2 := createPrimaryWallet(account)
					if err2 != nil {
						return err2
					}
				}

				// Create support data
				if _, err := busrpc.CallM(kyc.Method_CreateProgressForAccount, &kyc.CreateProgressForAccountRequest{
					Account: account,
				}, 5*time.Second); err != nil {
					return err // HTTP 500
				}
			}

			// Send back a JWT token
			initialToken, err = account.GenerateJWTToken(accountsColl, 7*24*time.Hour)
			if err != nil {
				return err // HTTP 500
			}

			// Return the account with the password (if it was created here)
			pjson, err := account.ToMobileJSON()
			if err != nil {
				return err // HTTP 500
			}
			response := map[string]interface{}{
				"account": json.RawMessage(pjson),
			}
			if password != "" {
				response["password"] = password
			}
			response["jwt"] = initialToken

			L.Info("Account created or updated successfully", zap.String("accountId", account.ID), zap.ByteString("accountJson", pjson))
			return c.JSON(http.StatusOK, response)
		})
		logging.L.Info("Debug: `POST /debug-accounts/treezor-sync-exceptional` is available")
	}

}
