package main

import (
	"encoding/json"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/accounts"
)

func registerBusMethods(rpc bus2.RpcClient, accountsDb *accounts.AccountsCollection) error {
	// ---------------
	// ╔═╗┌─┐┌┬┐  ┌┐ ┬ ┬  ╔═╗╔╦╗╔═╗╦╦
	// ║ ╦├┤  │   ├┴┐└┬┘  ║╣ ║║║╠═╣║║
	// ╚═╝└─┘ ┴   └─┘ ┴   ╚═╝╩ ╩╩ ╩╩╩═╝
	if err := rpc.Register(accounts.Method_GetByEmail, func(payload []byte) ([]byte, error) {
		request := accounts.GetByEmailRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		q := accountsDb.GetQueryBuilder()
		_ = q.Set(&dbnosql.Condition{Field: "email", Comparator: "==", Value: request.Email})
		result, err := accountsDb.Find(q)
		if err != nil {
			return nil, err
		}

		response := accounts.GetByEmailResponse{}
		if len(result) >= 1 {
			response.Account = result[0].(*accounts.Account)
		}
		return json.Marshal(response)
	}, true); err != nil {
		return err
	}

	// -----------------
	// ╔═╗┌─┐┌┬┐
	// ║ ╦├┤  │
	// ╚═╝└─┘ ┴
	if err := rpc.Register(accounts.Method_Get, func(payload []byte) ([]byte, error) {
		request := accounts.GetRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		q := accountsDb.GetQueryBuilder()
		_ = q.Set(&dbnosql.Condition{Field: "_id", Comparator: "==", Value: request.AccountId})
		result, err := accountsDb.Find(q)
		if err != nil {
			return nil, err
		}

		response := accounts.GetResponse{}
		if len(result) >= 1 {
			response.Account = result[0].(*accounts.Account)
		}
		return json.Marshal(response)
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┬┐  ┌┐ ┬ ┬  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐  ╦ ╦╔═╗╔═╗╦═╗  ╦╔╦╗
	// ║ ╦├┤  │   ├┴┐└┬┘   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘  ║ ║╚═╗║╣ ╠╦╝  ║ ║║
	// ╚═╝└─┘ ┴   └─┘ ┴    ╩ ┴└─└─┘└─┘└─┘└─┘┴└─  ╚═╝╚═╝╚═╝╩╚═  ╩═╩╝
	//
	if err := rpc.Register(accounts.Method_GetByTreezorUserId, func(payload []byte) ([]byte, error) {
		request := accounts.GetByTreezorUserIdRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		account, err := dbnosql.GetOneByField[*accounts.Account](accountsDb, "treezorUserId", request.TreezorUserId)
		if err != nil {
			return nil, err
		}

		return json.Marshal(accounts.GetByTreezorUserIdResponse{
			Account: account,
		})
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┬┐  ┌┐ ┬ ┬  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐  ┌─┐┬─┐┬┌┬┐┌─┐┬─┐┬ ┬  ╦ ╦╔═╗╦  ╦  ╔═╗╔╦╗  ╦╔╦╗
	// ║ ╦├┤  │   ├┴┐└┬┘   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘  ├─┘├┬┘││││├─┤├┬┘└┬┘  ║║║╠═╣║  ║  ║╣  ║   ║ ║║
	// ╚═╝└─┘ ┴   └─┘ ┴    ╩ ┴└─└─┘└─┘└─┘└─┘┴└─  ┴  ┴└─┴┴ ┴┴ ┴┴└─ ┴   ╚╩╝╩ ╩╩═╝╩═╝╚═╝ ╩   ╩═╩╝
	//
	if err := bus2.RegisterUM(accounts.Method_GetByTreezorPrimaryWalletId, rpc, func(i *accounts.GetByTreezorPrimaryWalletIdRequest) (*accounts.GetByTreezorPrimaryWalletIdResponse, error) {
		account, err := dbnosql.GetOneByField[*accounts.Account](accountsDb, "treezorPrimaryWalletId", i.TreezorPrimaryWalletId)
		if err != nil {
			return nil, err
		}
		return &accounts.GetByTreezorPrimaryWalletIdResponse{
			Account: account,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
