package main

import (
	"errors"
	"fmt"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/utils"
	"yochbee/_base/web"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerCardTransactionsBusMethods(busrpc bus2.RpcClient, cardTransactionsColl *CardTransactionsCollection, cache *goredislib.Client) error {
	// ---------------
	// ╔═╗┌─┐┌┬┐  ╔═╗╔═╗╦═╗╔╦╗  ╔╦╗╦═╗╔═╗╔╗╔╔═╗╔═╗╔═╗╔╦╗╦╔═╗╔╗╔
	// ║ ╦├┤  │   ║  ╠═╣╠╦╝ ║║   ║ ╠╦╝╠═╣║║║╚═╗╠═╣║   ║ ║║ ║║║║
	// ╚═╝└─┘ ┴   ╚═╝╩ ╩╩╚══╩╝   ╩ ╩╚═╩ ╩╝╚╝╚═╝╩ ╩╚═╝ ╩ ╩╚═╝╝╚╝
	if err := bus2.RegisterUM(treezor.Method_GetCardTransactionByTreezorID, busrpc,
		func(request *treezor.GetCardTransactionByTreezorIDRequest) (*treezor.GetCardTransactionByTreezorIDResponse, error) {
			L := logging.L.Named(treezor.Method_GetCardTransactionByTreezorID).With(
				zap.Any("cardTransactionID", request.TreezorID))
			L.Info("Started")
			defer L.Info("Finished")

			// Retrieve the CardTransaction from the DB
			cardTransaction, err := cardTransactionsColl.GetCurrentCardTransaction(request.TreezorID)
			if err != nil {
				L.Error("Error while retrieving CardTransaction from DB", zap.Error(err))
				return nil, err
			}

			return &treezor.GetCardTransactionByTreezorIDResponse{
				CardTransaction: cardTransaction,
			}, nil
		}, true); err != nil {
		return err
	}

	// ╔═╗┌─┐┌┬┐  ╔═╗┬─┐┌─┐┌─┐┬ ┬  ╔═╗┌─┐┬─┐┌┬┐  ╔╦╗┬─┐┌─┐┌┐┌┌─┐┌─┐┌─┐┌┬┐┬┌─┐┌┐┌┌─┐
	// ║ ╦├┤  │   ╠╣ ├┬┘├┤ └─┐├─┤  ║  ├─┤├┬┘ ││   ║ ├┬┘├─┤│││└─┐├─┤│   │ ││ ││││└─┐
	// ╚═╝└─┘ ┴   ╚  ┴└─└─┘└─┘┴ ┴  ╚═╝┴ ┴┴└──┴┘   ╩ ┴└─┴ ┴┘└┘└─┘┴ ┴└─┘ ┴ ┴└─┘┘└┘└─┘
	if err := bus2.RegisterUM(treezor.Method_GetFreshCardTransactions, busrpc, func(i *treezor.GetFreshCardTransactionsRequest) (*treezor.GetFreshCardTransactionsResponse, error) {
		L := logging.L.Named(treezor.Method_GetFreshCardTransactions).With(
			zap.Int64("treezorCardId", i.TreezorCardId),
			zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Check if any date is more than 90 days old - if so, SCA is required
		now := time.Now()
		ninetyDaysAgo := now.Add(-90 * 24 * time.Hour)
		requiresSCA := i.DateFrom.Before(ninetyDaysAgo) || i.DateTo.Before(ninetyDaysAgo)

		// Validate that TreezorUserId is not zero when SCA is required for 90+ day old dates
		if isScaEnabled && requiresSCA && i.TreezorUserId == 0 {
			L.Error("treezorUserId is required for card transaction retrieval when dates are older than 90 days")
			return nil, errors.New("treezorUserId is required for card transaction retrieval when dates are older than 90 days")
		}

		L.Info("SCA requirement for date range", zap.Bool("requiresSCA", requiresSCA),
			zap.Time("dateFrom", i.DateFrom), zap.Time("dateTo", i.DateTo), zap.Time("ninetyDaysAgo", ninetyDaysAgo))

		// Call Treezor API
		path := fmt.Sprintf("/core-connect/cardtransactions/%d", i.TreezorCardId)

		// Add createdDateFrom
		createdDateFromString := i.DateFrom.Format(utils.DateTimeFormat2)
		path, err := web.AddQueryParamToUrl(path, "createdDateFrom", createdDateFromString)
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}

		// Add createdDateTo
		createdDateToString := i.DateTo.Format(utils.DateTimeFormat2)
		path, err = web.AddQueryParamToUrl(path, "createdDateTo", createdDateToString)
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}

		// Call Treezor API - use SCA if dates are > 90 days old
		treezorUserIdForSCA := int64(0)
		if requiresSCA {
			treezorUserIdForSCA = i.TreezorUserId
		}
		rbytes, err := callTreezorApi(http.MethodGet, path, nil, nil, true, treezorUserIdForSCA, cache)
		if err != nil {
			L.Error("Error calling Treezor API", zap.Error(err))
			return nil, err
		}

		cardTransactions, err := treezor.UnwrapCardTransactionsFromTreezorResponse(rbytes)
		if err != nil {
			L.Error("Error unwrapping CardTransactions from Treezor API response", zap.Error(err))
			return nil, err
		}

		return &treezor.GetFreshCardTransactionsResponse{
			CardTransactions: cardTransactions,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
