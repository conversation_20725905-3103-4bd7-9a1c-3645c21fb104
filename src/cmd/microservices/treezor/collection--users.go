package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type UsersCollection struct {
	dbnosql.DataCollection
}

func setupUsersCollection(db dbnosql.Database) (*UsersCollection, error) {
	coll := db.DeclareCollection("treezor-users", func(user interface{}) interface{} {
		return user
	}, func() (destination interface{}) {
		return &treezor.User{}
	})

	// Unique indexes!
	//

	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "email", Order: dbnosql.OrderASC},
		},
		Name: "email",
	}); err != nil {
		return nil, err
	}
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "mobile", Order: dbnosql.OrderASC},
		},
		Name: "mobile",
	}); err != nil {
		return nil, err
	}

	// Normal indexes
	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "treezorUserId", "createdAt", "updatedAt"); err != nil {
		return nil, err
	}

	return &UsersCollection{coll}, nil
}

func handleUserWebhookEvents(usersColl *UsersCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleUserWebhookEvents").
		With(zap.String("event", event.EventName)).
		With(zap.String("eventId", event.ID))

	users, err := treezor.UnwrapUsersFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed unwrapping Users from Treezor response", zap.Error(err))
		return
	}
	for i, user := range users {
		user.WebhookTimestamp = event.TreezorTimestamp
		if user.WebhookTimestamp == 0 {
			L.Warn("Webhook timestamp is 0", zap.Any("event", event), zap.Any("user", user))
		}

		if event.EventName == "user.create" {
			L.Info("Skipping user.create event", zap.Any("user", user))
			continue
		}

		if _, err = treezor.SyncActualEntity(L, usersColl, user.TreezorUserId, "treezorUserId", user); err != nil {
			L.Error("Failed syncing user", zap.Error(err), zap.Int("i", i), zap.Int64("treezorUserId", user.TreezorUserId), zap.String("treezorEventId", event.TreezorId))
		}
	}
}
