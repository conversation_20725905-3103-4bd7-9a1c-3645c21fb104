package main

import (
	"errors"
	"net/http"
	"strconv"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/web"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerMandatesBusMethods(busrpc bus2.RpcClient, cache *goredislib.Client) error {
	//
	// ┌─┐┬─┐┌─┐┌─┐┌┬┐┌─┐  ╔╦╗╔═╗╔╗╔╔╦╗╔═╗╔╦╗╔═╗
	// │  ├┬┘├┤ ├─┤ │ ├┤   ║║║╠═╣║║║ ║║╠═╣ ║ ║╣
	// └─┘┴└─└─┘┴ ┴ ┴ └─┘  ╩ ╩╩ ╩╝╚╝═╩╝╩ ╩ ╩ ╚═╝
	if err := bus2.RegisterUM(treezor.Method_CreateMandate, busrpc, func(request *treezor.CreateMandateRequest) (*treezor.CreateMandateResponse, error) {
		L := logging.L.Named(treezor.Method_CreateMandate)
		L.Info("Started", zap.Int64("treezorUserId", request.TreezorUserId))
		defer L.Info("Finished")

		var mandate treezor.Mandate
		returnValues := treezor.TreezorReturnValues{}
		_, err := callTreezorApi(http.MethodPost, "/v1/mandates", map[string]interface{}{
			"label":         request.Label,
			"sddType":       request.SddType,
			"sequenceType":  request.SequenceType,
			"isPaper":       true, // A of November 19th, 2024, Treezor mandates must be paper mandates
			"userId":        request.TreezorUserId,
			"debtorName":    request.DebtorName,
			"debtorAddress": request.DebtorAddress,
			"debtorCity":    request.DebtorCity,
			"debtorZipCode": request.DebtorZipCode,
			"debtorCountry": request.DebtorCountry,
			"debtorIban":    request.DebtorIban,
			"signatureDate": request.SignatureDate,
			"createdIp":     request.CreatedIp,
		}, &mandate, true, request.TreezorUserId, cache, &returnValues)
		if err != nil {
			if returnValues.ErrorCode == "22026" {
				L.Error("SEPA Creditor Identifier requirement not met", zap.String("errorCode", returnValues.ErrorCode))
				return nil, errors.New("SEPA Creditor Identifier requirement not met")
			}

			return nil, err
		}

		return &treezor.CreateMandateResponse{
			Mandate: &mandate,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ┌─┐┌─┐┌┬┐  ╔╦╗╔═╗╔╗╔╔╦╗╔═╗╔╦╗╔═╗  ┌┐ ┬ ┬  ╦╔╦╗
	// │ ┬├┤  │   ║║║╠═╣║║║ ║║╠═╣ ║ ║╣   ├┴┐└┬┘  ║ ║║
	// └─┘└─┘ ┴   ╩ ╩╩ ╩╝╚╝═╩╝╩ ╩ ╩ ╚═╝  └─┘ ┴   ╩═╩╝
	if err := bus2.RegisterUM(treezor.Method_GetMandateByID, busrpc, func(request *treezor.GetMandateByIDRequest) (*treezor.GetMandateByIDResponse, error) {
		L := logging.L.Named(treezor.Method_GetMandateByID)
		L.Info("Started", zap.String("mandateId", request.MandateId))
		defer L.Info("Finished")

		// Validate that TreezorUserId is not zero when SCA is enabled
		if isScaEnabled && request.TreezorUserId == 0 {
			L.Error("treezorUserId is required for mandate retrieval when SCA is enabled")
			return nil, errors.New("treezorUserId is required for mandate retrieval when SCA is enabled")
		}

		var mandate treezor.Mandate
		_, err := callTreezorApi(http.MethodGet, "/v1/mandates/"+request.MandateId, nil, &mandate, true, request.TreezorUserId, cache, nil)
		if err != nil {
			return nil, err
		}

		return &treezor.GetMandateByIDResponse{
			Mandate: &mandate,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ┌─┐┌─┐┌─┐┬─┐┌─┐┬ ┬  ╔╦╗╔═╗╔╗╔╔╦╗╔═╗╔╦╗╔═╗╔═╗
	// └─┐├┤ ├─┤├┬┘│  ├─┤  ║║║╠═╣║║║ ║║╠═╣ ║ ║╣ ╚═╗
	// └─┘└─┘┴ ┴┴└─└─┘┴ ┴  ╩ ╩╩ ╩╝╚╝═╩╝╩ ╩ ╩ ╚═╝╚═╝
	if err := bus2.RegisterUM(treezor.Method_SearchMandates, busrpc, func(request *treezor.SearchMandatesRequest) (*treezor.SearchMandatesResponse, error) {
		L := logging.L.Named(treezor.Method_SearchMandates)
		L.Info("Started", zap.Int64("treezorUserId", request.TreezorUserId))
		defer L.Info("Finished")

		// Validate that TreezorUserId is not zero when SCA is enabled
		if isScaEnabled && request.TreezorUserId == 0 {
			L.Error("treezorUserId is required for mandate search when SCA is enabled")
			return nil, errors.New("treezorUserId is required for mandate search when SCA is enabled")
		}

		params := make(map[string]string)
		params["userId"] = strconv.FormatInt(request.TreezorUserId, 10)

		// Construct the URL with query parameters
		urlPath := "/v1/mandates"
		urlPath, err := web.AddQueryParamsToUrl(urlPath, params)
		if err != nil {
			L.Error("Error adding query parameters to URL", zap.Error(err))
			return nil, err
		}

		var mandates struct {
			Mandates []*treezor.Mandate `json:"mandates"`
		}
		_, err = callTreezorApi(http.MethodGet, urlPath, nil, &mandates, true, request.TreezorUserId, cache, nil)
		if err != nil {
			return nil, err
		}

		return &treezor.SearchMandatesResponse{
			Mandates: mandates.Mandates,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ┌┬┐┌─┐┬  ┌─┐┌┬┐┌─┐  ┌─┐  ╔╦╗╔═╗╔╗╔╔╦╗╔═╗╔╦╗╔═╗
	//  ││├┤ │  ├┤  │ ├┤   ├─┤  ║║║╠═╣║║║ ║║╠═╣ ║ ║╣
	// ─┴┘└─┘┴─┘└─┘ ┴ └─┘  ┴ ┴  ╩ ╩╩ ╩╝╚╝═╩╝╩ ╩ ╩ ╚═╝
	if err := bus2.RegisterUM(treezor.Method_DeleteMandate, busrpc, func(request *treezor.DeleteMandateRequest) (*treezor.DeleteMandateResponse, error) {
		L := logging.L.Named(treezor.Method_DeleteMandate)
		L.Info("Started", zap.String("mandateId", request.MandateId))
		defer L.Info("Finished")

		// Validate that TreezorUserId is not zero when SCA is enabled
		if isScaEnabled && request.TreezorUserId == 0 {
			L.Error("treezorUserId is required for mandate deletion when SCA is enabled")
			return nil, errors.New("treezorUserId is required for mandate deletion when SCA is enabled")
		}

		treezorReturnValue := treezor.TreezorReturnValues{}
		_, err := callTreezorApi(http.MethodDelete, "/v1/mandates/"+request.MandateId, nil, nil, true, request.TreezorUserId, cache, &treezorReturnValue)
		if err != nil {
			return nil, err
		}

		return &treezor.DeleteMandateResponse{
			Ok: treezorReturnValue.StatusCode == 204,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
