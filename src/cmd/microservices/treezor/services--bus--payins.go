package main

import (
	"errors"
	"net/http"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/utils"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerPayinsBusMethods(busrpc bus2.RpcClient, payinsColl *PayinsCollection, cache *goredislib.Client) error {
	if err := bus2.RegisterUM(treezor.Method_GetPayinByTreezorID, busrpc,
		func(request *treezor.GetPayinByTreezorIDRequest) (*treezor.GetPayinByTreezorIDResponse, error) {
			payin, err := dbnosql.GetOneByField[*treezor.Payin](payinsColl, "treezorPayinId", request.TreezorID)
			if err != nil {
				return nil, err
			}
			return &treezor.GetPayinByTreezorIDResponse{Payin: payin}, nil
		}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌─┐┌┐┌┌┬┐  ╔═╗╔═╗╔╦╗╦═╗
	// ╚═╗├┤ │││ ││  ╚═╗║   ║ ╠╦╝
	// ╚═╝└─┘┘└┘─┴┘  ╚═╝╚═╝ ╩ ╩╚═
	//
	if err := bus2.RegisterUM(treezor.Method_SendSCTRToUser, busrpc,
		func(request *treezor.SendSCTRToUserRequest) (*treezor.SendSCTRToUserResponse, error) {
			L := logging.L.Named("sendSctrToUser")

			payload := struct {
				UserID          int64   `json:"userId"`
				WalletID        int64   `json:"walletId"`
				PaymentMethodId int     `json:"paymentMethodId"`
				Amount          float64 `json:"amount"`
				Currency        string  `json:"currency"`
				MessageToUser   string  `json:"messageToUser"`
			}{
				UserID:          request.TreezorUserId,
				WalletID:        request.TreezorWalletId,
				PaymentMethodId: request.PaymentMethodId,
				Amount:          request.Amount,
				Currency:        "EUR",
				MessageToUser:   request.MessageToUser,
			}
			rbytes, err := callTreezorApi(http.MethodPost, "/v1/payins", &payload, nil, false, request.TreezorUserId, cache)
			if err != nil {
				L.Error("Error calling Treezor API", zap.Error(err))
				return nil, err
			}

			payins, err := treezor.UnwrapPayinsFromTreezorResponse(rbytes)
			if err != nil {
				L.Error("Error extracting Payin entries from Treezor API response", zap.Error(err))
				return nil, err
			}
			if len(payins) == 0 {
				return nil, errors.New("no payin entry found in Treezor API response")
			}

			return &treezor.SendSCTRToUserResponse{Payin: payins[0]}, nil
		}, true); err != nil {
		return err
	}

	//
	// ┌┬┐┬ ┬┬ ┌┬┐┬┌─┐┬  ┌─┐  ╔═╗╔═╗╦ ╦╦╔╗╔╔═╗
	// ││││ ││  │ │├─┘│  ├┤   ╠═╝╠═╣╚╦╝║║║║╚═╗
	// ┴ ┴└─┘┴─┘┴ ┴┴  ┴─┘└─┘  ╩  ╩ ╩ ╩ ╩╝╚╝╚═╝
	//
	if err := bus2.RegisterUM(treezor.Method_GetMultiplePayinsByTreezorIds, busrpc,
		func(request *treezor.GetMultiplePayinsByTreezorIdsRequest) (*treezor.GetMultiplePayinsByTreezorIdsResponse, error) {
			payins, err := dbnosql.GetMultiByField[*treezor.Payin](payinsColl, "treezorPayinId", request.TreezorIDs)
			if err != nil {
				return nil, err
			}
			dict := make(map[string]*treezor.Payin)
			for _, payin := range payins {
				key := utils.InterfaceToString(payin.TreezorPayinID)
				dict[key] = payin
			}
			return &treezor.GetMultiplePayinsByTreezorIdsResponse{
				Payins: dict,
			}, nil
		}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌┬┐┬┌┬┐  ╔═╗╔╦╗╔╦╗╔═╗
	// ║╣ ││││ │   ╚═╗ ║║ ║║║╣
	// ╚═╝┴ ┴┴ ┴   ╚═╝═╩╝═╩╝╚═╝
	if err := bus2.RegisterUM(treezor.Method_SendSDDEToUser, busrpc,
		func(request *treezor.SendSDDEToUserRequest) (*treezor.SendSDDEToUserResponse, error) {
			L := logging.L.Named("sendSddeToUser")

			payload := struct {
				WalletID        int64   `json:"walletId"`
				PaymentMethodId int     `json:"paymentMethodId"`
				MandateID       int64   `json:"mandateId"`
				Amount          float64 `json:"amount"`
				Currency        string  `json:"currency"`
				PayinDate       string  `json:"payinDate"`
				MessageToUser   string  `json:"messageToUser"`
			}{
				WalletID:        request.WalletID,
				PaymentMethodId: 21, // Must be 21 for SDDE
				MandateID:       request.MandateID,
				Amount:          request.Amount,
				Currency:        "EUR",
				PayinDate:       request.PayinDate,
				MessageToUser:   request.MessageToUser,
			}

			rbytes, err := callTreezorApi(http.MethodPost, "/v1/payins", &payload, nil, false, request.TreezorUserId, cache)
			if err != nil {
				L.Error("Error calling Treezor API", zap.Error(err))
				return nil, err
			}

			payins, err := treezor.UnwrapPayinsFromTreezorResponse(rbytes)
			if err != nil {
				L.Error("Error extracting Payin entries from Treezor API response", zap.Error(err))
				return nil, err
			}
			if len(payins) == 0 {
				return nil, errors.New("no payin entry found in Treezor API response")
			}

			return &treezor.SendSDDEToUserResponse{Payin: payins[0]}, nil
		}, true); err != nil {
		return err
	}

	return nil
}
