package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerWalletsBusMethods(busrpc bus2.RpcClient, walletsColl *WalletsCollection, cache *goredislib.Client) error {

	// ---------------
	// ╔═╗┬─┐┌─┐┌─┐┌┬┐┌─┐  ╦ ╦┌─┐┬  ┬  ┌─┐┌┬┐
	// ║  ├┬┘├┤ ├─┤ │ ├┤   ║║║├─┤│  │  ├┤  │
	// ╚═╝┴└─└─┘┴ ┴ ┴ └─┘  ╚╩╝┴ ┴┴─┘┴─┘└─┘ ┴
	if err := busrpc.Register(treezor.Method_CreateWallet, func(payload []byte) ([]byte, error) {
		L := logging.L.Named(treezor.Method_CreateWallet)
		L.Info("Started")
		defer L.Info("Finished")

		// Request parameter:
		account := accounts.Account{}
		if err := json.Unmarshal(payload, &account); err != nil {
			<PERSON><PERSON>rror("Unmarshalled bad payload", zap.Error(err))
			return nil, err
		}

		// Wait for the Tariff ID to be available
		<-tariffIdAvailable

		futureID := walletsColl.GenerateUniqueId()
		createWallet := &treezor.WalletCreateDTO{
			Type:        treezor.WalletTypePaymentAccountWallet,
			Name:        fmt.Sprintf("Portefeuille de %s", account.FirstName),
			OwnerUserId: account.TreezorUserId,
			TariffId:    tariffId,
			Currency:    "EUR",
			Tag:         futureID,
		}
		returnedList := struct {
			Wallets []*treezor.Wallet `json:"wallets"`
		}{}
		rbytes, err := callTreezorApi(http.MethodPost, "/v1/wallets", createWallet, &returnedList, true, 0, cache) // no SCA
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}
		if len(returnedList.Wallets) == 0 {
			L.Error("No wallet returned", zap.String("response", string(rbytes)))
			return nil, errors.New("no wallet returned")
		}

		// Mirror on our DB
		returnedObj := returnedList.Wallets[0]
		returnedObj.ID = futureID
		returnedObj.CreatedAt = time.Now()
		if _, err := walletsColl.Insert(returnedObj); err != nil {
			return nil, err
		}

		L.Info("Created a new Wallet", zap.Any("wallet", returnedObj))
		return json.Marshal(treezor.CreateWalletResponse{WalletId: returnedObj.TreezorWalletId})
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┬┐  ╦ ╦┌─┐┬  ┬  ┌─┐┌┬┐  ┌┐ ┬ ┬  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐  ╦ ╦┌─┐┌─┐┬─┐  ╦╔╦╗
	// ║ ╦├┤  │   ║║║├─┤│  │  ├┤  │   ├┴┐└┬┘   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘  ║ ║└─┐├┤ ├┬┘  ║ ║║
	// ╚═╝└─┘ ┴   ╚╩╝┴ ┴┴─┘┴─┘└─┘ ┴   └─┘ ┴    ╩ ┴└─└─┘└─┘└─┘└─┘┴└─  ╚═╝└─┘└─┘┴└─  ╩═╩╝
	if err := bus2.RegisterUM(treezor.Method_GetWalletByUserId,
		busrpc,
		func(request *treezor.GetWalletByUserIdRequest) (*treezor.GetWalletByUserIdResponse, error) {
			L := logging.L.Named(treezor.Method_GetWalletByUserId)
			L.Info("Started")
			defer L.Info("Finished")

			// By default, it will fetch the Main wallet if no type is defined
			conditions := []*dbnosql.Condition{
				{"treezorUserId", "==", request.TreezorUserId},
			}
			if request.WalletType != 0 {
				conditions = append(conditions, &dbnosql.Condition{"type", "==", request.WalletType})
			}

			q := walletsColl.GetQueryBuilder()
			_ = q.SetAnd(conditions...)
			q.SetLimit(1)
			q.SetOrderByField("treezorWebhookTimestamp", dbnosql.OrderDESC)
			wallets, err := walletsColl.Find(q)
			if err != nil {
				L.Error("Failed to find the latest Wallet", zap.Error(err), zap.Int64("treezorUserId", request.TreezorUserId))
				return nil, err
			}
			if len(wallets) == 0 {
				return &treezor.GetWalletByUserIdResponse{
					TreezorWallet: nil,
				}, nil
			}
			twallets := dbnosql.ConvertDocsList[*treezor.Wallet](wallets)
			wallet := twallets[0]

			L.Info("Found wallet", zap.Int64("walletId", wallet.TreezorWalletId), zap.Any("wallet", wallet))
			return &treezor.GetWalletByUserIdResponse{TreezorWallet: wallet}, nil
		}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┬┐  ╦ ╦╔═╗╦  ╦  ╔═╗╔╦╗  ┌┐ ┬ ┬  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐  ╦╔╦╗
	// ║ ╦├┤  │   ║║║╠═╣║  ║  ║╣  ║   ├┴┐└┬┘   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘  ║ ║║
	// ╚═╝└─┘ ┴   ╚╩╝╩ ╩╩═╝╩═╝╚═╝ ╩   └─┘ ┴    ╩ ┴└─└─┘└─┘└─┘└─┘┴└─  ╩═╩╝
	//
	if err := bus2.RegisterUM(treezor.Method_GetWalletByTreezorId,
		busrpc,
		func(i *treezor.GetWalletByTreezorIdRequest) (*treezor.GetWalletByTreezorIdResponse, error) {
			L := logging.L.Named(treezor.Method_GetWalletByTreezorId)
			L.Info("Started")
			defer L.Info("Finished")

			wallet, err := dbnosql.GetOneByField[*treezor.Wallet](walletsColl, "treezorWalletId", i.TreezorWalletId)
			if err != nil {
				L.Error("Failed to find the Wallet", zap.Error(err), zap.Int64("treezorWalletId", i.TreezorWalletId))
				return nil, err
			}
			if wallet == nil {
				return &treezor.GetWalletByTreezorIdResponse{
					TreezorWallet: nil,
				}, nil
			}

			L.Info("Found wallet", zap.Int64("walletId", wallet.TreezorWalletId))
			return &treezor.GetWalletByTreezorIdResponse{TreezorWallet: wallet}, nil

		}, true); err != nil {
		return err
	}

	// ╔═╗┌─┐┌┬┐  ╔═╗┬─┐┌─┐┌─┐┬ ┬  ╦ ╦┌─┐┬  ┬  ┌─┐┌┬┐┌─
	// ║ ╦├┤  │   ╠╣ ├┬┘├┤ └─┐├─┤  ║║║├─┤│  │  ├┤  │ └─
	// ╚═╝└─┘ ┴   ╚  ┴└─└─┘└─┘┴ ┴  ╚╩╝┴ ┴┴─┘┴─┘└─┘ ┴ └─┘
	if err := bus2.RegisterUM(treezor.Method_GetFreshWallets, busrpc, func(i *treezor.GetFreshWalletsRequest) (*treezor.GetFreshWalletsResponse, error) {
		L := logging.L.Named(treezor.Method_GetFreshWallets).With(
			zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		rbytes, err := callTreezorApi(http.MethodGet, fmt.Sprintf("/v1/wallets?userId=%d", i.TreezorUserId), nil, nil, true, i.TreezorUserId, cache)
		if err != nil {
			L.Error("Error calling Treezor API", zap.Error(err))
			return nil, err
		}

		// Get the Wallets list object
		wallets, err := treezor.UnwrapWalletsFromTreezorResponse(rbytes)
		if err != nil {
			L.Error("Error unwrapping Wallets from Treezor API response", zap.Error(err))
			return nil, err
		}

		return &treezor.GetFreshWalletsResponse{
			Wallets: wallets,
		}, nil
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┬┐  ╦ ╦╔═╗╦  ╦  ╔═╗╔╦╗╔═╗  ┌┐ ┬ ┬  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐  ╦╔╦╗╔═╗
	// ║ ╦├┤  │   ║║║╠═╣║  ║  ║╣  ║ ╚═╗  ├┴┐└┬┘   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘  ║ ║║╚═╗
	// ╚═╝└─┘ ┴   ╚╩╝╩ ╩╩═╝╩═╝╚═╝ ╩ ╚═╝  └─┘ ┴    ╩ ┴└─└─┘└─┘└─┘└─┘┴└─  ╩═╩╝╚═╝
	//
	if err := bus2.RegisterUM(treezor.Method_GetWalletsByTreezorIds,
		busrpc,
		func(i *treezor.GetWalletsByTreezorIdsRequest) (*treezor.GetWalletsByTreezorIdsResponse, error) {
			L := logging.L.Named(treezor.Method_GetWalletsByTreezorIds)
			L.Info("Started")
			defer L.Info("Finished")

			q := walletsColl.GetQueryBuilder()
			_ = q.Set(&dbnosql.Condition{"treezorWalletId", "inArray", i.TreezorWalletIds})

			wallets, err := walletsColl.Find(q)
			if err != nil {
				L.Error("Failed to find the Wallets", zap.Error(err), zap.Any("treezorWalletIds", i.TreezorWalletIds))
				return nil, err
			}
			if len(wallets) == 0 {
				return &treezor.GetWalletsByTreezorIdsResponse{TreezorWallets: nil}, nil
			}

			// Convert to []*treezor.Wallet
			twallets := dbnosql.ConvertDocsList[*treezor.Wallet](wallets)

			L.Info("Found wallets", zap.Int("walletsCount", len(twallets)))
			return &treezor.GetWalletsByTreezorIdsResponse{TreezorWallets: twallets}, nil

		}, true); err != nil {
		return err
	}

	return nil
}
