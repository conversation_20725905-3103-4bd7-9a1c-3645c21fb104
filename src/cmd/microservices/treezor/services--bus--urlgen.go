package main

import (
	"fmt"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/utils"
	"yochbee/_base/web"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func registerUrlGenerationBusMethods(busrpc bus2.RpcClient) error {
	// ╔═╗╔═╗╦═╗╔╦╗  ╔╦╗╦═╗╔═╗╔╗╔╔═╗╔═╗╔═╗╔╦╗╦╔═╗╔╗╔╔═╗  ╦ ╦╦═╗╦
	// ║  ╠═╣╠╦╝ ║║   ║ ╠╦╝╠═╣║║║╚═╗╠═╣║   ║ ║║ ║║║║╚═╗  ║ ║╠╦╝║
	// ╚═╝╩ ╩╩╚══╩╝   ╩ ╩╚═╩ ╩╝╚╝╚═╝╩ ╩╚═╝ ╩ ╩╚═╝╝╚╝╚═╝  ╚═╝╩╚═╩═╝
	// ╔═╗╔═╗╔╗╔╔═╗╦═╗╔═╗╔╦╗╦╔═╗╔╗╔
	// ║ ╦║╣ ║║║║╣ ╠╦╝╠═╣ ║ ║║ ║║║║
	// ╚═╝╚═╝╝╚╝╚═╝╩╚═╩ ╩ ╩ ╩╚═╝╝╚╝
	if err := bus2.RegisterUM(treezor.Method_GenerateCardTransactionsURL, busrpc,
		func(request *treezor.GenerateCardTransactionsURLRequest) (*treezor.GenerateCardTransactionsURLResponse, error) {
			L := logging.L.Named(treezor.Method_GenerateCardTransactionsURL).With(
				zap.Int64("treezorCardId", request.TreezorCardId),
				zap.String("dateFrom", request.DateFrom),
				zap.String("dateTo", request.DateTo))
			L.Info("Started")
			defer L.Info("Finished")

			// Load Paris location for proper timezone handling with daylight saving
			parisLoc, err := time.LoadLocation("Europe/Paris")
			if err != nil {
				L.Error("Failed to load Europe/Paris location", zap.Error(err))
				return nil, fmt.Errorf("failed to load Paris timezone: %w", err)
			}

			// Parse the input dates in YYYY-MM-DD format
			dateFromParsed, err := time.Parse("2006-01-02", request.DateFrom)
			if err != nil {
				L.Error("Failed to parse dateFrom", zap.Error(err))
				return nil, fmt.Errorf("invalid dateFrom format, expected YYYY-MM-DD: %w", err)
			}

			dateToParsed, err := time.Parse("2006-01-02", request.DateTo)
			if err != nil {
				L.Error("Failed to parse dateTo", zap.Error(err))
				return nil, fmt.Errorf("invalid dateTo format, expected YYYY-MM-DD: %w", err)
			}

			// Convert dates to Paris timezone (start of day for dateFrom, end of day for dateTo)
			// This ensures we capture the full day in Paris timezone
			dateFromParis := time.Date(dateFromParsed.Year(), dateFromParsed.Month(), dateFromParsed.Day(), 0, 0, 0, 0, parisLoc)
			dateToParis := time.Date(dateToParsed.Year(), dateToParsed.Month(), dateToParsed.Day(), 23, 59, 59, 999999999, parisLoc)

			L.Info("Date conversion completed",
				zap.Time("dateFromParis", dateFromParis),
				zap.Time("dateToParis", dateToParis))

			// Build the URL path for card transactions
			path := fmt.Sprintf("/core-connect/cardtransactions/%d", request.TreezorCardId)

			// Add createdDateFrom query parameter
			createdDateFromString := dateFromParis.Format(utils.DateTimeFormat2)
			path, err = web.AddQueryParamToUrl(path, "createdDateFrom", createdDateFromString)
			if err != nil {
				L.Error("Error adding createdDateFrom query param to URL", zap.Error(err))
				return nil, fmt.Errorf("failed to add createdDateFrom query param: %w", err)
			}

			// Add createdDateTo query parameter
			createdDateToString := dateToParis.Format(utils.DateTimeFormat2)
			path, err = web.AddQueryParamToUrl(path, "createdDateTo", createdDateToString)
			if err != nil {
				L.Error("Error adding createdDateTo query param to URL", zap.Error(err))
				return nil, fmt.Errorf("failed to add createdDateTo query param: %w", err)
			}

			L.Info("URL generation completed", zap.String("finalURL", path))

			return &treezor.GenerateCardTransactionsURLResponse{
				URL:                 path,
				ConvertedDateFromTZ: createdDateFromString,
				ConvertedDateToTZ:   createdDateToString,
			}, nil
		}, true); err != nil {
		return err
	}

	return nil
}
