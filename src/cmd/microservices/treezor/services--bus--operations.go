package main

import (
	"errors"
	"fmt"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/utils"
	"yochbee/_base/web"
	"yochbee/common/treezor"

	"github.com/tidwall/gjson"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerOperationsBusMethods(busrpc bus2.RpcClient, cache *goredislib.Client) error {
	if err := bus2.RegisterUM(treezor.Method_GetFreshOperations, busrpc, func(i *treezor.GetFreshOperationsRequest) (*treezor.GetFreshOperationsResponse, error) {
		L := logging.L.Named(treezor.Method_GetFreshOperations).With(
			zap.Int64("treezorWalletId", i.TreezorWalletId),
			zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Check if any date is more than 90 days old - if so, SCA is required
		now := time.Now()
		ninetyDaysAgo := now.Add(-90 * 24 * time.Hour)
		requiresSCA := i.DateFrom.Before(ninetyDaysAgo) || i.DateTo.Before(ninetyDaysAgo)

		// Validate that TreezorUserId is not zero when SCA is required for 90+ day old dates
		if isScaEnabled && requiresSCA && i.TreezorUserId == 0 {
			L.Error("treezorUserId is required for operations retrieval when dates are older than 90 days")
			return nil, errors.New("treezorUserId is required for operations retrieval when dates are older than 90 days")
		}

		L.Info("SCA requirement for date range", zap.Bool("requiresSCA", requiresSCA),
			zap.Time("dateFrom", i.DateFrom), zap.Time("dateTo", i.DateTo), zap.Time("ninetyDaysAgo", ninetyDaysAgo))

		// Call Treezor API
		path := "/core-connect/operations"

		// Add userId
		path, err := web.AddQueryParamToUrl(path, "userId", fmt.Sprintf("%d", i.TreezorUserId))
		if err != nil {
			L.Error("Error adding userId query param to URL", zap.Error(err))
			return nil, err
		}

		// Add walletId
		path, err = web.AddQueryParamToUrl(path, "walletId", fmt.Sprintf("%d", i.TreezorWalletId))
		if err != nil {
			L.Error("Error adding walletId query param to URL", zap.Error(err))
			return nil, err
		}

		// Add dateFrom
		dateFromString := i.DateFrom.Format(utils.DateTimeFormat2)
		path, err = web.AddQueryParamToUrl(path, "dateFrom", dateFromString)
		if err != nil {
			L.Error("Error adding dateFrom query param to URL", zap.Error(err))
			return nil, err
		}

		// Add dateTo
		dateToString := i.DateTo.Format(utils.DateTimeFormat2)
		path, err = web.AddQueryParamToUrl(path, "dateTo", dateToString)
		if err != nil {
			L.Error("Error adding dateTo query param to URL", zap.Error(err))
			return nil, err
		}

		// Add pageSize
		if i.PageSize > 0 {
			path, err = web.AddQueryParamToUrl(path, "pageSize", fmt.Sprintf("%d", i.PageSize))
			if err != nil {
				L.Error("Error adding pageSize query param to URL", zap.Error(err))
				return nil, err
			}
		}

		// Add cursor (if it was set)
		if i.Cursor != "" {
			path, err = web.AddQueryParamToUrl(path, "cursor", i.Cursor)
			if err != nil {
				L.Error("Error adding cursor query param to URL", zap.Error(err))
				return nil, err
			}
		}

		// Call Treezor API - use SCA if dates are > 90 days old
		treezorUserIdForSCA := int64(0)
		if requiresSCA {
			treezorUserIdForSCA = i.TreezorUserId
		}
		rbytes, err := callTreezorApi(http.MethodGet, path, nil, nil, false, treezorUserIdForSCA, cache)
		if err != nil {
			L.Error("Error calling Treezor API", zap.Error(err))
			return nil, err
		}

		operations, err := treezor.UnwrapOperationsFromTreezorResponse(rbytes, "data")
		if err != nil {
			L.Error("Error unwrapping Operations from Treezor API response", zap.Error(err))
			return nil, err
		}

		// Get the cursor information
		cursors := gjson.GetManyBytes(rbytes, "cursor.current", "cursor.next", "cursor.prev")

		result := &treezor.GetFreshOperationsResponse{
			Operations: operations,
			Cursor:     cursors[0].String(),
			CursorNext: cursors[1].String(),
			CursorPrev: cursors[2].String(),
		}
		return result, nil
	}, true); err != nil {
		return err
	}

	return nil
}
