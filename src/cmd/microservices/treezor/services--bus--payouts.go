package main

import (
	"errors"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerPayoutsBusMethods(busrpc bus2.RpcClient, payoutsColl dbnosql.DataCollection, cache *goredislib.Client) error {
	// ---------------
	// ╔═╗┌─┐┬ ┬┌─┐┬ ┬┌┬┐  ╦ ╦╔═╗╦  ╦  ╔═╗╔╦╗  ┌┬┐┌─┐  ╔╗ ╔═╗╔╗╔╔═╗╔═╗╦╔═╗╦╔═╗╦═╗╦ ╦
	// ╠═╝├─┤└┬┘│ ││ │ │   ║║║╠═╣║  ║  ║╣  ║    │ │ │  ╠╩╗║╣ ║║║║╣ ╠╣ ║║  ║╠═╣╠╦╝╚╦╝
	// ╩  ┴ ┴ ┴ └─┘└─┘ ┴   ╚╩╝╩ ╩╩═╝╩═╝╚═╝ ╩    ┴ └─┘  ╚═╝╚═╝╝╚╝╚═╝╚  ╩╚═╝╩╩ ╩╩╚═ ╩
	if err := bus2.RegisterUM(treezor.Method_PayoutWallet2Beneficiary, busrpc,
		func(request *treezor.PayoutWallet2BeneficiaryRequest) (*treezor.PayoutWallet2BeneficiaryResponse, error) {
			L := logging.L.Named(treezor.Method_PayoutWallet2Beneficiary).With(
				zap.String("senderAccountId", request.Account.ID),
				zap.String("beneficiaryId", request.Beneficiary.ID),
				zap.Float64("amount", request.Amount))
			L.Info("Started")
			defer L.Info("Finished")

			dto := treezor.PayoutWallet2BeneficiaryDTO{
				WalletId:      request.Account.TreezorPrimaryWalletId,
				BeneficiaryId: request.Beneficiary.TreezorBeneficiaryId,
				Amount:        request.Amount,
				Currency:      "EUR", // always
				Label:         request.Label,
			}

			// Validate that TreezorUserId is not zero when SCA is enabled
			if isScaEnabled && request.TreezorUserId == 0 {
				L.Error("treezorUserId is required for payout operations when SCA is enabled")
				return nil, errors.New("treezorUserId is required for payout operations when SCA is enabled")
			}

			// Call Treezor API for the SCTE
			rbytes, err := callTreezorApi(http.MethodPost, "/v1/payouts", &dto, nil, true, request.TreezorUserId, cache)
			if err != nil {
				L.Error("Error calling Treezor API", zap.Error(err))
				return nil, err
			}

			// Get the Payout object
			payouts, err := treezor.UnwrapPayoutsFromTreezorResponse(rbytes)
			if err != nil {
				L.Error("Error unwrapping Payouts from Treezor API response", zap.Error(err))
				return nil, err
			}
			if len(payouts) == 0 {
				L.Error("No Payouts returned from Treezor API response", zap.String("response", string(rbytes)))
				return nil, errors.New("no payouts returned")
			}

			// Persist this on the DB
			// NOTE: This also corresponds to the `payout.create` Webhook event, but that event is not relevant anymore
			// because we're already saving the initial data here
			payout := payouts[0]
			payout.ID = payoutsColl.GenerateUniqueId()
			payout.CreatedAt = time.Now()
			if _, err = payoutsColl.Insert(payout); err != nil {
				L.Error("Error inserting Treezor Payout into DB", zap.Error(err))
				return nil, err
			}
			L.Info("Saved Treezor Payout into DB: {id}", zap.String("id", payout.ID))

			return &treezor.PayoutWallet2BeneficiaryResponse{
				TreezorPayout: payout,
			}, nil
		}, true); err != nil {
		return err
	}

	// ---------------
	if err := bus2.RegisterUM(treezor.Method_GetPayoutByTreezorID, busrpc, func(i *treezor.GetPayoutByTreezorIDRequest) (*treezor.GetPayoutByTreezorIDResponse, error) {
		payout, err := dbnosql.GetOneByField[*treezor.Payout](payoutsColl, "treezorPayoutId", i.TreezorID)
		if err != nil {
			return nil, err
		}
		return &treezor.GetPayoutByTreezorIDResponse{Payout: payout}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
