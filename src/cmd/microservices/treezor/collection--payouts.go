package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type PayoutsCollection struct {
	dbnosql.DataCollection
}

func setupPayoutsCollection(db dbnosql.Database) (*PayoutsCollection, error) {
	coll := db.DeclareCollection("treezor-payouts", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.Payout{}
	})

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "createdAt", "updatedAt"); err != nil {
		return nil, err
	}

	return &PayoutsCollection{coll}, nil
}

// handlePayoutWebhookEvents simply updates the existing copy on the database. These payouts have already been created
// during the SCTE process, and are simply updated.
// With that said, we still process `payout.create` because the initial data is likely to be incomplete.
func handlePayoutWebhookEvents(payoutsColl *PayoutsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handlePayoutWebhookEvents()").With(zap.String("event", event.EventName))
	L.Info("Started handling {event}")
	defer L.Info("Finished with {event}")

	payouts, err := treezor.UnwrapPayoutsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed to unwrap Payouts", zap.Error(err))
		return
	}

	for _, payout := range payouts {
		payout.WebhookTimestamp = event.TreezorTimestamp

		if event.EventName == "payout.create" {
			L.Info("Skipping `payout.create` event")
			continue
		}

		if _, err = treezor.SyncActualEntity(L, payoutsColl, payout.TreezorPayoutId, "treezorPayoutId", payout); err != nil {
			L.Error("Failed to sync Payout", zap.Error(err), zap.Int64("treezorPayoutId", payout.TreezorPayoutId), zap.String("treezorEventId", event.TreezorId))
		}
	}
}
