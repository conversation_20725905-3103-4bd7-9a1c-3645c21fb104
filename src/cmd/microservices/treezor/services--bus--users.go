package main

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"
	"yochbee/_base/random"
	"yochbee/_base/utils"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

func registerUsersBusMethods(rpc bus2.RpcClient, usersColl dbnosql.DataCollection, livenessLinksColl dbnosql.DataCollection, cache *goredislib.Client) error {

	// ---------------
	// ╔═╗┬─┐┌─┐┌─┐┌┬┐┌─┐  ╦ ╦┌─┐┌─┐┬─┐
	// ║  ├┬┘├┤ ├─┤ │ ├┤   ║ ║└─┐├┤ ├┬┘
	// ╚═╝┴└─└─┘┴ ┴ ┴ └─┘  ╚═╝└─┘└─┘┴└─
	if err := rpc.Register(treezor.Method_CreateUser, func(payload []byte) ([]byte, error) {
		L := logging.L.Named(treezor.Method_CreateUser)
		L.Info("Started")
		defer L.Info("Finished")

		// Request parameter:
		account := accounts.Account{}
		if err := json.Unmarshal(payload, &account); err != nil {
			L.Error("Unmarshalled bad payload", zap.Error(err))
			return nil, err
		}

		// The country code can be deduced by the Country field, or the IP address
		country := account.IpAddressCountryIso3166
		if account.CountryCode != "" {
			country = account.CountryCode
			L.Info("Country code set in account: {countryCode}", zap.String("countryCode", account.CountryCode))
		} else {
			L.Info("No country set, using IP address country {ipAddressCountryCode}", zap.String("ipAddressCountryCode", account.IpAddressCountryIso3166))
		}

		theUser := &treezor.UserCreateDTO{
			// Mandatory fields
			UserTypeId:        1,
			SpecifiedUSPerson: 0,
			Email:             account.Email,
			Address1:          account.PhysicalAddress,
			Mobile:            account.PhoneNumber,
			Phone:             account.PhoneNumber, // we need to also set the `phone` field

			// Profile fields
			FirstName: account.FirstName,
			LastName:  account.LastName,
			Address2:  account.ExtraAddressInfo,
			PostCode:  account.PostalCode,
			City:      account.City,
			State:     account.State,
			Birthday:  account.Birthday.Format(json2.BirthDateLayout),
			Country:   country, // unlike us, Treezor uses the country code

			Tag: account.ID,
		}
		returnedUsers := struct {
			Users []*treezor.User `json:"users"`
		}{}
		rbytes, err := callTreezorApi(http.MethodPost, "/v1/users", theUser, &returnedUsers, true, 0, cache) // no SCA
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}
		if len(returnedUsers.Users) == 0 {
			L.Error("No user returned", zap.String("response", string(rbytes)))
			return nil, errors.New("no user returned")
		}

		// Mirror on our DB
		returnedUser := returnedUsers.Users[0]
		returnedUser.ID = usersColl.GenerateUniqueId()
		returnedUser.CreatedAt = time.Now()
		if _, err = treezor.SyncActualEntity(L, usersColl, returnedUser.ID, "treezorUserId", returnedUser); err != nil {
			return nil, err
		}

		L.Info("Created a new User", zap.String("id", returnedUser.ID))
		return json.Marshal(treezor.CreateUserResponse{Id: returnedUser.ID, TreezorUserId: returnedUser.TreezorUserId})
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╦ ╦┌─┐┌─┐┬─┐
	// ║ ║├─┘ ││├─┤ │ ├┤   ║ ║└─┐├┤ ├┬┘
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ╚═╝└─┘└─┘┴└─
	if err := rpc.Register(treezor.Method_UpdateUser, func(payload []byte) ([]byte, error) {
		L := logging.L.Named(treezor.Method_UpdateUser)
		L.Info("Started")
		defer L.Info("Finished")

		// Request parameter:
		account := accounts.Account{}
		if err := json.Unmarshal(payload, &account); err != nil {
			L.Error("Unmarshalled bad payload", zap.Error(err))
			return nil, err
		}

		// The country code can be deduced by the Country field, or the IP address
		country := account.IpAddressCountryIso3166
		if account.CountryCode != "" {
			country = account.CountryCode
			L.Info("Country code set in account: {countryCode}", zap.String("countryCode", account.CountryCode))
		} else {
			L.Info("No country set, using IP address country {ipAddressCountryCode}", zap.String("ipAddressCountryCode", account.IpAddressCountryIso3166))
		}

		reqData := &treezor.UserUpdateDTO{
			Email:    account.Email,
			Address1: account.PhysicalAddress,
			Mobile:   account.PhoneNumber,
			Phone:    account.PhoneNumber, // also needs to be set

			// Profile fields
			FirstName: account.FirstName,
			LastName:  account.LastName,
			Address2:  account.ExtraAddressInfo,
			PostCode:  account.PostalCode,
			City:      account.City,
			State:     account.State,
			Birthday:  account.Birthday.Format(json2.BirthDateLayout),
			Country:   country, // unlike us, Treezor uses the country code

			Tag: account.ID,
		}
		returnedUsers := struct {
			Users []*treezor.User `json:"users"`
		}{}
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/users/%d", account.TreezorUserId), reqData, &returnedUsers, false, account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}
		if len(returnedUsers.Users) == 0 {
			L.Error("No user returned", zap.String("response", string(rbytes)))
			return nil, errors.New("no user returned")
		}

		// Update our copy of the user
		if _, err = treezor.SyncActualEntity(L, usersColl, account.TreezorUserId, "treezorUserId", returnedUsers.Users[0]); err != nil {
			L.Error("Failed updating Treezor User", zap.Error(err))
			return nil, err
		}

		return nil, nil // success, nothing to return
	}, true); err != nil {
		return err
	}

	if err := bus2.RegisterUM(treezor.Method_CancelUser, rpc, func(i *treezor.CancelUserRequest) (*treezor.CancelUserResponse, error) {
		L := logging.L.Named(treezor.Method_CancelUser).With(zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Get the existing user
		user, err := dbnosql.GetOneByField[*treezor.User](usersColl, "treezorUserId", i.TreezorUserId)
		if err != nil {
			L.Error("Failed getting user", zap.Error(err))
			return nil, err
		}
		if user == nil {
			L.Warn("User not found")
			return nil, errors.New("user not found")
		}

		// Cancel the user on Treezor
		tuser := treezor.User{}
		_, err = callTreezorApi(http.MethodDelete, fmt.Sprintf("/v1/users/%d?origin=USER", i.TreezorUserId), nil, &tuser, false, tuser.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}

		// Update our copy of the user
		now := time.Now()
		tuser.CanceledAt = &now
		if _, err = treezor.SyncActualEntity(L, usersColl, tuser.TreezorUserId, "treezorUserId", &tuser); err != nil {
			L.Error("Failed updating DB with Treezor User", zap.Error(err))
			return nil, err
		}

		return &treezor.CancelUserResponse{Ok: true}, nil
	}, true); err != nil {
		return err
	}

	if err := bus2.RegisterUM(treezor.Method_ShiftUserInfo, rpc, func(i *treezor.ShiftUserInfoRequest) (*treezor.ShiftUserInfoResponse, error) {
		L := logging.L.Named(treezor.Method_ShiftUserInfo).With(zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Get the existing user
		var user *treezor.User
		userExistsLocally := true
		qu := usersColl.GetQueryBuilder()
		conditions := []*dbnosql.Condition{
			{"treezorUserId", "==", i.TreezorUserId},
		}
		if i.PhoneNumber != "" {
			conditions = append(conditions, &dbnosql.Condition{"phone", "==", i.PhoneNumber})
			conditions = append(conditions, &dbnosql.Condition{"mobile", "==", i.PhoneNumber})
		}
		_ = qu.SetOr(conditions...)
		list, err := usersColl.Find(qu)
		if err != nil {
			L.Error("Failed getting user(s)", zap.Error(err))
			return nil, err
		}
		if len(list) == 0 {
			// Get the user directly from Treezor
			L.Warn("User not found in DB, getting from Treezor")
			result, err := callTreezorApi(http.MethodGet, fmt.Sprintf("/v1/users/%d", i.TreezorUserId), nil, nil, false, i.TreezorUserId, cache)
			if err != nil {
				L.Error("Failed getting user from Treezor API", zap.Error(err))
				return nil, err
			}
			tusers, err := treezor.UnwrapUsersFromTreezorResponse(result)
			if err != nil {
				L.Error("Failed unwrapping user from Treezor API", zap.Error(err))
				return nil, err
			}
			if len(tusers) == 0 {
				L.Error("No user returned")
				return nil, errors.New("no user returned")
			}
			user = tusers[0]
			userExistsLocally = false
		} else {
			user = list[0].(*treezor.User)
			if len(list) > 1 {
				var ids []int64
				var mids []string
				for _, u := range list {
					ids = append(ids, u.(*treezor.User).TreezorUserId)
					mids = append(mids, u.(*treezor.User).ID)
				}
				L.Warn("Multiple users found in DB, using first one", zap.Int("count", len(list)), zap.Int64s("treezorUserIds", ids), zap.Strings("localUserIds", mids))
			}
		}

		// Assign a highly unlikely email address and phone number.
		// New Account creation cannot be done with an existing email address, or phone number.
		unlikelyEmail := fmt.Sprintf("%s.%s.disabled", user.Email, random.Strings(8))
		unlikelyPhone := fmt.Sprintf("+1202%d", random.Numbers(1000000, 9999999))
		user.Email = unlikelyEmail
		user.Mobile = unlikelyPhone
		user.Phone = unlikelyPhone

		// Update the user on Treezor (just these fields)
		update := struct {
			Email  string `json:"email"`
			Mobile string `json:"mobile"`
			Phone  string `json:"phone"`
		}{
			Email:  unlikelyEmail,
			Mobile: unlikelyPhone,
			Phone:  unlikelyPhone,
		}
		result, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/users/%d", i.TreezorUserId), update, nil, false, i.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}

		// Cancel?
		if i.DoCancel {
			now := time.Now()
			user.CanceledAt = &now

			// Cancel on Treezor
			result, err = callTreezorApi(http.MethodDelete, fmt.Sprintf("/v1/users/%d?origin=USER", i.TreezorUserId), nil, nil, false, i.TreezorUserId, cache)
			if err != nil {
				if strings.Contains(err.Error(), "Unable to Cancel wallet. Balance is not null") {
					L.Warn("Failed deleting the user because one of the Wallet balance is not null; attempting to continue", zap.Error(err))

					// Get the current user
					result, err = callTreezorApi(http.MethodGet, fmt.Sprintf("/v1/users/%d", i.TreezorUserId), nil, nil, false, i.TreezorUserId, cache)
					if err != nil {
						L.Error("Failed getting user from Treezor API while attempting to continue", zap.Error(err))
						return nil, err
					}
				} else {
					L.Error("Failed calling Treezor API", zap.Error(err))
					return nil, err
				}
			}
		}

		tusers, err := treezor.UnwrapUsersFromTreezorResponse(result)
		if err != nil {
			L.Error("Failed unwrapping User(s) from Treezor API response", zap.Error(err))
			return nil, err
		}
		if len(tusers) == 0 {
			L.Error("No user returned")
			return nil, errors.New("no user returned")
		}
		tuser := tusers[0]

		if userExistsLocally {
			tuser.CanceledAt = user.CanceledAt
			if tuser.CanceledAt == nil {
				tuser.CanceledAt = dbnosql.PNow()
			}

			// Update our copy of the user
			if _, err = treezor.SyncActualEntity(L, usersColl, tuser.TreezorUserId, "treezorUserId", tuser); err != nil {
				L.Error("Failed updating DB with Treezor User", zap.Error(err))
				return nil, err
			}
		}

		return &treezor.ShiftUserInfoResponse{
			ShiftedEmail:       unlikelyEmail,
			ShiftedPhoneNumber: unlikelyPhone,
		}, nil
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╔╦╗┌─┐┌─┐┬  ┌─┐┬─┐┌─┐┌┬┐┬┬  ┬┌─┐  ╦┌┐┌┌─┐┌─┐
	// ║ ║├─┘ ││├─┤ │ ├┤    ║║├┤ │  │  ├─┤├┬┘├─┤ │ │└┐┌┘├┤   ║│││├┤ │ │
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ═╩╝└─┘└─┘┴─┘┴ ┴┴└─┴ ┴ ┴ ┴ └┘ └─┘  ╩┘└┘└  └─┘
	// - Request must come pre-validated, no extra validation is done here
	if err := rpc.Register(treezor.Method_UpdateDeclarativeInformation, func(payload []byte) ([]byte, error) {
		request := treezor.UpdateDeclarativeInformationRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			logging.L.Error("RPC checks error", zap.String("method", treezor.Method_UpdateDeclarativeInformation), zap.Error(err))
			return nil, err
		}

		L := logging.L.Named(treezor.Method_UpdateDeclarativeInformation).With(zap.Int64("userId", request.UserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Convert the request.Info into a simple map[string]interface{}
		jInfo, err := json.Marshal(request.Info)
		if err != nil {
			L.Error("Failed marshaling request.Info", zap.Error(err))
			return nil, err
		}
		convertedInfo := map[string]interface{}{}
		if err := json.Unmarshal(jInfo, &convertedInfo); err != nil {
			L.Error("Failed unmarshaling request.Info", zap.Error(err))
			return nil, err
		}

		// Set the `taxResidence` field to FR if it's a French Tax Resident
		if request.Info.FrenchTaxResident == 1 {
			convertedInfo["taxResidence"] = "FR"
		}
		delete(convertedInfo, "frenchTaxResident")

		// Send the update to Treezor
		user := treezor.User{}
		_, err = callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/users/%d", request.UserId), convertedInfo, &user, false, user.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}

		// Update our copy of the user
		if _, err = treezor.SyncActualEntity(L, usersColl, user.TreezorUserId, "treezorUserId", &user); err != nil {
			L.Error("Failed updating Treezor User", zap.Error(err))
			return nil, err
		}

		return json.Marshal(treezor.UpdateDeclarativeInformationResponse{TreezorUser: &user})
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌┬┐┌─┐┬─┐┌┬┐  ╦╔═╦ ╦╔═╗  ┬─┐┌─┐┬  ┬┬┌─┐┬ ┬
	// ╚═╗ │ ├─┤├┬┘ │   ╠╩╗╚╦╝║    ├┬┘├┤ └┐┌┘│├┤ │││
	// ╚═╝ ┴ ┴ ┴┴└─ ┴   ╩ ╩ ╩ ╚═╝  ┴└─└─┘ └┘ ┴└─┘└┴┘
	if err := rpc.Register(treezor.Method_StartKYCReview, func(payload []byte) ([]byte, error) {
		request := treezor.StartKYCReviewRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		L := logging.L.Named(treezor.Method_StartKYCReview)
		L.Info("Started")
		defer L.Info("Finished")

		// Get the account in order to get the TreezorUserId, at the same time: verify that the account does exist
		account, err := accounts.Get(rpc, request.Progress.AccountId)
		if err != nil {
			return nil, err
		}
		if account == nil {
			return nil, errors.New("account not found")
		}

		// Finally execute the KYC review ...
		user := treezor.User{}
		if _, err = callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/users/%d/Kycreview", account.TreezorUserId), nil, &user, true, 0, cache); err != nil { // no SCA
			return nil, err
		}

		// Update our copy of the user
		if _, err = treezor.SyncActualEntity(L, usersColl, user.TreezorUserId, "treezorUserId", &user); err != nil {
			return nil, err
		}
		// After this operation, Treezor will send a `user.kycreview` webhook, which will be intercepted by the `kyc`
		// service to update its Progress object.

		return bus2.OK()
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌┬┐┌─┐┬─┐┌┬┐  ╦╔═╦ ╦╔═╗  ╦  ┬┬  ┬┌─┐┌┐┌┌─┐┌─┐┌─┐
	// ╚═╗ │ ├─┤├┬┘ │   ╠╩╗╚╦╝║    ║  │└┐┌┘├┤ │││├┤ └─┐└─┐
	// ╚═╝ ┴ ┴ ┴┴└─ ┴   ╩ ╩ ╩ ╚═╝  ╩═╝┴ └┘ └─┘┘└┘└─┘└─┘└─┘
	if err := rpc.Register(treezor.Method_StartKYCLiveness, func(payload []byte) ([]byte, error) {
		request := treezor.StartKYCLivenessRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		// Call the Treezor API that starts KYC Liveness for the user
		treezorUserId := request.Account.TreezorUserId
		tresponse := struct {
			Identification struct {
				IdentificationId  string `json:"identification-id"`
				IdentificationUrl string `json:"identification-url"`
			} `json:"identification"`
		}{}
		raw, err := callTreezorApi(http.MethodPost, fmt.Sprintf("/v1/users/%d/kycliveness", treezorUserId), nil, &tresponse, true, 0, cache) // no SCA
		if err != nil {
			return nil, err
		}

		// Save a copy on the DB
		livenessLink := &treezor.LivenessLink{
			ID:                livenessLinksColl.GenerateUniqueId(),
			IdentificationId:  tresponse.Identification.IdentificationId,
			IdentificationUrl: tresponse.Identification.IdentificationUrl,
			AccountId:         request.Account.ID,
			UserId:            request.Account.TreezorUserId,
			CreatedAt:         time.Now(),
			UpdatedAt:         nil,
			Raw:               string(raw),
		}
		if _, err = livenessLinksColl.Insert(livenessLink); err != nil {
			return nil, err
		}

		return json.Marshal(&treezor.StartKYCLivenessResponse{
			LivenessLink: livenessLink,
		})
	}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌─┐┌┬┐  ╔╦╗╦═╗╔═╗╔═╗╔═╗╔═╗╦═╗  ╦ ╦╔═╗╔═╗╦═╗
	// ║ ╦├┤  │    ║ ╠╦╝║╣ ║╣ ╔═╝║ ║╠╦╝  ║ ║╚═╗║╣ ╠╦╝
	// ╚═╝└─┘ ┴    ╩ ╩╚═╚═╝╚═╝╚═╝╚═╝╩╚═  ╚═╝╚═╝╚═╝╩╚═
	//
	if err := bus2.RegisterUM(treezor.Method_GetTreezorUser, rpc, func(i *treezor.GetTreezorUserRequest) (*treezor.GetTreezorUserResponse, error) {
		tuser, err := dbnosql.GetOneByField[*treezor.User](usersColl, "treezorUserId", i.TreezorUserId)
		if err != nil {
			return nil, err
		}

		return &treezor.GetTreezorUserResponse{
			User: tuser,
		}, nil
	}, true); err != nil {
		return err
	}

	exchangeScaProofForSessionJWT := func(L *zap.Logger, treezorUserId int64, jws string) error {
		// Prepare payload
		payload := struct {
			GrantType    string `json:"grant_type"`
			ClientId     string `json:"client_id"`
			ClientSecret string `json:"client_secret"`
			Username     string `json:"username"`
			Password     string `json:"password"`
			Sca          string `json:"sca"`
		}{}
		payload.GrantType = "delegated_end_user"
		payload.ClientId = clientId
		payload.ClientSecret = clientSecret
		payload.Username = fmt.Sprintf("%d", treezorUserId)
		shasum := sha256.Sum256([]byte(fmt.Sprintf("%d%s", treezorUserId, clientSecret)))
		payload.Password = hex.EncodeToString(shasum[:])
		payload.Sca = jws

		// Prepare request
		url := coreConnectApiBaseUrl + "/oauth/token"
		bytesPayload, err := json.Marshal(payload)
		if err != nil {
			L.Error("Error marshalling payload", zap.Error(err))
			return err
		}
		req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(bytesPayload))
		if err != nil {
			L.Error("Error creating request", zap.Error(err))
			return err
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "application/json")
		req.Header.Set("User-Agent", yochbeeUserAgent)

		// Call the Treezor API
		response, err := apiHttpClient.Do(req)
		if err != nil {
			L.Error("FAILED", zap.Error(err))
			return err
		}
		defer response.Body.Close()

		// Process response
		if response.StatusCode != http.StatusOK {
			L.Error("Unexpected status code", zap.Int("status", response.StatusCode))
			return errors.New("unexpected HTTP status code")
		}

		// Read the response
		var responsePayload struct {
			AccessToken string `json:"access_token"`
			TokenType   string `json:"token_type"`
			ExpiresIn   int    `json:"expires_in"`
		}
		if err := json.NewDecoder(response.Body).Decode(&responsePayload); err != nil {
			L.Error("Error decoding response", zap.Error(err))
			return err
		}

		// Store the JWT in cache memory
		const scaSessionExpiration = 59 * time.Minute // Treezor SCA session JWTs expire after 1 hour
		sessionKey := fmt.Sprintf("treezorSCAJwt:session:%d", treezorUserId)
		if err = cache.Set(context.Background(), sessionKey, responsePayload.AccessToken, scaSessionExpiration).Err(); err != nil {
			L.Error("Error storing JWT in cache", zap.Error(err))
			return err
		}

		return nil
	}

	// ╔═╗─┐ ┬┌─┐┬ ┬┌─┐┌┐┌┌─┐┌─┐  ╔═╗╔═╗╔═╗  ┌─┐┬─┐┌─┐┌─┐┌─┐
	// ║╣ ┌┴┬┘│  ├─┤├─┤││││ ┬├┤   ╚═╗║  ╠═╣  ├─┘├┬┘│ ││ │├┤
	// ╚═╝┴ └─└─┘┴ ┴┴ ┴┘└┘└─┘└─┘  ╚═╝╚═╝╩ ╩  ┴  ┴└─└─┘└─┘└
	if err := bus2.RegisterUM(treezor.Method_ExchangeSCAProof, rpc, func(i *treezor.ExchangeSCAProofRequest) (*treezor.ExchangeSCAProofResponse, error) {
		L := logging.L.Named(treezor.Method_ExchangeSCAProof).With(zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Get the existing user
		user, err := dbnosql.GetOneByField[*treezor.User](usersColl, "treezorUserId", i.TreezorUserId)
		if err != nil {
			L.Error("Failed getting user", zap.Error(err))
			return nil, err
		}
		if user == nil {
			L.Warn("User not found")
			return nil, errors.New("user not found")
		}

		// Exchange to an SCA session JWT, then store it in cache memory
		if err := exchangeScaProofForSessionJWT(L, i.TreezorUserId, i.JWS); err != nil {
			// Already logs
			return nil, err
		}

		return &treezor.ExchangeSCAProofResponse{
			Ok: true,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╔═╗┌─┐┌─┐┬ ┬┌─┐  ╔═╗╔═╗╔═╗  ┌─┐┬─┐┌─┐┌─┐┌─┐
	// ║  ├─┤│  ├─┤├┤   ╚═╗║  ╠═╣  ├─┘├┬┘│ ││ │├┤
	// ╚═╝┴ ┴└─┘┴ ┴└─┘  ╚═╝╚═╝╩ ╩  ┴  ┴└─└─┘└─┘└
	if err := bus2.RegisterUM(treezor.Method_CacheSCAProof, rpc, func(i *treezor.CacheSCAProofRequest) (*treezor.CacheSCAProofResponse, error) {
		L := logging.L.Named(treezor.Method_CacheSCAProof).With(zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Decode the JWS in order to get the url information from the payload. It is within the `signed_data` field.
		// The `signed_data` field is a base64-encoded JSON string.
		parts := strings.Split(i.JWS, ".")
		if len(parts) != 3 {
			L.Error("Invalid JWS format")
			return nil, errors.New("invalid JWS format")
		}
		body, err := utils.DecodeTreezorBase64(parts[1])
		if err != nil {
			L.Error("Error decoding JWS", zap.Error(err), zap.String("jws", i.JWS))
			return nil, err
		}
		var payload struct {
			SignedData string `json:"signed_data"` // base64-encoded JSON string
		}
		if err := json.Unmarshal(body, &payload); err != nil {
			L.Error("Error unmarshalling JWS", zap.Error(err))
			return nil, err
		}

		// Some of the JWS that are sent do not contain the `signed_data` field. In this case, we don't store the JWS
		// for per operation (because there is no associated operation), but simply exchange it for a new SCA session JWT.
		var (
			operationKey string
			operationUrl string
		)
		if payload.SignedData != "" {
			// Decode the `signed_data` field
			signedData, err := utils.DecodeTreezorBase64(payload.SignedData)
			if err != nil {
				L.Error("Error decoding signed_data", zap.Error(err), zap.String("signed_data", payload.SignedData))
				return nil, err
			}
			operationUrl = gjson.GetBytes(signedData, "url").String()
			if operationUrl == "" {
				L.Error("Error getting url from signed_data")
				return nil, errors.New("error getting url from signed_data")
			}

			// Store the JWS in cache memory
			shasum := sha256.Sum256([]byte(operationUrl))
			operationUrlHash := hex.EncodeToString(shasum[:])
			operationKey = fmt.Sprintf("treezorSCAJws:operation:%d:%s", i.TreezorUserId, operationUrlHash)
			expiration := 5 * time.Minute
			if err := cache.Set(context.Background(), operationKey, i.JWS, expiration).Err(); err != nil {
				L.Error("Error storing JWS in cache", zap.Error(err))
				return nil, err
			}
		} else {
			L.Info("JWS does not contain a signed_data field (no operation associated)", zap.String("jws", i.JWS))
		}

		// This same JWS can be used to exchange an SCA session JWT with Treezor. If there isn't any more
		// SCA session (expired), then the JWS will be used to exchange a new SCA session JWT (like above)
		sessionKey := fmt.Sprintf("treezorSCAJwt:session:%d", i.TreezorUserId)
		if _, err := cache.Get(context.Background(), sessionKey).Result(); err == goredislib.Nil {
			// No SCA session JWT, exchange the JWS for a new SCA session JWT
			if err := exchangeScaProofForSessionJWT(L, i.TreezorUserId, i.JWS); err != nil {
				// Already logs
				return nil, err
			}
			L.Info("Exchanged SCA proof for a new SCA session JWT 🎉")
		} else if err != nil {
			L.Error("Error getting SCA session JWT from cache", zap.Error(err))
			return nil, err
		} else {
			// There is a SCA session JWT, nothing to do
			L.Info("SCA session JWT already exists")
		}

		return &treezor.CacheSCAProofResponse{
			Ok:           true,
			SessionKey:   sessionKey,
			OperationKey: operationKey,
			OperationUrl: operationUrl,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
