package main

import (
	"encoding/json"
	"time"
	"yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type CardTxAuthsCollection struct {
	dbnosql.DataCollection
}

func setupCardTxAuthsCollection(db dbnosql.Database) (*CardTxAuthsCollection, error) {
	coll := db.DeclareCollection("treezor-cardtxauths", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.CardTxAuth{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "publicToken", Order: dbnosql.OrderASC},
		},
		Name: "queryPublicToken",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
		},
		Name: "createdAt",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "webhookTimestamp", Order: dbnosql.OrderDESC},
		},
		Name: "webhookTimestamp",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "paymentDate", Order: dbnosql.OrderDESC},
		},
		Name: "paymentDate",
	}); err != nil {
		return nil, err
	}

	return &CardTxAuthsCollection{coll}, nil
}

func handleCard3DSv2AuthenticationWebhookEvents(signaling bus.Client, cardTxAuthsColl *CardTxAuthsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleCard3DSv2AuthenticationWebhookEvents").With(zap.String("event", event.EventName), zap.String("eventId", event.ID))

	payload := []byte(event.Payload.(string))

	publishCardTxAuth := func(cardTxAuth *treezor.CardTxAuth) {
		cardTxAuthByte, err := json.Marshal(cardTxAuth)
		if err != nil {
			L.Error("Failed to marshal the CardTxAuth object", zap.Error(err))
			return
		}

		eventName := "treezor.custom.cardTxAuth.create"
		if event.EventName == "card3DSv2Authentication.update" {
			eventName = "treezor.custom.cardTxAuth.update"
		}
		cardTxAuthStr := string(cardTxAuthByte)
		if err := signaling.PublishEvent(&bus.Event{
			Topic:            eventName,
			Data:             cardTxAuthStr, // already string
			Retrigger:        false,
			Time:             cardTxAuth.CreatedAt,
			WebhookTimestamp: event.TreezorTimestamp,
		}); err != nil {
			L.Error("Failed to re-publish event", zap.Error(err))
			return
		}
	}

	switch event.EventName {
	case "card3DSv2Authentication.create":
		cardTxAuth, err := treezor.UnwrapCardTxAuthFromTreezorResponse(payload)
		if err != nil {
			L.Error("Failed to unwrap the CardTxAuth object", zap.Error(err))
			return
		}

		cardTxAuth.WebhookTimestamp = event.TreezorTimestamp
		cardTxAuth.CreatedAt = time.Now()
		if _, err := cardTxAuthsColl.Insert(cardTxAuth); err != nil {
			L.Error("Failed to insert the CardTxAuth", zap.Error(err))
			return
		}
		L.Info("CardTxAuth created", zap.String("cardTxAuthId", cardTxAuth.ID))

		publishCardTxAuth(cardTxAuth)

	case "card3DSv2Authentication.update":
		cardTxAuthUpdate, err := treezor.UnwrapCardTxAuthUpdateFromTreezorResponse(payload)
		if err != nil {
			L.Error("Failed to unwrap the CardTxAuthUpdate object", zap.Error(err))
			return
		}

		cardTxAuthI, err := cardTxAuthsColl.GetById(cardTxAuthUpdate.ID)
		if err != nil {
			L.Error("Failed to find the original CardTxAuth", zap.Error(err), zap.String("cardTxAuthId", cardTxAuthUpdate.ID))
			return
		}
		cardTxAuth, ok := cardTxAuthI.(*treezor.CardTxAuth)
		if !ok {
			L.Error("Failed to cast the original CardTxAuth")
			return
		}

		cardTxAuth.WebhookTimestamp = event.TreezorTimestamp
		cardTxAuth.CreatedAt = time.Now()
		if err := cardTxAuthsColl.AppendUpdateToCardTxAuth(cardTxAuth, cardTxAuthUpdate); err != nil {
			L.Error("Failed to append the update to the CardTxAuth", zap.Error(err))
			return
		}
		L.Info("CardTxAuth updated", zap.String("cardTxAuthId", cardTxAuth.ID))

		publishCardTxAuth(cardTxAuth)

	default:
		L.Warn("Unrecognized `card3DSv2Authentication` event", zap.String("event", event.EventName), zap.String("eventId", event.ID), zap.ByteString("payload", payload))
	}
}

func (c *CardTxAuthsCollection) AppendUpdateToCardTxAuth(cardTxAuth *treezor.CardTxAuth, cardTxAuthUpdate *treezor.CardTxAuthUpdate) error {
	cardTxAuth.Updates = append(cardTxAuth.Updates, *cardTxAuthUpdate)
	cardTxAuth.UpdatedAt = dbnosql.PNow()

	if err := c.UpdateById(cardTxAuth.ID, cardTxAuth); err != nil {
		logging.L.Error("Failed to update the CardTxAuth", zap.Error(err))
		return err
	}

	return nil
}
