package main

import (
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type TransactionsCollection struct {
	dbnosql.DataCollection
}

func setupTransactionsCollection(db dbnosql.Database) (*TransactionsCollection, error) {
	coll := db.DeclareCollection("treezor-transactions", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.Transaction{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorId", Order: dbnosql.OrderASC},
			{Name: "treezorWalletDebitId", Order: dbnosql.OrderASC},
			{Name: "treezorWalletCreditId", Order: dbnosql.OrderASC},
		},
		Name: "query",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
			{Name: "updatedAt", Order: dbnosql.OrderDESC},
		},
		Name: "dates",
	}); err != nil {
		return nil, err
	}

	return &TransactionsCollection{coll}, nil
}

func handleTransactionsWebhookEvents(transactionsColl *TransactionsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleTransactionsWebhookEvents").With(zap.String("event", event.EventName)).With(zap.String("eventId", event.ID))

	transactions, err := treezor.UnwrapTransactionsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed to unwrap Transactions", zap.Error(err))
		return
	}

	for _, transaction := range transactions {
		transaction.WebhookTimestamp = event.TreezorTimestamp

		switch event.EventName {
		case "transaction.create":
			//
			// Created (the only event for a Transaction)
			//
			transaction.ID = transactionsColl.GenerateUniqueId()
			transaction.CreatedAt = time.Now()
			if _, err := transactionsColl.Insert(transaction); err != nil {
				L.Error("Failed to insert the Transaction", zap.Error(err))
			}

		default:
			L.Warn("Unexpected event", zap.String("event", event.EventName), zap.String("eventId", event.ID), zap.Any("transaction", transaction))
		}
	}
}
