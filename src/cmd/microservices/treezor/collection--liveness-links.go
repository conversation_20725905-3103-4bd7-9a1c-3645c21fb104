package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"
)

func setupLivenessLinksCollection(db dbnosql.Database) (dbnosql.DataCollection, error) {
	coll := db.DeclareCollection("treezor-liveness-links", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.LivenessLink{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "accountId", Order: dbnosql.OrderASC},
			{Name: "userId", Order: dbnosql.OrderASC},
		},
		Name: "owner",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
			{Name: "updatedAt", Order: dbnosql.OrderDESC},
		},
		Name: "dates",
	}); err != nil {
		return nil, err
	}

	return coll, nil
}
