package main

import (
	"fmt"
	"net/http"
	bus2 "yochbee/_base/bus"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerStatementsBusMethods(busrpc bus2.RpcClient, cache *goredislib.Client) error {
	if err := bus2.RegisterUM(treezor.Method_GetAccountStatement, busrpc, func(i *treezor.GetAccountStatementRequest) (*treezor.GetAccountStatementResponse, error) {
		L := logging.L.Named(treezor.Method_GetAccountStatement).With(
			zap.Int64("treezorWalletId", i.TreezorWalletId),
			zap.Int64("treezorUserId", i.TreezorUserId),
			zap.Int("year", i.Year),
			zap.Int("month", i.Month))
		L.Info("Started")
		defer L.Info("Finished")

		// Call Treezor API
		// Format month as 2 digits (with leading zero if needed)
		monthStr := fmt.Sprintf("%02d", i.Month)

		// Construct the API path
		path := fmt.Sprintf("/core-connect/statements/%d/computed?year=%d&month=%s", i.TreezorWalletId, i.Year, monthStr)

		// Make the API call
		response := struct {
			Link     string `json:"link"`
			ExpireIn int    `json:"expireIn"`
		}{}

		_, err := callTreezorApi(http.MethodGet, path, nil, &response, false, i.TreezorUserId, cache) // Use SCA with the user ID
		if err != nil {
			L.Error("Failed calling Treezor API", zap.Error(err))
			return nil, err
		}

		// Return the download URL and expiry time
		return &treezor.GetAccountStatementResponse{
			DownloadURL: response.Link,
			ExpiresIn:   response.ExpireIn,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
