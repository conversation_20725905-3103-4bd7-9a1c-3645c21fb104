package main

import (
	"errors"
	"fmt"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	utils "yochbee/_base/utils"
	"yochbee/_base/web"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func registerTransactionsBusMethods(busrpc bus2.RpcClient, cache *goredislib.Client) error {

	// ╔═╗┌─┐┌┬┐  ╔═╗┬─┐┌─┐┌─┐┬ ┬  ╔╦╗┬─┐┌─┐┌┐┌┌─┐┌─┐┌─┐┌┬┐┬┌─┐┌┐┌┌─┐
	// ║ ╦├┤  │   ╠╣ ├┬┘├┤ └─┐├─┤   ║ ├┬┘├─┤│││└─┐├─┤│   │ ││ ││││└─┐
	// ╚═╝└─┘ ┴   ╚  ┴└─└─┘└─┘┴ ┴   ╩ ┴└─┴ ┴┘└┘└─┘┴ ┴└─┘ ┴ ┴└─┘┘└┘└─┘
	if err := bus2.RegisterUM(treezor.Method_GetFreshTransactions, busrpc, func(i *treezor.GetFreshTransactionsRequest) (*treezor.GetFreshTransactionsResponse, error) {
		L := logging.L.Named(treezor.Method_GetFreshTransactions).With(
			zap.Int64("treezorWalletId", i.TreezorWalletId),
			zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Check if any date is more than 90 days old - if so, SCA is required
		now := time.Now()
		ninetyDaysAgo := now.Add(-90 * 24 * time.Hour)
		requiresSCA := i.DateFrom.Before(ninetyDaysAgo) || i.DateTo.Before(ninetyDaysAgo)

		// Validate that TreezorUserId is not zero when SCA is required for 90+ day old dates
		if isScaEnabled && requiresSCA && i.TreezorUserId == 0 {
			L.Error("treezorUserId is required for transaction retrieval when dates are older than 90 days")
			return nil, errors.New("treezorUserId is required for transaction retrieval when dates are older than 90 days")
		}

		L.Info("SCA requirement for date range", zap.Bool("requiresSCA", requiresSCA),
			zap.Time("dateFrom", i.DateFrom), zap.Time("dateTo", i.DateTo), zap.Time("ninetyDaysAgo", ninetyDaysAgo))

		path := "/v1/transactions"
		path, err := web.AddQueryParamToUrl(path, "walletId", fmt.Sprintf("%d", i.TreezorWalletId))
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}
		// Add createdDateFrom
		createdDateFromString := i.DateFrom.Format(utils.DateTimeFormat)
		path, err = web.AddQueryParamToUrl(path, "createdDateFrom", createdDateFromString)
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}
		// Add createdDateTo
		createdDateToString := i.DateTo.Format(utils.DateTimeFormat)
		path, err = web.AddQueryParamToUrl(path, "createdDateTo", createdDateToString)
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}
		// Add sortOrder
		path, err = web.AddQueryParamToUrl(path, "sortOrder", "ASC")
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}
		// Add sortBy
		path, err = web.AddQueryParamToUrl(path, "sortBy", "createdDate")
		if err != nil {
			L.Error("Error adding query param to URL", zap.Error(err))
			return nil, err
		}

		// Call Treezor API - use SCA if dates are > 90 days old
		treezorUserIdForSCA := int64(0)
		if requiresSCA {
			treezorUserIdForSCA = i.TreezorUserId
		}
		rbytes, err := callTreezorApi(http.MethodGet, path, nil, nil, true, treezorUserIdForSCA, cache)
		if err != nil {
			L.Error("Error calling Treezor API", zap.Error(err))
			return nil, err
		}

		// Get the Transactions list object
		responseTransactions, err := treezor.UnwrapTransactionsFromTreezorResponse(rbytes)
		if err != nil {
			L.Error("Error unwrapping Transactions from Treezor API response", zap.Error(err))
			return nil, err
		}

		// Convert the Treezor Transactions to Banking Transactions
		bankingTransactions := make([]*banking.Transaction, len(responseTransactions))
		for i, transaction := range responseTransactions {
			bankingTransactions[i] = transaction.MapToNewBankingTransaction()
		}
		return &treezor.GetFreshTransactionsResponse{
			Transactions: bankingTransactions,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
