package main

import (
	"fmt"
	"net/http"
	"strings"
	"time"
	"yochbee/_base/bus"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func registerSCAWalletsBusMethods(busrpc bus.RpcClient, scaWalletsColl *SCAWalletsCollection, cache *goredislib.Client) error {
	//
	// ┌─┐┬─┐┌─┐┌─┐┌┬┐┌─┐  ╔═╗╔═╗╔═╗  ╦ ╦┌─┐┬  ┬  ┌─┐┌┬┐
	// │  ├┬┘├┤ ├─┤ │ ├┤   ╚═╗║  ╠═╣  ║║║├─┤│  │  ├┤  │
	// └─┘┴└─└─┘┴ ┴ ┴ └─┘  ╚═╝╚═╝╩ ╩  ╚╩╝┴ ┴┴─┘┴─┘└─┘ ┴
	//
	if err := bus.RegisterUM(treezor.Method_CreateSCAWalletForUser, busrpc, func(request *treezor.CreateSCAWalletForUserRequest) (*treezor.CreateSCAWalletForUserResponse, error) {
		L := logging.L.Named(treezor.Method_CreateSCAWalletForUser).With(
			zap.Int64("treezorUserId", request.TreezorUserId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// Prepare the payload
		payload := map[string]interface{}{
			"userId":       fmt.Sprintf("%d", request.TreezorUserId),
			"scaWalletTag": request.WalletTag,
		}

		// Call the Treezor API
		rbytes, err := callTreezorApi(http.MethodPost, "/core-connect/sca/scawallets/", payload, nil, true, 0, cache) // no SCA
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		// Unwrap the SCA Wallet from the Treezor response
		scaWallet, err := treezor.UnwrapSingleSCAWalletFromTreezorResponse(rbytes)
		if err != nil {
			return nil, err
		}
		scaWallet.ID = scaWalletsColl.GenerateUniqueId()
		L.Info("SCA Wallet manually created", zap.String("id", scaWallet.ID), zap.String("scaWalletId", scaWallet.TreezorId), zap.Any("scaWallet", scaWallet))

		// Save the SCA Wallet in the database (as a snapshot)
		if _, err = scaWalletsColl.Insert(scaWallet); err != nil {
			L.Error("Failed to save SCA Wallet in database", zap.Error(err))
			return nil, err
		}

		return &treezor.CreateSCAWalletForUserResponse{
			TreezorSCAWallet: scaWallet,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ┌─┐┌─┐┌┬┐  ┌┐ ┬ ┬  ╔╦╗╦═╗╔═╗╔═╗╔═╗╔═╗╦═╗  ╦╔╦╗
	// │ ┬├┤  │   ├┴┐└┬┘   ║ ╠╦╝║╣ ║╣ ╔═╝║ ║╠╦╝  ║ ║║
	// └─┘└─┘ ┴   └─┘ ┴    ╩ ╩╚═╚═╝╚═╝╚═╝╚═╝╩╚═  ╩═╩╝
	//
	if err := bus.RegisterUM(treezor.Method_GetSCAWalletByTreezorID, busrpc, func(request *treezor.GetSCAWalletByTreezorIDRequest) (*treezor.GetSCAWalletByTreezorIDResponse, error) {
		L := logging.L.Named(treezor.Method_GetSCAWalletByTreezorID).With(
			zap.String("treezorSCAWalletId", request.TreezorWalletId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// Get the SCA Wallet from the database
		scaWallet, err := scaWalletsColl.GetByTreezorID(request.TreezorWalletId)
		if err != nil {
			if err == mongo.ErrNoDocuments {
				L.Error("SCA Wallet not found in database")
				return nil, err
			}
			L.Error("Failed to get SCA Wallet from database", zap.Error(err))
			return nil, err
		}

		return &treezor.GetSCAWalletByTreezorIDResponse{
			TreezorSCAWallet: scaWallet,
		}, nil
	}, true); err != nil {
		return err
	}

	// Get the SCA Wallet from Treezor
	if err := bus.RegisterUM(treezor.Method_GetFreshSCAWalletByTreezorID, busrpc, func(request *treezor.GetFreshSCAWalletByTreezorIdRequest) (*treezor.GetFreshSCAWalletByTreezorIdResponse, error) {
		L := logging.L.Named(treezor.Method_GetFreshSCAWalletByTreezorID).With(
			zap.String("treezorSCAWalletId", request.TreezorWalletId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// Get the SCA Wallet from Treezor
		// Call the Treezor API
		rbytes, err := callTreezorApi(http.MethodGet, "/core-connect/sca/scawallets/"+request.TreezorWalletId, nil, nil, true, 0, cache) // no SCA
		if err != nil {
			// It is possible that the SCA Wallet is not found in Treezor, in this case, the
			// error string will contain "No ScaWallet was found" or "resource_not_found_error"
			// NOTE: The error message "resource_not_found_error" was not expected before July 8th, 2025, and is not
			// something that was communicated by Treezor.
			if strings.Contains(err.Error(), "No ScaWallet was found") ||
				strings.Contains(err.Error(), "resource_not_found_error") {
				L.Warn("SCA Wallet not found in Treezor")
				return &treezor.GetFreshSCAWalletByTreezorIdResponse{
					TreezorSCAWallet: nil,
				}, nil
			}
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		// Unwrap the SCA Wallet from the Treezor response
		scaWallet, err := treezor.UnwrapSingleSCAWalletFromTreezorResponse(rbytes)
		if err != nil {
			return nil, err
		}

		// Save the SCA Wallet in the database (as a snapshot), this is a merge operation:
		// - Get the SCA Wallet from the database (if exists)
		// - Overwrite the fields from the Treezor response
		// - Save the SCA Wallet in the database as a new snapshot
		scaWalletFromDB, err := scaWalletsColl.GetByTreezorID(request.TreezorWalletId)
		if err != nil {
			if err == mongo.ErrNoDocuments {
				L.Error("SCA Wallet not found in database")
				return nil, err
			}
			L.Error("Failed to get SCA Wallet from database", zap.Error(err))
			return nil, err
		}

		if scaWalletFromDB != nil {
			L.Info("SCA Wallet found in database", zap.String("id", scaWalletFromDB.ID), zap.String("scaWalletId", scaWalletFromDB.TreezorId), zap.Any("scaWallet", scaWalletFromDB))

			// Merge the SCA Wallet from the database with the one from Treezor
			scaWalletFromDB.Status = scaWallet.Status
			scaWalletFromDB.SubStatus = scaWallet.SubStatus
			scaWalletFromDB.PasscodeStatus = scaWallet.PasscodeStatus
			scaWalletFromDB.Locked = scaWallet.Locked
			scaWalletFromDB.LockReasons = scaWallet.LockReasons
			scaWalletFromDB.SettingsProfile = scaWallet.SettingsProfile
			scaWalletFromDB.CreationDate = scaWallet.CreationDate
			scaWalletFromDB.ActivationDate = scaWallet.ActivationDate
			scaWalletFromDB.DeletionDate = scaWallet.DeletionDate
			scaWalletFromDB.ActivationCodeExpiryDate = scaWallet.ActivationCodeExpiryDate
			scaWalletFromDB.AuthenticationMethods = scaWallet.AuthenticationMethods
			scaWalletFromDB.InvalidActivationAttempts = scaWallet.InvalidActivationAttempts
			scaWalletFromDB.UserID = scaWallet.UserID
			scaWalletFromDB.SCAWalletTag = scaWallet.SCAWalletTag
			scaWalletFromDB.ClientID = scaWallet.ClientID
			scaWalletFromDB.CreatedAt = time.Now()
			scaWallet = scaWalletFromDB

			L.Info("SCA Wallet merged with the one from Treezor", zap.String("scaWalletId", scaWallet.TreezorId), zap.Any("scaWallet", scaWallet))
		}

		// Save the SCA Wallet in the database (as a snapshot)
		scaWallet.ID = scaWalletsColl.GenerateUniqueId()
		if _, err = scaWalletsColl.Insert(scaWallet); err != nil {
			L.Error("Failed to save SCA Wallet in database", zap.Error(err))
			return nil, err
		}
		L.Info("SCA Wallet snapshot inserted in database", zap.String("id", scaWallet.ID), zap.String("scaWalletId", scaWallet.TreezorId), zap.Any("scaWallet", scaWallet))

		return &treezor.GetFreshSCAWalletByTreezorIdResponse{
			TreezorSCAWallet: scaWallet,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ╔╦╗┌─┐┬  ┌─┐┌┬┐┌─┐  ╔═╗╔═╗╔═╗  ╦ ╦┌─┐┬  ┬  ┌─┐┌┬┐
	//  ║║├┤ │  ├┤  │ ├┤   ╚═╗║  ╠═╣  ║║║├─┤│  │  ├┤  │
	// ═╩╝└─┘┴─┘└─┘ ┴ └─┘  ╚═╝╚═╝╩ ╩  ╚╩╝┴ ┴┴─┘┴─┘└─┘ ┴
	//
	if err := bus.RegisterUM(treezor.Method_DeleteSCAWallet, busrpc, func(request *treezor.DeleteSCAWalletRequest) (*treezor.DeleteSCAWalletResponse, error) {
		L := logging.L.Named(treezor.Method_DeleteSCAWallet).With(
			zap.String("treezorSCAWalletId", request.TreezorWalletId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// Call the Treezor API
		_, err := callTreezorApi(http.MethodDelete, fmt.Sprintf("/core-connect/sca/scawallets/%s", request.TreezorWalletId), nil, nil, false, 0, cache) // no SCA
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		return &treezor.DeleteSCAWalletResponse{
			Ok: true,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
