package main

import (
	"testing"
	"yochbee/_base/utils"
)

func TestSignedDataDecoding(t *testing.T) {
	signedData := `eyJ1cmwiOiJodHRwczovL3lvY2hiZWUuYXBpLnRyZWV6b3IuY28vY29yZS1jb25uZWN0L29wZXJhdGlvbnM_d2FsbGV0SWQ9MTQwNDU2ODMmZGF0ZUZyb209MTY1NjA2NjMwMDAwMCZkYXRlVG89MTcxOTIyNDcyNjAzMCIsImJvZHkiOnt9fQ==`
	decoded, err := utils.DecodeTreezorBase64(signedData)
	if err != nil {
		t.Errorf("Error decoding signed data: %v", err)
	}
	t.Logf("Decoded signed data: %s", decoded)

	signedData = `eyJ1cmwiOiJodHRwczovL3lvY2hiZWUuYXBpLnRyZWV6b3IuY28vY29yZS1jb25uZWN0L29wZXJhdGlvbnM/dXNlcklkPTk4MzcwNTMmd2FsbGV0SWQ9MTQwNDU2ODMmZGF0ZUZyb209MjAyNC0wMS0wMVQxMjoxNTozNyUyQjAxOjAwJmRhdGVUbz0yMDI0LTEwLTA4VDEyOjE1OjM3JTJCMDE6MDAiLCJib2R5Ijp7fX0=`
	decoded, err = utils.DecodeTreezorBase64(signedData)
	if err != nil {
		t.Errorf("Error decoding signed data: %v", err)
	}
	t.Logf("Decoded signed data: %s", decoded)
}
