package main

import (
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type CardSnapshotsCollection struct {
	dbnosql.DataCollection
}

func (c *CardSnapshotsCollection) InsertAsSnapshot(card *treezor.Card) (string, error) {
	card.ID = c.GenerateUniqueId()
	card.CreatedAt = time.Now()
	if card.WebhookTimestamp == 0 {
		card.WebhookTimestamp = card.CreatedAt.UnixMilli()
	}
	return c.Insert(card)
}

func setupCardsCollection(db dbnosql.Database) (*CardSnapshotsCollection, error) {
	coll := db.DeclareCollection("treezor-cards+", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.Card{}
	})

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "treezorCardId", "treezorUserId", "treezorWalletId", "createdAt"); err != nil {
		return nil, err
	}

	return &CardSnapshotsCollection{coll}, nil
}

func handleCardsWebhookEvents(cardsColl *CardSnapshotsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleCardsWebhookEvents").With(zap.String("event", event.EventName)).With(zap.String("eventId", event.ID))

	cards, err := treezor.UnwrapCardsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed unwrapping Cards from Treezor response", zap.Error(err))
		return
	}

	switch event.EventName {
	case "card.createvirtual", "card.createphysical":
		L.Info("Skipping card event {event}")
		return
	}

	for _, tcard := range cards {
		tcard.WebhookTimestamp = event.TreezorTimestamp

		// Simply store as snapshots. The banking service will also listen to this event, and will update its own copy
		//

		id, err := cardsColl.InsertAsSnapshot(tcard)
		if err != nil {
			L.Error("Failed inserting card snapshot", zap.Error(err))
			continue
		}
		L.Info("New Card SNAPSHOT inserted", zap.String("id", id))
	}
}
