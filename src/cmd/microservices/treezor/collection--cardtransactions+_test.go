package main

import (
	"testing"
	"yochbee/common/treezor"
)

func TestCardTransactionsUnwrapping(t *testing.T) {
	responseStaging := `{"cardtransactions":[{"cardtransactionId":"141871","cardId":"998545780","walletId":"2582478","walletCurrency":"978","merchantId":"3713092","merchantName":"Test card transaction","merchantCity":"","merchantCountry":"","paymentLocalTime":"135844","publicToken":"108336022","paymentAmount":"40.00","paymentCurrency":"978","fees":"0.00","paymentCountry":"FRA","paymentId":"1000089848","paymentStatus":"A","paymentLocalAmount":"40.00","posCardholderPresence":"0","posPostcode":"75013","posCountry":"250","posTerminalId":"SC005E3A","posCardPresence":"0","panEntryMethod":"0","authorizationNote":"","authorizationResponseCode":"0","authorizationIssuerId":"2755975927883","authorizationIssuerTime":"2024-01-23 14:23:04","authorizationMti":"100","authorizedBalance":"163.75","limitAtmYear":"0","limitAtmMonth":"0","limitAtmWeek":"2000","limitAtmDay":"1000","limitAtmAll":"0","limitPaymentYear":"0","limitPaymentMonth":"0","limitPaymentWeek":"3000","limitPaymentDay":"2000","limitPaymentAll":"0","paymentDailyLimit":"0.00","totalLimitAtmYear":"0.00","totalLimitAtmMonth":"0.00","totalLimitAtmWeek":"0.00","totalLimitAtmDay":"0.00","totalLimitAtmAll":"0.00","totalLimitPaymentYear":"0.00","totalLimitPaymentMonth":"0.00","totalLimitPaymentWeek":"45.00","totalLimitPaymentDay":"40.00","totalLimitPaymentAll":"0.00","cardDigitalizationExternalId":"123456789","mccCode":"5331","acquirerId":"12653","is3DS":null,"merchantAddress":null,"paymentLocalDate":null}]}`
	ctransactions, err := treezor.UnwrapCardTransactionsFromTreezorResponse([]byte(responseStaging))
	if err != nil {
		t.Errorf("Error while unwrapping card transactions: %v", err)
	}
	if len(ctransactions) != 1 {
		t.Errorf("Expected 1 card transaction, got %d", len(ctransactions))
	}

	responseProd := `{"cardTransactions":[{"cardtransactionId":"141871","cardId":"998545780","walletId":"2582478","walletCurrency":"978","merchantId":"3713092","merchantName":"Test card transaction","merchantCity":"","merchantCountry":"","paymentLocalTime":"135844","publicToken":"108336022","paymentAmount":"40.00","paymentCurrency":"978","fees":"0.00","paymentCountry":"FRA","paymentId":"1000089848","paymentStatus":"A","paymentLocalAmount":"40.00","posCardholderPresence":"0","posPostcode":"75013","posCountry":"250","posTerminalId":"SC005E3A","posCardPresence":"0","panEntryMethod":"0","authorizationNote":"","authorizationResponseCode":"0","authorizationIssuerId":"2755975927883","authorizationIssuerTime":"2024-01-23 14:23:04","authorizationMti":"100","authorizedBalance":"163.75","limitAtmYear":"0","limitAtmMonth":"0","limitAtmWeek":"2000","limitAtmDay":"1000","limitAtmAll":"0","limitPaymentYear":"0","limitPaymentMonth":"0","limitPaymentWeek":"3000","limitPaymentDay":"2000","limitPaymentAll":"0","paymentDailyLimit":"0.00","totalLimitAtmYear":"0.00","totalLimitAtmMonth":"0.00","totalLimitAtmWeek":"0.00","totalLimitAtmDay":"0.00","totalLimitAtmAll":"0.00","totalLimitPaymentYear":"0.00","totalLimitPaymentMonth":"0.00","totalLimitPaymentWeek":"45.00","totalLimitPaymentDay":"40.00","totalLimitPaymentAll":"0.00","cardDigitalizationExternalId":"123456789","mccCode":"5331","acquirerId":"12653","is3DS":null,"merchantAddress":null,"paymentLocalDate":null}]}`
	ctransactions, err = treezor.UnwrapCardTransactionsFromTreezorResponse([]byte(responseProd))
	if err != nil {
		t.Errorf("Error while unwrapping card transactions: %v", err)
	}
	if len(ctransactions) != 1 {
		t.Errorf("Expected 1 card transaction, got %d", len(ctransactions))
	}

	// This one has a weird `createdAt` date that needs to be utils.DynamicFormatTime
	responseStagingBadDate := `{"cardtransactions":[{"cardtransactionId":"265373","cardId":"175156","walletId":"2145901","walletCurrency":"978","merchantId":"AmazonFr","merchantName":"Merchant Name","merchantCity":"","merchantCountry":"","paymentLocalTime":"135844","publicToken":"108077906","paymentAmount":"12.50","paymentCurrency":"978","fees":"0.00","paymentCountry":"FRA","paymentId":"1000164003","paymentStatus":"A","paymentLocalAmount":"12.50","posCardholderPresence":"0","posPostcode":"75013","posCountry":"250","posTerminalId":"SC005E3A","posCardPresence":"0","panEntryMethod":"0","authorizationNote":"","authorizationResponseCode":"0","authorizationIssuerId":"8415304052359","authorizationIssuerTime":"2024-03-11 13:00:00","authorizationMti":"100","authorizedBalance":"127.85","limitAtmYear":"80","limitAtmMonth":"70","limitAtmWeek":"60","limitAtmDay":"50","limitAtmAll":"1234","limitPaymentYear":"80","limitPaymentMonth":"70","limitPaymentWeek":"60","limitPaymentDay":"50","limitPaymentAll":"2001","paymentDailyLimit":"0.00","totalLimitAtmYear":"0.00","totalLimitAtmMonth":"0.00","totalLimitAtmWeek":"0.00","totalLimitAtmDay":"0.00","totalLimitAtmAll":"0.00","totalLimitPaymentYear":"44.30","totalLimitPaymentMonth":"28.40","totalLimitPaymentWeek":"28.40","totalLimitPaymentDay":"28.40","totalLimitPaymentAll":"28.40","cardDigitalizationExternalId":"123456789","mccCode":"8574","acquirerId":"12653","createdAt":"2024-06-24 18:25:43","is3DS":null,"merchantAddress":null,"paymentLocalDate":null}]}`
	ctransactions, err = treezor.UnwrapCardTransactionsFromTreezorResponse([]byte(responseStagingBadDate))
	if err != nil {
		t.Errorf("Error while unwrapping card transactions: %v", err)
	}
	if len(ctransactions) != 1 {
		t.Errorf("Expected 1 card transaction, got %d", len(ctransactions))
	}
}
