package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type WalletsCollection struct {
	dbnosql.DataCollection
}

func (c *WalletsCollection) GetOneByTreezorId(treezorWalletID int64) (*treezor.Wallet, error) {
	return dbnosql.GetOneByField[*treezor.Wallet](c, "treezorWalletId", treezorWalletID)
}

func (c *WalletsCollection) Update(twallet *treezor.Wallet) error {
	if twallet.ID == "" {
		return dbnosql.ErrMissingID
	}
	return c.UpdateById(twallet.ID, twallet)
}

func setupWalletsCollection(db dbnosql.Database) (*WalletsCollection, error) {
	coll := db.DeclareCollection("treezor-wallets", func(o interface{}) interface{} {
		return o
	}, func() (destination interface{}) {
		return &treezor.Wallet{}
	})

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "treezorUserId", "iban", "createdAt", "updatedAt"); err != nil {
		return nil, err
	}

	return &WalletsCollection{coll}, nil
}

func handleWalletWebhookEvents(walletColl *WalletsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleWalletWebhookEvents").
		With(zap.String("event", event.EventName)).
		With(zap.String("eventId", event.ID))

	wallets, err := treezor.UnwrapWalletsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		logging.L.Error("handleWalletWebhookEvents(): Could not unwrap the Wallets", zap.Error(err))
		return
	}
	for i, wallet := range wallets {
		wallet.WebhookTimestamp = event.TreezorTimestamp

		// Don't create a new Wallet if it's not a Card wallet
		if event.EventName == "wallet.create" && wallet.Type != treezor.WalletTypeElectronicMoneyCard {
			L.Info("Skipping wallet.create event", zap.Any("wallet", wallet))
			continue
		}

		if _, err = treezor.SyncActualEntity(L, walletColl, wallet.TreezorWalletId, "treezorWalletId", wallet); err != nil {
			L.Error("Failed syncing wallet", zap.Error(err), zap.Int("i", i), zap.Int64("treezorWalletId", wallet.TreezorWalletId), zap.String("treezorEventId", event.TreezorId))
		}
	}
}
