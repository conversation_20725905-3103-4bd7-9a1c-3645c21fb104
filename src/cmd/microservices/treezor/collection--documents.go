package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type DocumentsCollection struct {
	dbnosql.DataCollection
}

func setupDocumentsCollection(db dbnosql.Database) (*DocumentsCollection, error) {
	coll := db.DeclareCollection("treezor-documents", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.Document{}
	})

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "treezorUserId", "treezorDocumentId", "createdAt", "updatedAt"); err != nil {
		return nil, err
	}

	return &DocumentsCollection{coll}, nil
}

func handleDocumentWebhookEvents(documentsColl *DocumentsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleDocumentWebhookEvents")

	documents, err := treezor.UnwrapDocumentsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed unwrapping documents from Treezor response", zap.Error(err))
		return
	}

	for _, tdocument := range documents {
		tdocument.WebhookTimestamp = event.TreezorTimestamp

		if _, err = treezor.SyncActualEntity(L, documentsColl, tdocument.TreezorDocumentId, "treezorDocumentId", tdocument); err != nil {
			L.Error("Failed syncing document", zap.Error(err), zap.Int64("treezorDocumentId", tdocument.TreezorDocumentId), zap.String("treezorEventId", event.TreezorId))
		}
	}
}
