package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type BeneficiariesCollection struct {
	dbnosql.DataCollection
}

// setupBeneficiariesCollection creates a new BeneficiariesCollection and sets up the necessary indexes. Note
// that is is the raw version of the Beneficiary object that is from Treezor, different from `banking`.
func setupBeneficiariesCollection(db dbnosql.Database) (*BeneficiariesCollection, error) {
	coll := db.DeclareCollection("treezor-beneficiaries", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.BeneficiaryRaw{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorUserId", Order: dbnosql.OrderASC},
		},
		Name: "queryTreezorUserId",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
		},
		Name: "createdAt",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "updatedAt", Order: dbnosql.OrderDESC},
		},
		Name: "updatedAt",
	}); err != nil {
		return nil, err
	}

	return &BeneficiariesCollection{coll}, nil
}

func handleBeneficiaryWebhookEvents(beneficiariesColl *BeneficiariesCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleBeneficiaryWebhookEvents").With(zap.String("event", event.EventName), zap.String("eventId", event.ID))

	payload := []byte(event.Payload.(string))

	switch event.EventName {
	case "beneficiary.create":
		beneficiaries, err := treezor.UnwrapBeneficiaryRawFromTreezorResponse(payload)
		if err != nil {
			L.Error("Failed to unwrap the Beneficiary object", zap.Error(err))
			return
		}

		beneficiary := beneficiaries[0] // there should be only one Beneficiary in the array
		if _, err := beneficiariesColl.Insert(beneficiary); err != nil {
			L.Error("Failed to insert the Beneficiary", zap.Error(err))
			return
		}
		L.Info("Beneficiary created", zap.Int64("beneficiaryId", beneficiary.ID))

	case "beneficiary.update":
		beneficiaryUpdates, err := treezor.UnwrapBeneficiaryRawFromTreezorResponse(payload)
		if err != nil {
			L.Error("Failed to unwrap the BeneficiaryUpdate object", zap.Error(err))
			return
		}

		for _, beneficiaryUpdate := range beneficiaryUpdates {
			beneficiaryI, err := beneficiariesColl.GetOneByField("id", beneficiaryUpdate.ID)
			if err != nil {
				L.Error("Failed to find the original Beneficiary", zap.Error(err), zap.Int64("beneficiaryId", beneficiaryUpdate.ID))
				return
			}
			beneficiary, ok := beneficiaryI.(*treezor.Beneficiary)
			if !ok {
				L.Error("Failed to cast the original Beneficiary")
				return
			}

			if err := beneficiariesColl.UpdateOneByField("id", beneficiary.ID, beneficiary); err != nil {
				L.Error("Failed to append the update to the Beneficiary", zap.Error(err))
				return
			}
			L.Info("Beneficiary updated", zap.Int64("beneficiaryId", beneficiary.ID))
		}

	default:
		L.Warn("Unrecognized `beneficiary` event", zap.String("event", event.EventName), zap.String("eventId", event.ID), zap.ByteString("payload", payload))
	}
}
