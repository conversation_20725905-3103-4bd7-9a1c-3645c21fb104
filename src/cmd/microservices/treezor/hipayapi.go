package main

import (
	"bytes"
	"context"
	"yochbee/_base/config"
	json2 "yochbee/_base/json"
)

var (
	// hiPayBaseUrl              string
	hiPayPrivateTokenUsername string
	hiPayPrivateTokenPassword string
	// hiPayPublicTokenUsername  string
	// hiPayPublicTokenPassword  string
)

func setupHiPayAccess(config config.Provider) error {
	err := config.GetLive(context.Background(), "config.hipay", func(data []byte) {
		payload := struct {
			BaseUrl      string `json:"baseUrl" validate:"required,url"`
			PrivateToken struct {
				Username string `json:"username" validate:"required"`
				Password string `json:"password" validate:"required"`
			} `json:"privateToken"`
			PublicToken struct {
				Username string `json:"username" validate:"required"`
				Password string `json:"password" validate:"required"`
			} `json:"publicToken"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(bytes.NewReader(data), &payload); err != nil {
			panic(err)
		}

		configLock.Lock()
		defer configLock.Unlock()

		// hiPayBaseUrl = payload.BaseUrl
		hiPayPrivateTokenUsername = payload.PrivateToken.Username
		hiPayPrivateTokenPassword = payload.PrivateToken.Password
		// hiPayPublicTokenUsername = payload.PublicToken.Username
		// hiPayPublicTokenPassword = payload.PublicToken.Password
	})

	return err
}
