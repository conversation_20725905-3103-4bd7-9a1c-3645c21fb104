package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type LivenessStatusCollection struct {
	dbnosql.DataCollection
}

func setupLivenessStatusCollection(db dbnosql.Database) (*LivenessStatusCollection, error) {
	coll := db.DeclareCollection("treezor-liveness-status", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.LivenessStatus{}
	})

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "treezorUserId", "createdAt", "updatedAt"); err != nil {
		return nil, err
	}

	return &LivenessStatusCollection{coll}, nil
}

func handleLivenessStatusWebhookEvents(livenessStatusColl *LivenessStatusCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleLivenessStatusWebhookEvents")

	status, err := treezor.UnwrapLivenessStatusFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("handleLivenessStatusWebhookEvents(): failed to unwrap LivenessStatus", zap.Error(err), zap.Any("event", event))
		return
	}

	status.WebhookTimestamp = event.TreezorTimestamp
	if _, err = treezor.SyncActualEntity(L, livenessStatusColl, status.TreezorUserId, "treezorUserId", status); err != nil {
		L.Error("handleLivenessStatusWebhookEvents(): failed to sync LivenessStatus", zap.Error(err), zap.Int64("treezorUserId", status.TreezorUserId), zap.String("treezorEventId", event.TreezorId))
	}
}
