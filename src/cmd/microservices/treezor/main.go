package main

import (
	"yochbee/_base"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"

	goredislib "github.com/redis/go-redis/v9"
)

func registerAllServices(busrpc bus2.RpcClient, signaling bus2.Client, cache *goredislib.Client, db dbnosql.Database, cardTxAuthsCollection *CardTxAuthsCollection) error {
	// ---------------------------------
	usersCollection, err := setupUsersCollection(db)
	if err != nil {
		return err
	}

	walletsCollection, err := setupWalletsCollection(db)
	if err != nil {
		return err
	}

	documentsCollection, err := setupDocumentsCollection(db)
	if err != nil {
		return err
	}

	livenessLinksCollection, err := setupLivenessLinksCollection(db)
	if err != nil {
		return err
	}

	livenessStatusCollection, err := setupLivenessStatusCollection(db)
	if err != nil {
		return err
	}

	payoutsCollection, err := setupPayoutsCollection(db)
	if err != nil {
		return err
	}

	payoutRefundsCollection, err := setupPayoutRefundsCollection(db)
	if err != nil {
		return err
	}

	payinsCollection, err := setupPayinsCollection(db)
	if err != nil {
		return err
	}

	transactionsCollection, err := setupTransactionsCollection(db)
	if err != nil {
		return err
	}

	balancesCollection, err := setupBalancesCollection(db)
	if err != nil {
		return err
	}

	cardsCollection, err := setupCardsCollection(db)
	if err != nil {
		return err
	}

	cardTransactionsCollection, err := setupCardTransactionsCollection(db)
	if err != nil {
		return err
	}

	scaWalletsCollection, err := setupSCAWalletsCollection(db)
	if err != nil {
		return err
	}

	beneficiariesCollection, err := setupBeneficiariesCollection(db)
	if err != nil {
		return err
	}

	// ---------------------------------
	if err = registerBusMethods(busrpc, signaling,
		usersCollection, documentsCollection, livenessStatusCollection, payoutsCollection, payoutRefundsCollection, payinsCollection, transactionsCollection, walletsCollection, balancesCollection, cardsCollection, cardTransactionsCollection, scaWalletsCollection, cardTxAuthsCollection, beneficiariesCollection); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerUsersBusMethods(busrpc,
		usersCollection, livenessLinksCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerWalletsBusMethods(busrpc, walletsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerDocumentsBusMethods(busrpc, documentsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerBeneficiaryBusMethods(busrpc, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerPayoutsBusMethods(busrpc, payoutsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerPayinsBusMethods(busrpc, payinsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerCardsBusMethods(busrpc, cardsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerCardTransactionsBusMethods(busrpc, cardTransactionsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerTransactionsBusMethods(busrpc, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerSCAWalletsBusMethods(busrpc, scaWalletsCollection, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerOperationsBusMethods(busrpc, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerMandatesBusMethods(busrpc, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerStatementsBusMethods(busrpc, cache); err != nil {
		return err
	}

	// ---------------------------------
	if err = registerUrlGenerationBusMethods(busrpc); err != nil {
		return err
	}

	return nil
}

func main() {
	config.Microservice = "treezor"

	app := _base.SetupAppWithCustomProvidersAndInvokes("Treezor",
		[]interface{}{
			setupCardTxAuthsCollection,
		},
		[]interface{}{
			setupTreezorInternalTokenAcquisition,
			setupHiPayAccess,
			registerCardTxAuthsBusMethods,
			registerAllServices,
		},
	)

	app.Run()
}
