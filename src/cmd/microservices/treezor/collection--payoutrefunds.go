package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type PayoutRefundsCollection struct {
	dbnosql.DataCollection
}

func setupPayoutRefundsCollection(db dbnosql.Database) (*PayoutRefundsCollection, error) {
	coll := db.DeclareCollection("treezor-payoutrefunds", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.PayoutRefund{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "payoutId", Order: dbnosql.OrderASC},
		},
		Name: "payoutId",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
			{Name: "updatedAt", Order: dbnosql.OrderDESC},
		},
		Name: "dates",
	}); err != nil {
		return nil, err
	}

	return &PayoutRefundsCollection{coll}, nil
}

func handlePayoutRefundsWebhookEvents(payoutRefundsColl *PayoutRefundsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handlePayoutRefundsWebhookEvents").With(zap.String("event", event.EventName))

	payoutRefunds, err := treezor.UnwrapPayoutRefundsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		logging.L.Error("handlePayoutRefundsWebhookEvents(): failed to unwrap Payout Refunds", zap.Error(err))
		return
	}

	for _, refund := range payoutRefunds {
		refund.WebhookTimestamp = event.TreezorTimestamp

		if _, err = treezor.SyncActualEntity(L, payoutRefundsColl, refund.TreezorPayoutRefundID, "treezorPayoutRefundId", refund); err != nil {
			L.Error("Failed to sync Payout Refund", zap.Error(err))
		}
	}
}
