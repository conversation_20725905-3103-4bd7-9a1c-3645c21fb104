package main

import (
	"encoding/base64"
	bus2 "yochbee/_base/bus"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func registerBusMethods(busrpc bus2.RpcClient, signaling bus2.Client,
	usersColl *UsersCollection,
	documentsColl *DocumentsCollection,
	livenessStatusColl *LivenessStatusCollection,
	payoutsColl *PayoutsCollection,
	payoutRefundsColl *PayoutRefundsCollection,
	payinsColl *PayinsCollection,
	transactionsColl *TransactionsCollection,
	walletsColl *WalletsCollection,
	balancesColl *BalanceSnapshotsCollection,
	cardsColl *CardSnapshotsCollection,
	cardTransactionsColl *CardTransactionsCollection,
	scaWalletsColl *SCAWalletsCollection,
	cardTxAuthsColl *CardTxAuthsCollection,
	beneficiariesColl *BeneficiariesCollection,
) error {
	// ---------------
	// ┬┌┐┌┌─┐┌─┐┌─┐┌┬┐  ╔═╗╦═╗╔═╗╔═╗╦ ╦  ╔═╗╔╗╔╔╦╗╦╔╦╗╦ ╦
	// │││││ ┬├┤ └─┐ │   ╠╣ ╠╦╝║╣ ╚═╗╠═╣  ║╣ ║║║ ║ ║ ║ ╚╦╝
	// ┴┘└┘└─┘└─┘└─┘ ┴   ╚  ╩╚═╚═╝╚═╝╩ ╩  ╚═╝╝╚╝ ╩ ╩ ╩  ╩
	// This is called immediately after a Webhook has been received from <PERSON><PERSON>. This is a direct RPC call.
	// The condition is that the Webhook's entity type is known.
	if err := bus2.RegisterUM(treezor.Method_IngestFreshEntity, busrpc, func(r *treezor.IngestFreshEntityRequest) (*treezor.IngestFreshEntityResponse, error) {
		logging.L.Info("ingestFreshEntity(): started", zap.String("treezorEventId", r.Event.TreezorId))
		defer logging.L.Info("ingestFreshEntity(): finished", zap.String("treezorEventId", r.Event.TreezorId))

		switch r.EntityType {
		case "user":
			handleUserWebhookEvents(usersColl, r.Event)
		case "wallet":
			handleWalletWebhookEvents(walletsColl, r.Event)
		case "document":
			handleDocumentWebhookEvents(documentsColl, r.Event)
		case "kycliveness":
			handleLivenessStatusWebhookEvents(livenessStatusColl, r.Event)
		case "payout":
			handlePayoutWebhookEvents(payoutsColl, r.Event)
		case "payoutrefund":
			handlePayoutRefundsWebhookEvents(payoutRefundsColl, r.Event)
		case "payin":
			handlePayinsWebhookEvents(payinsColl, r.Event)
		case "transaction":
			handleTransactionsWebhookEvents(transactionsColl, r.Event)
		case "balance":
			handleBalancesWebhookEvents(balancesColl, walletsColl, r.Event)
		case "beneficiary":
			handleBeneficiaryWebhookEvents(beneficiariesColl, r.Event)
		case "card":
			handleCardsWebhookEvents(cardsColl, r.Event)
		case "cardtransaction":
			handleCardTransactionsWebhookEvents(cardTransactionsColl, r.Event)
		case "sca.wallet":
			handleSCAWalletWebhookEvents(scaWalletsColl, r.Event)
		case "card3DSv2Authentication":
			handleCard3DSv2AuthenticationWebhookEvents(signaling, cardTxAuthsColl, r.Event)
		}

		return &treezor.IngestFreshEntityResponse{}, nil
	}, true); err != nil {
		return err
	}

	// ---------------
	// ┌─┐┌─┐┌┬┐  ╦ ╦┬╔═╗┌─┐┬ ┬  ╔═╗╦ ╦╔╗ ╦  ╦╔═╗╦ ╦  ╔═╗╦ ╦╔╦╗╦ ╦╔═╗╦═╗╦╔═╗╔═╗╔╦╗╦╔═╗╔╗╔
	// │ ┬├┤  │   ╠═╣│╠═╝├─┤└┬┘  ╠═╝║ ║╠╩╗║  ║║  ╠═╣  ╠═╣║ ║ ║ ╠═╣║ ║╠╦╝║╔═╝╠═╣ ║ ║║ ║║║║
	// └─┘└─┘ ┴   ╩ ╩┴╩  ┴ ┴ ┴   ╩  ╚═╝╚═╝╩═╝╩╚═╝╩ ╩  ╩ ╩╚═╝ ╩ ╩ ╩╚═╝╩╚═╩╚═╝╩ ╩ ╩ ╩╚═╝╝╚╝
	if err := bus2.RegisterUM(treezor.Method_GetHiPayPublicAuthorization, busrpc, func(*treezor.GetHiPayPublicAuthorizationRequest) (*treezor.GetHiPayPublicAuthorizationResponse, error) {
		configLock.Lock()
		defer configLock.Unlock()

		// See also: https://docs.treezor.com/guide/cards/acquisition.html#using-hipay-s-api
		return &treezor.GetHiPayPublicAuthorizationResponse{
			PublicAuthorization: base64.StdEncoding.EncodeToString([]byte(hiPayPrivateTokenUsername + ":" + hiPayPrivateTokenPassword)),
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
