package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	bus2 "yochbee/_base/bus"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func registerCardTxAuthsBusMethods(busrpc bus2.RpcClient, cardTxAuthsColl *CardTxAuthsCollection, cache *goredislib.Client) error {
	//
	// ╔═╗┌─┐┌┬┐  ╔═╗┌─┐┬─┐┌┬┐╔╦╗─┐ ┬╔═╗┬ ┬┌┬┐┬ ┬┌─┐  ┌┐ ┬ ┬  ╦╔╦╗┌─┐
	// ║ ╦├┤  │   ║  ├─┤├┬┘ ││ ║ ┌┴┬┘╠═╣│ │ │ ├─┤└─┐  ├┴┐└┬┘  ║ ║║└─┐
	// ╚═╝└─┘ ┴   ╚═╝┴ ┴┴└──┴┘ ╩ ┴ └─╩ ╩└─┘ ┴ ┴ ┴└─┘  └─┘ ┴   ╩═╩╝└─┘
	if err := bus2.RegisterUM(treezor.Method_GetCardTxAuthsByIDs, busrpc, func(request *treezor.GetCardTxAuthsByIDsRequest) (*treezor.GetCardTxAuthsByIDsResponse, error) {
		L := logging.L.Named(treezor.Method_GetCardTxAuthsByIDs).With(zap.Strings("treezorCardTxAuthIds", request.TreezorCardTxAuthIds))
		L.Info("Started")
		defer L.Info("Finished")

		// Get the CardTxAuth objects using raw MongoDB method
		rawColl := cardTxAuthsColl.GetRawCollection().(*mongo.Collection)
		cursor, err := rawColl.Find(context.Background(), bson.M{"_id": bson.M{"$in": request.TreezorCardTxAuthIds}})
		if err != nil {
			L.Error("Error finding Treezor CardTxAuths", zap.Error(err))
			return nil, err
		}

		var cardTxAuths []*treezor.CardTxAuth
		if err := cursor.All(context.Background(), &cardTxAuths); err != nil {
			L.Error("Error unmarshalling Treezor CardTxAuths", zap.Error(err))
			return nil, err
		}

		return &treezor.GetCardTxAuthsByIDsResponse{
			CardTxAuths: cardTxAuths,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╔═╗╦ ╦╔╦╗╦ ╦╔═╗╔╗╔╔╦╗╦╔═╗╔═╗╔╦╗╔═╗  ╔═╗┌─┐┬─┐┌┬┐  ╔╦╗┬─┐┌─┐┌┐┌┌─┐┌─┐┌─┐┌┬┐┬┌─┐┌┐┌
	// ╠═╣║ ║ ║ ╠═╣║╣ ║║║ ║ ║║  ╠═╣ ║ ║╣   ║  ├─┤├┬┘ ││   ║ ├┬┘├─┤│││└─┐├─┤│   │ ││ ││││
	// ╩ ╩╚═╝ ╩ ╩ ╩╚═╝╝╚╝ ╩ ╩╚═╝╩ ╩ ╩ ╚═╝  ╚═╝┴ ┴┴└──┴┘   ╩ ┴└─┴ ┴┘└┘└─┘┴ ┴└─┘ ┴ ┴└─┘┘└┘
	if err := bus2.RegisterUM(treezor.Method_AuthenticateCardTransaction, busrpc, func(request *treezor.AuthenticateCardTransactionRequest) (*treezor.AuthenticateCardTransactionResponse, error) {
		L := logging.L.Named(treezor.Method_AuthenticateCardTransaction).With(zap.String("treezorCardTxAuthId", request.TreezorCardTxAuthId))
		L.Info("Started")
		defer L.Info("Finished")

		// Validate that TreezorUserId is not zero when SCA is enabled
		if isScaEnabled && request.TreezorUserId == 0 {
			L.Error("treezorUserId is required for card transaction authentication when SCA is enabled")
			return nil, errors.New("treezorUserId is required for card transaction authentication when SCA is enabled")
		}

		// Call the Treezor API
		path := fmt.Sprintf("/v1/auth-requests/%s/result", request.TreezorCardTxAuthId)
		treezorReturnValues := treezor.TreezorReturnValues{}
		rbytes, err := callTreezorApi(http.MethodPut, path, map[string]any{
			"authenticationResult":    request.Result,
			"authenticationSignature": request.Signature,
		}, nil, false, request.TreezorUserId, cache, &treezorReturnValues)
		if err != nil {
			L.Error("Error calling Treezor API", zap.Error(err))
			return nil, err
		}

		return &treezor.AuthenticateCardTransactionResponse{
			Ok:          treezorReturnValues.StatusCode == 200,
			StatusCode:  treezorReturnValues.StatusCode,
			RawResponse: string(rbytes),
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
