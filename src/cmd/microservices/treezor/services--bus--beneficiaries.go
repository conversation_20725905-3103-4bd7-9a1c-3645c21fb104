package main

import (
	"errors"
	"fmt"
	"net/http"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/utils"
	"yochbee/common/treezor"

	goredislib "github.com/redis/go-redis/v9"
)

func registerBeneficiaryBusMethods(busrpc bus2.RpcClient, cache *goredislib.Client) error {
	// ---------------
	// ╔═╗┬ ┬┌┐┌┌─┐  ╔╗ ╔═╗╔╗╔╔═╗╔═╗╦╔═╗╦╔═╗╦═╗╦ ╦  ┌┬┐┌─┐  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐
	// ╚═╗└┬┘││││    ╠╩╗║╣ ║║║║╣ ╠╣ ║║  ║╠═╣╠╦╝╚╦╝   │ │ │   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘
	// ╚═╝ ┴ ┘└┘└─┘  ╚═╝╚═╝╝╚╝╚═╝╚  ╩╚═╝╩╩ ╩╩╚═ ╩    ┴ └─┘   ╩ ┴└─└─┘└─┘└─┘└─┘┴└─
	if err := bus2.RegisterUM(treezor.Method_SyncBeneficiaryToTreezor, busrpc,
		func(request *treezor.SyncBeneficiaryToTreezorRequest) (*treezor.SyncBeneficiaryToTreezorResponse, error) {
			var dto interface{}
			if request.Beneficiary.TreezorBeneficiaryId == 0 {
				// Creation
				d := treezor.BeneficiaryDTO{}
				if err := utils.M.AutoMapper(request.Beneficiary, &d); err != nil {
					return nil, err
				}
				d.UserId = request.Beneficiary.TreezorUserId
				dto = d
			} else {
				// Updating
				d := treezor.BeneficiaryUpdateDTO{}
				if err := utils.M.AutoMapper(request.Beneficiary, &d); err != nil {
					return nil, err
				}
				dto = d
			}
			treezorUserId := request.Beneficiary.TreezorUserId

			// Validate that TreezorUserId is not zero when SCA is enabled
			if isScaEnabled && treezorUserId == 0 {
				return nil, errors.New("treezorUserId is required for beneficiary operations when SCA is enabled")
			}

			var (
				err    error
				rbytes []byte
			)
			if request.Beneficiary.TreezorBeneficiaryId == 0 {
				// CREATE
				rbytes, err = callTreezorApi(http.MethodPost, "/v1/beneficiaries", &dto, nil, true, treezorUserId, cache)
			} else {
				// UPDATE
				rbytes, err = callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/beneficiaries/%d", request.Beneficiary.TreezorBeneficiaryId), &dto, nil, true, treezorUserId, cache)
			}
			if err != nil {
				return nil, err
			}
			beneficiaries, err := treezor.UnwrapBeneficiariesFromTreezorResponse(rbytes)
			if err != nil {
				return nil, err
			}
			if len(beneficiaries) == 0 {
				return nil, errors.New("no beneficiaries returned")
			}

			// The result is NOT saved into the database. The only thing that we need is the ID of the beneficiary on
			// Treezor's side, and we just return that.
			// Caller is expected to persist that ID into the database for future reference.

			return &treezor.SyncBeneficiaryToTreezorResponse{
				TreezorBeneficiary: beneficiaries[0],
			}, nil
		}, true); err != nil {
		return err
	}

	// ---------------
	// ╔╦╗┌─┐┬  ┌─┐┌┬┐┌─┐  ╔╗ ╔═╗╔╗╔╔═╗╔═╗╦╔═╗╦╔═╗╦═╗╦ ╦
	//  ║║├┤ │  ├┤  │ ├┤   ╠╩╗║╣ ║║║║╣ ╠╣ ║║  ║╠═╣╠╦╝╚╦╝
	// ═╩╝└─┘┴─┘└─┘ ┴ └─┘  ╚═╝╚═╝╝╚╝╚═╝╚  ╩╚═╝╩╩ ╩╩╚═ ╩
	// Actually, this is NOT a supported method, such API isn't available on Treezor's side.
	// See: https://www.treezor.com/api-documentation/#/beneficiaries
	if err := bus2.RegisterUM(treezor.Method_DeleteBeneficiaryFromTreezor, busrpc,
		func(request *treezor.DeleteBeneficiaryFromTreezorRequest) (*treezor.DeleteBeneficiaryFromTreezorResponse, error) {
			if request.Beneficiary.TreezorBeneficiaryId == 0 {
				return nil, errors.New("beneficiary does not have a Treezor ID")
			}
			treezorUserId := request.Beneficiary.TreezorUserId

			// Validate that TreezorUserId is not zero when SCA is enabled
			if isScaEnabled && treezorUserId == 0 {
				return nil, errors.New("treezorUserId is required for beneficiary deletion when SCA is enabled")
			}

			_, err := callTreezorApi(http.MethodDelete, fmt.Sprintf("/v1/beneficiaries/%d", request.Beneficiary.TreezorBeneficiaryId), nil, nil, true, treezorUserId, cache)
			if err != nil {
				return nil, err
			}

			return &treezor.DeleteBeneficiaryFromTreezorResponse{
				Ok: true,
			}, nil
		}, true); err != nil {
		return err
	}

	return nil
}
