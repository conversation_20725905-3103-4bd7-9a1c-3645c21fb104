package main

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

var (
	downloadHttpClient = &http.Client{}
)

func registerDocumentsBusMethods(rpc bus2.RpcClient, documentsColl dbnosql.DataCollection, cache *goredislib.Client) error {
	// ---------------
	// ╔╦╗┬─┐┬┌─┐┌─┐┌─┐┬─┐  ╔╦╗┌─┐┌─┐┬ ┬┌┬┐┌─┐┌┐┌┌┬┐┌─┐  ╦ ╦╔═╗╦  ╔═╗╔═╗╔╦╗
	//  ║ ├┬┘││ ┬│ ┬├┤ ├┬┘   ║║│ ││  │ ││││├┤ │││ │ └─┐  ║ ║╠═╝║  ║ ║╠═╣ ║║
	//  ╩ ┴└─┴└─┘└─┘└─┘┴└─  ═╩╝└─┘└─┘└─┘┴ ┴└─┘┘└┘ ┴ └─┘  ╚═╝╩  ╩═╝╚═╝╩ ╩═╩╝
	// This call is required by Treezor for them to have the documents transferred from the Liveness provider to them.
	if err := bus2.RegisterUM(treezor.Method_TriggerKYCLivenessDocumentsSendingTreezor, rpc, func(r *treezor.TriggerKYCLivenessDocumentsSendingTreezorRequest) (*treezor.TriggerKYCLivenessDocumentsSendingTreezorResponse, error) {
		if _, err := callTreezorApi(
			http.MethodPut,
			fmt.Sprintf("/v1/users/%d/kycliveness", r.TreezorUserId),
			nil, nil, false, 0, cache); err != nil {
			return nil, err
		}

		return &treezor.TriggerKYCLivenessDocumentsSendingTreezorResponse{
			Ok: true,
		}, nil
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╦ ╦┌─┐┬  ┌─┐┌─┐┌┬┐  ╔╦╗┌─┐┌─┐┬ ┬┌┬┐┌─┐┌┐┌┌┬┐
	// ║ ║├─┘│  │ │├─┤ ││   ║║│ ││  │ ││││├┤ │││ │
	// ╚═╝┴  ┴─┘└─┘┴ ┴─┴┘  ═╩╝└─┘└─┘└─┘┴ ┴└─┘┘└┘ ┴
	if err := rpc.Register(treezor.Method_UploadDocument, func(payload []byte) ([]byte, error) {
		request := treezor.UploadDocumentRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			logging.L.Error("RPC checks error", zap.String("method", treezor.Method_UploadDocument), zap.Error(err))
			return nil, err
		}
		document := request.Document

		// Checks
		if (document.Type == kyc.DT_TaxStatement || document.Type == kyc.DT_TaxExemptionStatement) && request.ResidenceId == 0 {
			return nil, errors.New("required `residenceId` is missing (for doc. type 24 or 25")
		}

		// Download the file into memory
		resp, err := downloadHttpClient.Get(document.SecretUrl)
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		download, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		if int64(len(download)) != document.Size {
			return nil, errors.New("failed to download the complete file")
		}

		// Convert to Base64 and upload to Treezor
		fileb64 := base64.StdEncoding.EncodeToString(download)
		parsedDocuments := struct {
			Documents []*treezor.Document `json:"documents"`
		}{}
		upload := map[string]interface{}{
			"fileContentBase64": fileb64,
			"userId":            fmt.Sprintf("%d", document.TreezorUserId),
			"documentTypeId":    uint8(document.Type),
			"name":              document.OriginalFilename,

			// We cannot send our ID in the documentTag because, as weird as it sounds, it needs an int, not the usual
			// string like users and wallets. So, we're going to have to do a lookup for a relation to be made.
			// "documentTag":       document.ID,
		}
		if request.ResidenceId > 0 {
			upload["residenceId"] = request.ResidenceId
		}
		if _, err = callTreezorApi(http.MethodPost, "/v1/documents", upload, &parsedDocuments, true, 0, cache); err != nil {
			return nil, err
		}

		// Save this object into the DB
		treezorDocument := parsedDocuments.Documents[0]
		treezorDocument.ID = documentsColl.GenerateUniqueId()
		treezorDocument.CreatedAt = time.Now()
		if _, err = documentsColl.Insert(treezorDocument); err != nil {
			return nil, err
		}

		// Return the document from Treezor back to the caller
		response := treezor.UploadDocumentResponse{
			TreezorDocument: treezorDocument,
		}
		return json.Marshal(response)
	}, true); err != nil {
		return err
	}

	return nil
}
