package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type PayinsCollection struct {
	dbnosql.DataCollection
}

func setupPayinsCollection(db dbnosql.Database) (*PayinsCollection, error) {
	coll := db.DeclareCollection("treezor-payins", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.Payin{}
	})

	// Add unique index on treezorPayinId to prevent duplicates
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorPayinId", Order: dbnosql.OrderASC},
		},
		Name: "treezorPayinId",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "treezorId", "treezorUserId", "treezorWalletId", "createdAt", "updatedAt"); err != nil {
		return nil, err
	}

	return &PayinsCollection{coll}, nil
}

func handlePayinsWebhookEvents(payinsColl *PayinsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handlePayinsWebhookEvents").With(zap.String("event", event.EventName)).With(zap.String("eventId", event.ID))

	payins, err := treezor.UnwrapPayinsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		logging.L.Error("handlePayinsWebhookEvents(): failed to unwrap Payins", zap.Error(err))
		return
	}

	for i, payin := range payins {
		payin.WebhookTimestamp = event.TreezorTimestamp

		if _, err = treezor.SyncActualEntity(L, payinsColl, payin.TreezorPayinID, "treezorPayinId", payin); err != nil {
			L.Error("Failed syncing user", zap.Error(err), zap.Int("i", i), zap.Any("treezorPayinId", payin.TreezorPayinID))
		}
	}
}
