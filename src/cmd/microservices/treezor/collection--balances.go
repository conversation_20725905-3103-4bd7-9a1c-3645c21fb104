package main

import (
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type BalanceSnapshotsCollection struct {
	dbnosql.DataCollection
}

func (c *BalanceSnapshotsCollection) GetOneSnapshot(treezorWalletID int64, webhookTimestamp int64) (*treezor.Balance, error) {
	q := c.GetQueryBuilder()
	_ = q.SetAnd(
		&dbnosql.Condition{"treezorWalletId", "==", treezorWalletID},
		&dbnosql.Condition{"treezorWebhookTimestamp", "==", webhookTimestamp})
	snapshots, err := c.Find(q)
	if err != nil {
		return nil, err
	}
	if len(snapshots) == 0 {
		return nil, nil
	}
	return snapshots[0].(*treezor.Balance), nil
}

func setupBalancesCollection(db dbnosql.Database) (*BalanceSnapshotsCollection, error) {
	coll := db.DeclareCollection("treezor-balances+", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.Balance{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorWalletId", Order: dbnosql.OrderASC},
			{Name: "treezorWebhookTimestamp", Order: dbnosql.OrderASC},
		},
		Name: "snapshotQuery",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "createdAt"); err != nil {
		return nil, err
	}

	return &BalanceSnapshotsCollection{coll}, nil
}

func handleBalancesWebhookEvents(balancesColl *BalanceSnapshotsCollection, walletsColl *WalletsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleBalancesWebhookEvents").With(zap.String("event", event.EventName)).With(zap.String("eventId", event.ID))

	balances, err := treezor.UnwrapBalancesFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed unwrapping Balances from Treezor response", zap.Error(err))
		return
	}

	// Create a new entry of Balance in the database
	for _, tbalance := range balances {
		// Check that the same snapshot does not already exist
		existing, err := balancesColl.GetOneSnapshot(tbalance.TreezorWalletID, event.TreezorTimestamp)
		if err != nil {
			L.Error("Failed checking if the Balance snapshot already exists", zap.Error(err), zap.Any("event", event))
			continue
		}
		if existing != nil {
			L.Warn("Skipping New Balance snapshot insertion, it already exists", zap.Any("event", event), zap.Any("existing", existing))
			continue
		}

		// NOTE: Balance is a snapshot, so we need to create a new entry for each
		//

		tbalance.ID = balancesColl.GenerateUniqueId()
		tbalance.CreatedAt = time.Now()
		tbalance.WebhookTimestamp = event.TreezorTimestamp

		if _, err = balancesColl.Insert(tbalance); err != nil {
			L.Error("Failed inserting new Balance snapshot into the database", zap.Error(err))
			continue
		}
		L.Info("New snapshot Balance inserted into the database", zap.String("id", tbalance.ID))

		// Update the corresponding Wallet's balance
		twallet, err := walletsColl.GetOneByTreezorId(tbalance.TreezorWalletID)
		if err != nil {
			L.Error("Failed getting the corresponding Wallet", zap.Error(err), zap.Int64("treezorWalletId", tbalance.TreezorWalletID))
			continue
		}
		if twallet == nil {
			L.Error("Failed getting the corresponding Wallet, it does not exist", zap.Int64("treezorWalletId", tbalance.TreezorWalletID))
			continue
		}
		twallet.UpdateFromBalance(tbalance)
		if err = walletsColl.Update(twallet); err != nil {
			L.Error("Failed updating the corresponding Wallet", zap.Error(err), zap.Any("wallet", twallet))
			continue
		}
		L.Info("Treezor Wallet also updated with the new Balance; this will reflect to the user", zap.String("id", twallet.ID))
	}
}
