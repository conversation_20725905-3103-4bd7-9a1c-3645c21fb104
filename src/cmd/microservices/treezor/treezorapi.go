package main

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"
	"yochbee/_base/config"
	"yochbee/_base/random"
	"yochbee/_base/web"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go.uber.org/zap"
)

const (
	yochbeeUserAgent = "YoChBee/v1.0"
)

var (
	allowTokensLogging = strings.TrimSpace(os.Getenv("ALLOW_TOKENS_LOGGING")) == "true"

	tokenRequestChan = make(chan tokenRequest)

	configLock            = &sync.Mutex{}
	clientId              = ""
	clientSecret          = ""
	coreConnectApiBaseUrl = ""
	tariffId              = int64(0)
	tariffIdAvailable     = make(chan struct{}) // by default, not available
	cardPrint             = int64(0)

	apiHttpClient = &http.Client{
		Timeout: 10 * time.Second,
	}

	isScaEnabled bool
)

type tokenRequest struct {
	token chan string
}

// setupTreezorInternalTokenAcquisition is the mechanism that ensures that there is always a token ready to be used for Treezor
// API access. Its result is not used outside of this microservice, but the side effect is what other microservices will
// want and use.
func setupTreezorInternalTokenAcquisition(config config.Provider) error {
	err := config.GetLive(context.Background(), "config.treezor", func(data []byte) {
		da := gjson.GetManyBytes(data, "clientId", "clientSecret", "coreConnectApiBaseUrl", "tariffId", "cardPrint", "isScaEnabled")
		configLock.Lock()
		defer configLock.Unlock()

		clientId = da[0].String()
		clientSecret = da[1].String()
		coreConnectApiBaseUrl = da[2].String()
		tariffId = da[3].Int()
		cardPrint = da[4].Int()
		isScaEnabled = da[5].Bool()
		if !da[0].Exists() || !da[1].Exists() || !da[2].Exists() || !da[3].Exists() || !da[4].Exists() {
			panic("incomplete keys in config.treezor")
		}

		if !accounts.EnableSCA {
			logging.L.Info("SCA is disabled (from accounts.EnableSCA == false)")
			isScaEnabled = false
		}

		if allowTokensLogging {
			logging.L.Info("Treezor configuration updated",
				zap.String("clientId", clientId),
				zap.String("clientSecret", clientSecret),
				zap.String("coreConnectApiBaseUrl", coreConnectApiBaseUrl),
				zap.Int64("tariffId", tariffId),
				zap.Int64("cardPrint", cardPrint),
				zap.Bool("isScaEnabled", isScaEnabled))
		}

		select {
		case <-tariffIdAvailable:
			// Already closed
		default:
			close(tariffIdAvailable) // from this point on, it's available
		}
	})
	if err != nil {
		return err
	}

	// How do we get a new token?
	httpClient := &http.Client{}
	acquireToken := func() (string, error) {
		// Build fresh request based on (possibly) new parameters
		payload := url.Values{}
		payload.Set("grant_type", "client_credentials")
		configLock.Lock()
		payload.Set("client_id", clientId)
		payload.Set("client_secret", clientSecret)
		configLock.Unlock()

		resp, err := httpClient.Post(coreConnectApiBaseUrl+"/oauth/token", "application/x-www-form-urlencoded", strings.NewReader(payload.Encode()))
		if err != nil {
			return "", err
		}
		defer resp.Body.Close()

		// Get the new token
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}

		// Check if there was an error
		if jl := gjson.GetManyBytes(data, "errors.0.message", "access_token"); jl[0].Exists() {
			return "", fmt.Errorf("error from Treezor: %s", jl[0].String())
		} else if !jl[1].Exists() {
			logging.L.Error("No access_token in response from Treezor", zap.String("response", string(data)))
			return "", errors.New("no access_token in response from Treezor")
		} else {
			token := jl[1].String()
			if token == "" {
				logging.L.Error("Empty token from Treezor", zap.String("response", string(data)))
				return "", errors.New("empty token from Treezor")
			}
			if allowTokensLogging {
				logging.L.Info("New Access Token acquired", zap.String("token", jl[1].String()))
			} else {
				logging.L.Info("New Access Token acquired")
			}
			return jl[1].String(), nil
		}
	}

	// At the start, we'll wait for initial token to be available before answering requests
	var (
		requests     chan tokenRequest = nil
		timer                          = time.NewTimer(0) // immediately fires on the first wait
		currentToken                   = ""
	)

	// Start the core loop in a different gorouting to return control to FX
	go func() {
		for {
			select {
			case <-timer.C:
				// Retry forever until we get a proper token
				for {
					token, err := acquireToken()
					if err != nil {
						time.Sleep(2 * time.Second)
						continue
					}
					currentToken = token
					break
				}

				// Only after 55 minutes again will we need another token
				timer.Reset(55 * time.Minute)

				// Receive requests to get tokens from now on ...
				requests = tokenRequestChan

			case req := <-requests:
				// Simply return the current token, which is guaranteed to be valid if used immediately
				req.token <- currentToken
			}
		}
	}()
	return nil
}

func getTreezorToken(cache *goredislib.Client, useScaSessionJWT bool, treezorUserId int64) (string, error) {
	if isScaEnabled && useScaSessionJWT {
		if treezorUserId == 0 {
			return "", errors.New("cannot use SCA session JWT without a treezorUserId")
		}

		// Get the currently stored SCA session JWT for this user
		sessionKey := fmt.Sprintf("treezorSCAJwt:session:%d", treezorUserId)
		scaSessionJWT, err := cache.Get(context.Background(), sessionKey).Result()
		if err != nil {
			if err.Error() != "redis: nil" {
				return "", err
			}
			// Not found in cache
			return "", treezor.ErrorNoMoreScaSessionJWT
		}
		return scaSessionJWT, nil
	}

	// Use the standard token
	req := tokenRequest{token: make(chan string)}
	tokenRequestChan <- req
	return <-req.token, nil
}

const treezorApiCallTries = 4

// callTreezorApi calls the Treezor API with the provided HTTP verb, path, payload, and other necessary information. It handles retries, SCA requirements, and error processing.
//
// Parameters:
// - verb: HTTP verb to use for the API call.
// - path: URL path for the API call.
// - jsonAblePayload: Payload to be sent in JSON format.
// - unmarshalResponseIntoAddr: Address to unmarshal the API response into.
// - addAccessTag: Flag to indicate if an access tag should be added to the payload.
// - treezorUserId: ID of the Treezor user for SCA lookup.
// - cache: Redis client for accessing cached SCA proofs.
// - returnValues: Optional pointer to a TreezorReturnValues struct to store additional return values.
//
// Return:
// - rbytes: Response bytes from the API call.
// - err: Error encountered during the API call.
func callTreezorApi(verb, path string, jsonAblePayload interface{}, unmarshalResponseIntoAddr interface{}, addAccessTag bool, treezorUserId int64, cache *goredislib.Client, returnValues ...*treezor.TreezorReturnValues) (rbytes []byte, err error) {
	// Logging
	finalStatus := 0
	finalStatusPtr := &finalStatus
	L := logging.L.Named("callTreezorApi()").With(zap.String("verb", verb), zap.String("path", path))
	L.Info("Started {verb} {path}")
	defer L.Info("Finished {verb} {path} — {status}", zap.Int("status", finalStatus))

	if path == "" || path[0] != '/' {
		return nil, errors.New("invalid path for callTreezorApi")
	}

	// Do we need SCA?
	configLock.Lock()
	requiresSca := isScaEnabled && treezorUserId != 0
	configLock.Unlock()

	// Prepare accessTag if applicable
	accessTag := ""
	if addAccessTag && (verb == "POST" || verb == "PUT") {
		accessTag = random.Strings(39)
	}

	var shouldNotRetryErr error = nil
	for i := 0; i < treezorApiCallTries && shouldNotRetryErr == nil; i++ {
		L := L.With(zap.Int("try", i+1)) // new logger without past information

		// Get a proper API base URL
		configLock.Lock()
		urll := coreConnectApiBaseUrl + path
		configLock.Unlock()

		shasum := sha256.Sum256([]byte(urll))
		urlHash := hex.EncodeToString(shasum[:])
		if err != nil {
			L.Error("Error hashing URL", zap.Error(err))
			return nil, err
		}
		cacheKey := fmt.Sprintf("treezorSCAJws:operation:%d:%s", treezorUserId, urlHash)
		L.Info("SCA requirement: {requiresSca}", zap.Bool("requiresSca", requiresSca), zap.Bool("isScaEnabled", isScaEnabled),
			zap.String("cacheKey", cacheKey), zap.Int64("treezorUserId", treezorUserId), zap.String("url", urll))

		// This will be populated if SCA is enabled and the operation needs it
		perOperationScaJws := ""

		if requiresSca {
			// Redis: check if there is an operation of the requesting user matching this current operation
			perOperationScaJws, err = cache.Get(context.Background(), cacheKey).Result()
			if err != nil {
				if err.Error() != "redis: nil" {
					L.Error("Error getting scaJws from cache", zap.Error(err))
					return nil, err
				}
				// If the error is "redis: nil", just log it and continue ... it means that the scaJws is not in the cache
				L.Info("scaJws not found in cache", zap.Error(err), zap.String("cacheKey", cacheKey), zap.String("url", urll))
			}

			if perOperationScaJws != "" {
				// If we have an scaJws, we can use it and continue
				if allowTokensLogging {
					L.Info("Using SCA per-operation JWS 🟢", zap.String("scaJws", perOperationScaJws))
				} else {
					L.Info("Using SCA per-operation JWS 🟢")
				}
			}
		}

		// Add SCA proof to URL query string for GET/DELETE requests (must be done before request creation)
		if requiresSca && perOperationScaJws != "" && (verb == "GET" || verb == "DELETE") {
			urll, err = web.AddQueryParamToUrl(urll, "sca", perOperationScaJws)
			if err != nil {
				L.Error("Error adding scaProof into query string", zap.Error(err))
				return nil, fmt.Errorf("failed adding `scaProof` to the query string: %w", err)
			}

			if allowTokensLogging {
				L.Info("Added SCA proof 🟢 JWS to query string", zap.String("scaJws", perOperationScaJws), zap.String("url", urll))
			} else {
				L.Info("Added SCA proof 🟢 JWS to query string", zap.String("url", urll))
			}
		}

		// Marshal into JSON; all Treezor API that accepts a data are expecting JSON
		var (
			req          *http.Request
			bytesPayload []byte
		)
		if jsonAblePayload != nil {
			bytesPayload, err = json.Marshal(jsonAblePayload)
			if err != nil {
				L.Error("Error marshalling payload", zap.Error(err))
				return nil, err
			}

			// Add the access tag if requested and it was applicable
			if addAccessTag && accessTag != "" {
				bytesPayload, err = sjson.SetBytes(bytesPayload, "accessTag", accessTag)
				if err != nil {
					L.Error("Error adding accessTag into JSON", zap.Error(err))
					return nil, fmt.Errorf("failed adding `accessTag` to the JSON outgoing payload: %w", err)
				}
			}

			// Add SCA proof to JSON body for POST/PUT requests
			if requiresSca && perOperationScaJws != "" && (verb == "POST" || verb == "PUT") {
				bytesPayload, err = sjson.SetBytes(bytesPayload, "sca", perOperationScaJws)
				if err != nil {
					L.Error("Error adding scaProof into JSON", zap.Error(err))
					return nil, fmt.Errorf("failed adding `scaProof` to the JSON outgoing payload: %w", err)
				}

				if allowTokensLogging {
					L.Info("Added SCA proof 🟢 JWS to JSON body", zap.String("scaJws", perOperationScaJws))
				} else {
					L.Info("Added SCA proof 🟢 JWS to JSON body")
				}
			}

			// Prepare the request that contains a payload
			req, err = http.NewRequest(verb, urll, bytes.NewReader(bytesPayload))
			if err != nil {
				L.Error("Error creating request", zap.Error(err))
				return nil, err
			}
		} else {
			req, err = http.NewRequest(verb, urll, nil)
			if err != nil {
				L.Error("Error creating request", zap.Error(err))
				return nil, err
			}
		}
		L = L.With(zap.ByteString("payload", bytesPayload))

		// Headers that must be sent for all Treezor API requests
		if requiresSca {
			scaUserToken, err := getTreezorToken(cache, true, treezorUserId)
			if err != nil {
				L.Error("Error getting SCA session JWT", zap.Error(err))
				return nil, err
			}
			req.Header.Set("Authorization", "Bearer "+scaUserToken)

			if allowTokensLogging {
				L.Info("Adding SCA session JWT 🟢 in `Authorization` header", zap.String("jwt", scaUserToken))
			} else {
				L.Info("Adding SCA session JWT 🟢 in `Authorization` header")
			}
		} else {
			token, err := getTreezorToken(cache, false, 0)
			if err != nil {
				L.Error("Error getting Treezor standard token", zap.Error(err))
				return nil, err
			}
			req.Header.Set("Authorization", "Bearer "+token)

			if allowTokensLogging {
				L.Info("Adding standard JWT in `Authorization` header", zap.String("jwt", token))
			} else {
				L.Info("Adding standard JWT in `Authorization` header")
			}
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", yochbeeUserAgent)

		// Execute and get the response
		response, err := apiHttpClient.Do(req)
		if err != nil {
			L.Error("FAILED", zap.Error(err))
			return nil, err
		}
		*finalStatusPtr = response.StatusCode

		// Process the response
		err = func() error {
			defer response.Body.Close()

			if unmarshalResponseIntoAddr != nil {
				// Send a copy of the response to the log inside bytesResponse
				bytesResponse := bytes.NewBuffer(make([]byte, 0, 4096))
				bytesResponse.Reset()
				reader := io.TeeReader(response.Body, bytesResponse)

				// Decode the response into the user-given unmarshalResponseIntoAddr, streaming
				jdec := json.NewDecoder(reader)
				if err = jdec.Decode(unmarshalResponseIntoAddr); err != nil {
					L.Error("FAILED", zap.Error(err))
					return err
				}

				// All successful
				rbytes = bytesResponse.Bytes()
			} else {
				rbytes, err = io.ReadAll(response.Body)
				if err != nil {
					L.Error("FAILED", zap.Error(err))
					return err
				}
			}

			// Check to make sure that we're not getting an error
			gerr := gjson.GetBytes(rbytes, "errors.0.message")
			if gerr.Exists() {
				// If we have an error here, it is most likely something that cannot be retried and it will
				// keep failing even if we retried
				shouldNotRetryErr = fmt.Errorf("error in Treezor API call: %s", gerr.String())
				L.Error("Treezor error", zap.Error(shouldNotRetryErr), zap.ByteString("response", rbytes))

				// Populate returnValues if provided
				if len(returnValues) > 0 {
					ret := returnValues[0]
					ret.StatusCode = response.StatusCode
					ret.ErrorCode = gjson.GetBytes(rbytes, "errors.0.code").String()
					ret.ErrorMsg = gerr.String()
				}

				return shouldNotRetryErr
			}

			L.Info("Success", zap.ByteString("response", rbytes))
			return nil
		}()

		if err != nil {
			if i+1 < treezorApiCallTries {
				// Wait a while, and retry
				time.Sleep(time.Duration(i+1) * time.Second)
			}
		} else {
			// Populate returnValues if provided
			if len(returnValues) > 0 {
				ret := returnValues[0]
				ret.StatusCode = response.StatusCode
			}
			return rbytes, nil
		}
	}

	if shouldNotRetryErr != nil {
		return nil, shouldNotRetryErr
	}

	L.Error("FAILED after all retries")
	return nil, fmt.Errorf("failed after %d tries", treezorApiCallTries)
}
