package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"
)

type SCAWalletsCollection struct {
	dbnosql.DataCollection
}

func setupSCAWalletsCollection(db dbnosql.Database) (*SCAWalletsCollection, error) {
	coll := db.DeclareCollection("treezor-sca-wallets+", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.SCAWallet{}
	})

	if err := coll.SetMultipleIndexesOn("userId", "createdAt"); err != nil {
		return nil, err
	}

	return &SCAWalletsCollection{coll}, nil
}

func (c *SCAWalletsCollection) GetSCAWalletByUserId(userId int64) (*treezor.SCAWallet, error) {
	q := c.GetQueryBuilder()
	_ = q.Set(&dbnosql.Condition{"userId", "==", userId})
	q.SetOrder<PERSON>yField("createdAt", dbnosql.OrderDESC)
	q.SetLimit(1)

	scaWallets, err := c.Find(q)
	if err != nil {
		return nil, err
	}
	if len(scaWallets) == 0 {
		return nil, nil
	}

	return scaWallets[0].(*treezor.SCAWallet), nil
}

func (c *SCAWalletsCollection) GetByTreezorID(walletId string) (*treezor.SCAWallet, error) {
	q := c.GetQueryBuilder()
	// We have stored the Treezor ID as string in the treezorId field because Treezor already generated the ID for us
	_ = q.Set(&dbnosql.Condition{"treezorId", "==", walletId})
	q.SetLimit(1)

	scaWallets, err := c.Find(q)
	if err != nil {
		return nil, err
	}
	if len(scaWallets) == 0 {
		return nil, nil
	}

	return scaWallets[0].(*treezor.SCAWallet), nil
}

func handleSCAWalletWebhookEvents(scaWalletsColl *SCAWalletsCollection, event *treezor.WebhookEvent) {
	scaWallet, err := treezor.UnwrapSingleSCAWalletFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		return
	}

	// Assign a unique ID to the SCAWallet
	scaWallet.ID = scaWalletsColl.GenerateUniqueId()

	if _, err = scaWalletsColl.Insert(scaWallet); err != nil {
		return
	}
}
