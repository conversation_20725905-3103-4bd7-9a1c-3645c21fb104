package main

import (
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/_base/utils"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type CardTransactionsCollection struct {
	dbnosql.DataCollection
}

func setupCardTransactionsCollection(db dbnosql.Database) (*CardTransactionsCollection, error) {
	coll := db.DeclareCollection("treezor-cardtransactions+", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.CardTransaction{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorCardId", Order: dbnosql.OrderASC},
			{Name: "treezorWebhookTimestamp", Order: dbnosql.OrderASC},
		},
		Name: "snapshotQuery",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetMultipleIndexesOn("treezorWebhookTimestamp", "createdAt"); err != nil {
		return nil, err
	}

	return &CardTransactionsCollection{coll}, nil
}

func (c *CardTransactionsCollection) GetCurrentCardTransaction(cardTransactionId int64) (*treezor.CardTransaction, error) {
	q := c.GetQueryBuilder()
	_ = q.Set(&dbnosql.Condition{"treezorId", "==", cardTransactionId})
	q.SetOrderByField("treezorWebhookTimestamp", dbnosql.OrderDESC)
	q.SetLimit(1)
	snapshots, err := c.Find(q)
	if err != nil {
		return nil, err
	}
	if len(snapshots) == 0 {
		return nil, nil
	}
	return snapshots[0].(*treezor.CardTransaction), nil
}

func handleCardTransactionsWebhookEvents(cardTransactionsColl *CardTransactionsCollection, event *treezor.WebhookEvent) {
	L := logging.L.Named("handleCardTransactionsWebhookEvents").With(zap.String("event", event.EventName)).With(zap.String("eventId", event.ID))

	cardTransactions, err := treezor.UnwrapCardTransactionsFromTreezorResponse([]byte(event.Payload.(string)))
	if err != nil {
		L.Error("Failed unwrapping CardTransactions from Treezor response", zap.Error(err), zap.Any("eventPayload", event.Payload))
		return
	}

	for _, cardTransaction := range cardTransactions {
		cardTransaction.TreezorWebhookTimestamp = event.TreezorTimestamp

		L = L.With(
			zap.String("cardTransactionId", cardTransaction.ID),
			zap.Int64("treezorId", cardTransaction.TreezorId))

		// Always insert the cardTransaction in the database, as it's a snapshot in time
		cardTransaction.ID = cardTransactionsColl.GenerateUniqueId()
		cardTransaction.CreatedAt = utils.DynamicFormatTime{Time: time.Now()}
		if _, err = cardTransactionsColl.Insert(cardTransaction); err != nil {
			L.Error("Failed inserting CardTransaction in database", zap.Error(err), zap.Any("cardTransaction", cardTransaction))
			continue
		}
		L.Info("CardTransaction snapshot inserted in database", zap.Any("cardTransaction", cardTransaction))
	}
}
