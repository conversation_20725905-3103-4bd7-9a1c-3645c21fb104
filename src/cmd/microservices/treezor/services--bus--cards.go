package main

import (
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	goredislib "github.com/redis/go-redis/v9"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

func registerCardsBusMethods(busrpc bus2.RpcClient, cardsColl *CardSnapshotsCollection, cache *goredislib.Client) error {
	saveCardSnapshotFromTreezorResponse := func(L *zap.Logger, rbytes []byte) (*treezor.Card, error) {
		// Get the Card objects
		cards, err := treezor.UnwrapCardsFromTreezorResponse(rbytes)
		if err != nil {
			L.Error("Error unwrapping Cards from Treezor API response", zap.Error(err))
			return nil, err
		}
		if len(cards) == 0 {
			<PERSON>.<PERSON>rror("No card returned from Treezor API")
			return nil, errors.New("no card returned from Treezor API")
		}

		// Save in the Treezor DB
		cardSnapshot := cards[0]
		// NOTE: This ID is meaningless as it is a snapshot; the treezorCardId is the one that is a good reference
		cardSnapshot.ID = cardsColl.GenerateUniqueId()
		cardSnapshot.CreatedAt = time.Now()
		// Assign a non-0 timestamp
		cardSnapshot.WebhookTimestamp = time.Now().UnixMilli()
		if _, err := cardsColl.Insert(cardSnapshot); err != nil {
			L.Error("Error inserting Treezor Card into the DB", zap.Error(err))
			return nil, err
		}
		L.Info("✅ Inserted Treezor Card into the DB: {id}", zap.String("id", cardSnapshot.ID))

		return cardSnapshot, nil
	}

	// ╔═╗┌─┐┌┬┐  ╔═╗┬─┐┌─┐┌─┐┬ ┬  ╔═╗┌─┐┬─┐┌┬┐
	// ║ ╦├┤  │   ╠╣ ├┬┘├┤ └─┐├─┤  ║  ├─┤├┬┘ ││
	// ╚═╝└─┘ ┴   ╚  ┴└─└─┘└─┘┴ ┴  ╚═╝┴ ┴┴└──┴┘

	if err := bus2.RegisterUM(treezor.Method_GetFreshCard, busrpc, func(request *treezor.GetFreshCardRequest) (*treezor.GetFreshCardResponse, error) {
		L := logging.L.Named(treezor.Method_GetFreshCard).With(
			zap.Int64("treezorUserId", request.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		rbytes, err := callTreezorApi(http.MethodGet, fmt.Sprintf("/v1/cards/%d", request.TreezorCardId), nil, nil, true, 0, cache) // no SCA
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.GetFreshCardResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╔═╗┬─┐┌┬┐┌─┐┬─┐  ╔═╗╦ ╦╦ ╦╔═╗╦╔═╗╔═╗╦    ╔═╗┌─┐┬─┐┌┬┐
	// ║ ║├┬┘ ││├┤ ├┬┘  ╠═╝╠═╣╚╦╝╚═╗║║  ╠═╣║    ║  ├─┤├┬┘ ││
	// ╚═╝┴└──┴┘└─┘┴└─  ╩  ╩ ╩ ╩ ╚═╝╩╚═╝╩ ╩╩═╝  ╚═╝┴ ┴┴└──┴┘
	if err := bus2.RegisterUM(treezor.Method_OrderCard, busrpc,
		func(request *treezor.OrderCardRequest) (*treezor.OrderCardResponse, error) {
			L := logging.L.Named(treezor.Method_OrderCard).With(
				zap.String("accountId", request.Account.ID),
				zap.Int64("treezorUserId", request.Account.TreezorUserId),
				zap.Bool("isPhysical", request.IsPhysical),
			)
			L.Info("Started")
			defer L.Info("Finished")

			urlPath := "/v1/cards/CreateVirtual"
			if request.IsPhysical {
				urlPath = "/v1/cards/RequestPhysical"
			}

			configLock.Lock()
			cardPrintValue := cardPrint
			configLock.Unlock()

			// Prepare the payload
			payload := map[string]interface{}{
				"userId":     request.Account.TreezorUserId,
				"walletId":   request.Account.TreezorPrimaryWalletId,
				"permsGroup": "TRZ-CU-016", // FIXME: in the future, if needed, make this map from the request's Option* fields
				"cardTag":    fmt.Sprintf("account-%s-%v", request.Account.ID, request.IsPhysical),
				"cardPrint":  fmt.Sprintf("%d", cardPrintValue),
				"pin":        request.PinCode,
			}
			if request.DailyLimits.Withdrawals > 0 {
				payload["limitAtmDay"] = request.DailyLimits.Withdrawals
			}
			if request.WeeklyLimits.Withdrawals > 0 {
				payload["limitAtmWeek"] = request.WeeklyLimits.Withdrawals
			}
			if request.MonthlyLimits.Withdrawals > 0 {
				payload["limitAtmMonth"] = request.MonthlyLimits.Withdrawals
			}
			if request.YearlyLimits.Withdrawals > 0 {
				payload["limitAtmYear"] = request.YearlyLimits.Withdrawals
			}
			if request.GlobalLimits.Withdrawals > 0 {
				payload["limitAtmAll"] = request.GlobalLimits.Withdrawals
			}
			if request.DailyLimits.Payments > 0 {
				payload["limitPaymentDay"] = request.DailyLimits.Payments
			}
			if request.WeeklyLimits.Payments > 0 {
				payload["limitPaymentWeek"] = request.WeeklyLimits.Payments
			}
			if request.MonthlyLimits.Payments > 0 {
				payload["limitPaymentMonth"] = request.MonthlyLimits.Payments
			}
			if request.YearlyLimits.Payments > 0 {
				payload["limitPaymentYear"] = request.YearlyLimits.Payments
			}
			if request.GlobalLimits.Payments > 0 {
				payload["limitPaymentAll"] = request.GlobalLimits.Payments
			}

			rbytes, err := callTreezorApi(http.MethodPost, urlPath, payload, nil, true, request.Account.TreezorUserId, cache)
			if err != nil {
				L.Error("Failed to call Treezor API", zap.Error(err))
				return nil, err
			}

			cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
			if err != nil {
				return nil, err
			}

			return &treezor.OrderCardResponse{
				Card: cardSnapshot,
			}, nil

		}, true); err != nil {
		return err
	}

	// ╔═╗╔═╗╔╦╗╦╦  ╦╔═╗╔╦╗╔═╗  ╔═╗┌─┐┬─┐┌┬┐
	// ╠═╣║   ║ ║╚╗╔╝╠═╣ ║ ║╣   ║  ├─┤├┬┘ ││
	// ╩ ╩╚═╝ ╩ ╩ ╚╝ ╩ ╩ ╩ ╚═╝  ╚═╝┴ ┴┴└──┴┘
	if err := bus2.RegisterUM(treezor.Method_ActivateCard, busrpc, func(request *treezor.ActivateCardRequest) (*treezor.ActivateCardResponse, error) {
		L := logging.L.Named(treezor.Method_ActivateCard).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/Activate", request.TreezorCardId), nil, nil, true, request.Account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.ActivateCardResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╦═╗┌─┐┌─┐┬┌─┐┌┬┐┌─┐┬─┐  ╔╦╗╦ ╦╦═╗╔═╗╔═╗  ╔╦╗╔═╗
	// ╠╦╝├┤ │ ┬│└─┐ │ ├┤ ├┬┘   ║ ╠═╣╠╦╝║╣ ║╣    ║║╚═╗
	// ╩╚═└─┘└─┘┴└─┘ ┴ └─┘┴└─   ╩ ╩ ╩╩╚═╚═╝╚═╝  ═╩╝╚═╝
	if err := bus2.RegisterUM(treezor.Method_RegisterCard3DS, busrpc, func(request *treezor.RegisterCard3DSRequest) (*treezor.RegisterCard3DSResponse, error) {
		L := logging.L.Named(treezor.Method_RegisterCard3DS).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// See also: https://docs.treezor.com/guide/cards/creation.html#enrolling-a-card-for-3d-secure
		// All our users have international phone numbers with the strict processing that we did
		rbytes, err := callTreezorApi(http.MethodPost, fmt.Sprintf("/v1/cards/Register3DS?cardId=%d", request.TreezorCardId), nil, nil, false, 0, cache) // no SCA
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.RegisterCard3DSResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╔═╗┌┐┌┬─┐┌─┐┬  ┬    ╔═╗┌─┐┬─┐┌┬┐
	// ║╣ │││├┬┘│ ││  │    ║  ├─┤├┬┘ ││
	// ╚═╝┘└┘┴└─└─┘┴─┘┴─┘  ╚═╝┴ ┴┴└──┴┘
	if err := bus2.RegisterUM(treezor.Method_EnrollCard, busrpc, func(request *treezor.EnrollCardRequest) (*treezor.EnrollCardResponse, error) {
		L := logging.L.Named(treezor.Method_EnrollCard).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		response, err := callTreezorApi(http.MethodPost, fmt.Sprintf("/v1/cards/%d/authentication-methods", request.TreezorCardId), map[string]interface{}{
			"method": "OOB",
		}, nil, false, request.Account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		// Get the authenticationMethodId
		authenticationMethodId := gjson.GetBytes(response, "id")
		if !authenticationMethodId.Exists() {
			L.Error("Card Enrollment failed")
			return &treezor.EnrollCardResponse{
				Ok: false,
			}, nil
		}

		return &treezor.EnrollCardResponse{
			Ok: true,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╦╔═┌┐┌┌─┐┬ ┬┌┐┌  ╔═╗╦╔╗╔
	// ║ ║├─┘ ││├─┤ │ ├┤   ╠╩╗││││ │││││││  ╠═╝║║║║
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ╩ ╩┘└┘└─┘└┴┘┘└┘  ╩  ╩╝╚╝
	if err := bus2.RegisterUM(treezor.Method_UpdateKnownCardPIN, busrpc, func(request *treezor.UpdateKnownCardPINRequest) (*treezor.UpdateKnownCardPINResponse, error) {
		L := logging.L.Named(treezor.Method_UpdateKnownCardPIN).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// See also: https://docs.treezor.com/guide/cards/modification.html#with-the-previous-pin
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/ChangePIN", request.TreezorCardId), map[string]interface{}{
			"currentPIN": request.CurrentPIN,
			"newPIN":     request.NewPIN,
			"confirmPIN": request.NewPIN,
		}, nil, true, request.Account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.UpdateKnownCardPINResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╔╗ ╦  ╔═╗╔═╗╦╔═╦╔╗╔╔═╗  ╔═╗┌┬┐┌─┐┌┬┐┬ ┬┌─┐
	// ║ ║├─┘ ││├─┤ │ ├┤   ╠╩╗║  ║ ║║  ╠╩╗║║║║║ ╦  ╚═╗ │ ├─┤ │ │ │└─┐
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ╚═╝╩═╝╚═╝╚═╝╩ ╩╩╝╚╝╚═╝  ╚═╝ ┴ ┴ ┴ ┴ └─┘└─┘
	if err := bus2.RegisterUM(treezor.Method_UpdateCardBlockingStatus, busrpc, func(request *treezor.UpdateCardBlockingStatusRequest) (*treezor.UpdateCardBlockingStatusResponse, error) {
		L := logging.L.Named(treezor.Method_UpdateCardBlockingStatus).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// See also: https://docs.treezor.com/guide/cards/modification.html#block-a-card
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/LockUnlock", request.TreezorCardId), map[string]interface{}{
			"lockStatus": request.LockStatus,
		}, nil, true, request.Account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.UpdateCardBlockingStatusResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╔═╗╔═╗╦═╗╔╦╗  ╦  ┬┌┬┐┬┌┬┐┌─┐
	// ║ ║├─┘ ││├─┤ │ ├┤   ║  ╠═╣╠╦╝ ║║  ║  │││││ │ └─┐
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ╚═╝╩ ╩╩╚══╩╝  ╩═╝┴┴ ┴┴ ┴ └─┘
	if err := bus2.RegisterUM(treezor.Method_UpdateCardLimits, busrpc, func(request *treezor.UpdateCardLimitsRequest) (*treezor.UpdateCardLimitsResponse, error) {
		L := logging.L.Named(treezor.Method_UpdateCardLimits).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// See also: https://docs.treezor.com/guide/cards/modification.html#payments-withdrawals-limits
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/Limits", request.TreezorCardId), map[string]interface{}{
			"limitPaymentDay":   request.DailyLimits.Payments,
			"limitPaymentWeek":  request.WeeklyLimits.Payments,
			"limitPaymentMonth": request.MonthlyLimits.Payments,
			"limitPaymentYear":  request.YearlyLimits.Payments,
			"limitPaymentAll":   request.GlobalLimits.Payments,
			"limitAtmDay":       request.DailyLimits.Withdrawals,
			"limitAtmWeek":      request.WeeklyLimits.Withdrawals,
			"limitAtmMonth":     request.MonthlyLimits.Withdrawals,
			"limitAtmYear":      request.YearlyLimits.Withdrawals,
			"limitAtmAll":       request.GlobalLimits.Withdrawals,
		}, nil, false, request.Account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.UpdateCardLimitsResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	// ╦ ╦┌─┐┌┬┐┌─┐┌┬┐┌─┐  ╔═╗┌─┐┬─┐┌┬┐  ╔═╗╔═╗╔═╗╔╦╗╦ ╦╦═╗╔═╗╔═╗
	// ║ ║├─┘ ││├─┤ │ ├┤   ║  ├─┤├┬┘ ││  ╠╣ ║╣ ╠═╣ ║ ║ ║╠╦╝║╣ ╚═╗
	// ╚═╝┴  ─┴┘┴ ┴ ┴ └─┘  ╚═╝┴ ┴┴└──┴┘  ╚  ╚═╝╩ ╩ ╩ ╚═╝╩╚═╚═╝╚═╝
	if err := bus2.RegisterUM(treezor.Method_UpdateCardFeatures, busrpc, func(request *treezor.UpdateCardFeaturesRequest) (*treezor.UpdateCardFeaturesResponse, error) {
		L := logging.L.Named(treezor.Method_UpdateCardFeatures).With(
			zap.String("accountId", request.Account.ID),
			zap.Int64("treezorUserId", request.Account.TreezorUserId),
			zap.Int64("treezorCardId", request.TreezorCardId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// See also: https://docs.treezor.com/guide/cards/modification.html#options-change
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/Options", request.TreezorCardId), map[string]interface{}{
			"atm":     request.OptionAtm,
			"online":  request.OptionOnline,
			"nfc":     request.OptionNfc,
			"foreign": request.OptionForeign,
		}, nil, true, request.Account.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.UpdateCardFeaturesResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌─┐┌┐┌┬  ┬┌─┐┬─┐┌┬┐  ╦  ╦╦╦═╗╔╦╗╦ ╦╔═╗╦    ┌┬┐┌─┐  ╔═╗╦ ╦╦ ╦╔═╗╦╔═╗╔═╗╦
	// ║  │ ││││└┐┌┘├┤ ├┬┘ │   ╚╗╔╝║╠╦╝ ║ ║ ║╠═╣║     │ │ │  ╠═╝╠═╣╚╦╝╚═╗║║  ╠═╣║
	// ╚═╝└─┘┘└┘ └┘ └─┘┴└─ ┴    ╚╝ ╩╩╚═ ╩ ╚═╝╩ ╩╩═╝   ┴ └─┘  ╩  ╩ ╩ ╩ ╚═╝╩╚═╝╩ ╩╩═╝
	//
	if err := bus2.RegisterUM(treezor.Method_ConvertVirtualCardToPhysical, busrpc, func(i *treezor.ConvertVirtualCardToPhysicalRequest) (*treezor.ConvertVirtualCardToPhysicalResponse, error) {
		L := logging.L.Named(treezor.Method_ConvertVirtualCardToPhysical).With(
			zap.Int64("treezorCardId", i.TreezorCardId),
			zap.Int64("treezorUserId", i.TreezorUserId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// See also: https://docs.treezor.com/guide/cards/conversion.html#from-virtual-to-physical
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/ConvertVirtual", i.TreezorCardId), map[string]interface{}{}, nil, true, i.TreezorUserId, cache)
		if err != nil {
			L.Error("Failed to call Treezor API", zap.Error(err))
			return nil, err
		}

		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.ConvertVirtualCardToPhysicalResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ╦ ╦┌┐┌┌┐ ┬  ┌─┐┌─┐┬┌─  ┌┬┐┬ ┬┌─┐  ╔═╗╦╔╗╔
	// ║ ║│││├┴┐│  │ ││  ├┴┐   │ ├─┤├┤   ╠═╝║║║║
	// ╚═╝┘└┘└─┘┴─┘└─┘└─┘┴ ┴   ┴ ┴ ┴└─┘  ╩  ╩╝╚╝
	//
	if err := bus2.RegisterUM(treezor.Method_UnblockPIN, busrpc, func(i *treezor.UnblockPINRequest) (*treezor.UnblockPINResponse, error) {
		L := logging.L.Named(treezor.Method_UnblockPIN).With(
			zap.Int64("treezorCardId", i.TreezorCardId),
			zap.Int64("treezorUserId", i.TreezorUserId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// Send a PUT request to Treezor API to unblock the PIN
		// See alos: https://docs.treezor.com/guide/cards/modification.html#pin-unlock
		rbytes, err := callTreezorApi(http.MethodPut, fmt.Sprintf("/v1/cards/%d/UnblockPIN", i.TreezorCardId), map[string]interface{}{}, nil, true, i.TreezorUserId, cache)
		if err != nil {
			return nil, err
		}

		// Save the card snapshot
		cardSnapshot, err := saveCardSnapshotFromTreezorResponse(L, rbytes)
		if err != nil {
			return nil, err
		}
		return &treezor.UnblockPINResponse{
			Card: cardSnapshot,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌─┐┬─┐┌┬┐  ╦╔╦╗╔═╗╔═╗╔═╗
	// ║  ├─┤├┬┘ ││  ║║║║╠═╣║ ╦║╣
	// ╚═╝┴ ┴┴└──┴┘  ╩╩ ╩╩ ╩╚═╝╚═╝
	//
	if err := bus2.RegisterUM(treezor.Method_GetCardImage, busrpc, func(i *treezor.GetCardImageRequest) (*treezor.GetCardImageResponse, error) {
		L := logging.L.Named(treezor.Method_GetCardImage).With(
			zap.Int64("treezorCardId", i.TreezorCardId),
			zap.Int64("treezorUserId", i.TreezorUserId),
		)
		L.Info("Started")
		defer L.Info("Finished")

		// Validate that TreezorUserId is not zero when SCA is enabled
		if isScaEnabled && i.TreezorUserId == 0 {
			L.Error("treezorUserId is required for card image retrieval when SCA is enabled")
			return nil, errors.New("treezorUserId is required for card image retrieval when SCA is enabled")
		}

		// Send a GET request to Treezor API to get the card image
		// See also: https://www.treezor.com/api-documentation/#/card/get_cardimages
		rbytes, err := callTreezorApi(http.MethodGet, fmt.Sprintf("/v1/cardimages?cardId=%d", i.TreezorCardId), nil, nil, false, i.TreezorUserId, cache)
		if err != nil {
			return nil, err
		}

		// Extract the cardimages.0.file JSON field
		rFile := gjson.GetBytes(rbytes, "cardimages.0.file")
		if !rFile.Exists() {
			return nil, errors.New("cardimages.0.file field does not exist from Treezor response")
		}

		// Convert the base64 encoded image to bytes
		rbytes, err = base64.StdEncoding.DecodeString(string(rFile.String()))
		if err != nil {
			return nil, err
		}

		// Detect the mime type
		mime := http.DetectContentType(rbytes)
		L.Info("Detected mime type", zap.String("mime", mime))

		return &treezor.GetCardImageResponse{
			Image: rbytes,
			Mime:  mime,
		}, nil
	}, true); err != nil {
		return err
	}

	//
	// ╔═╗┌─┐┌┬┐  ╦ ╦╔═╗╔═╗╦═╗  ╔═╗╔═╗╦═╗╔╦╗╔═╗
	// ║ ╦├┤  │   ║ ║╚═╗║╣ ╠╦╝  ║  ╠═╣╠╦╝ ║║╚═╗
	// ╚═╝└─┘ ┴   ╚═╝╚═╝╚═╝╩╚═  ╚═╝╩ ╩╩╚══╩╝╚═╝
	//
	if err := bus2.RegisterUM(treezor.Method_GetTreezorUserCards, busrpc, func(i *treezor.GetTreezorUserCardsRequest) (*treezor.GetTreezorUserCardsResponse, error) {
		L := logging.L.Named(treezor.Method_GetTreezorUserCards).With(
			zap.Int64("treezorUserId", i.TreezorUserId))
		L.Info("Started")
		defer L.Info("Finished")

		// Send a GET request to Treezor API to get the cards of the user
		// See also: https://docs.treezor.com/guide/swagger/introduction.html#/card/get_v1_cards
		rbytes, err := callTreezorApi(http.MethodGet, fmt.Sprintf("/v1/cards?userId=%d", i.TreezorUserId), nil, nil, false, i.TreezorUserId, cache)
		if err != nil {
			return nil, err
		}

		// Unwrap the cards from the Treezor response
		cards, err := treezor.UnwrapCardsFromTreezorResponse(rbytes)
		if err != nil {
			return nil, err
		}

		return &treezor.GetTreezorUserCardsResponse{
			Cards: cards,
		}, nil

	}, true); err != nil {
		return err
	}

	return nil
}
