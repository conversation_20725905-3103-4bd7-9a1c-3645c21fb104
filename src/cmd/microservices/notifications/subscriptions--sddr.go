package main

import (
	"fmt"
	"time"

	"yochbee/_base/bus"
	"yochbee/common/accounts"
	"yochbee/common/notifications"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

func subscribeToSDDREvents(busrpc bus.RpcClient, signaling bus.Client, notificationsColl *NotificationsCollection) error {
	notificationsQueue, err := signaling.DeclareSubscriptionQueue("Notifications.Subscriptions-sddr", true, false,
		[]string{
			"treezor.sepa_sddr.reception", // SDDR reception event
			"treezor.sepa.return_sddr",    // SDDR return event
		},
	)
	if err != nil {
		return err
	}

	return signaling.SubscribeToEventForever(notificationsQueue, "notifications/subscriptions--sddr.go", false, func(event *bus.Event, ack func() error) {
		L := logging.L.Named("Consumer(sddr)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		// Parse the JSON data
		eventData := []byte(event.Data.(string))

		switch event.Topic {
		case "treezor.sepa_sddr.reception":
			handleSDDRReception(busrpc, notificationsColl, eventData, L)
		case "treezor.sepa.return_sddr":
			handleSDDRReturn(busrpc, notificationsColl, eventData, L)
		default:
			L.Warn("Unknown SDDR event type")
		}

		if err := ack(); err != nil {
			L.Error("Error while acknowledging message", zap.Error(err))
		}
	})
}

func handleSDDRReception(busrpc bus.RpcClient, notificationsColl *NotificationsCollection, eventData []byte, L *zap.Logger) {
	// Parse the reception event
	walletID := gjson.GetBytes(eventData, "sepaSddr.wallet_id").Int()
	transactionID := gjson.GetBytes(eventData, "sepaSddr.transaction_id").String()
	amount := gjson.GetBytes(eventData, "sepaSddr.interbank_settlement_amount").String()
	mandateID := gjson.GetBytes(eventData, "sepaSddr.mandate_id").String()
	creditorName := gjson.GetBytes(eventData, "sepaSddr.creditor_name").String()
	collectionDate := gjson.GetBytes(eventData, "sepaSddr.requested_collection_date.date").String()

	L.Info("Processing SDDR reception",
		zap.Int64("walletID", walletID),
		zap.String("transactionID", transactionID),
		zap.String("amount", amount),
		zap.String("creditorName", creditorName))

	// Get the account associated with this wallet
	account, err := getAccountByWalletID(busrpc, walletID)
	if err != nil {
		L.Error("Failed to get account for wallet", zap.Int64("walletID", walletID), zap.Error(err))
		return
	}
	if account == nil {
		L.Error("Account not found for wallet", zap.Int64("walletID", walletID))
		return
	}
	if account.PersonalTopic == "" {
		L.Error("User's personalTopic not set", zap.String("accountID", account.ID))
		return
	}

	// Format the collection date for display
	formattedDate := formatCollectionDate(collectionDate)

	// Create the notification
	now := time.Now()
	notification := &notifications.Notification{
		ID:                 notificationsColl.GenerateUniqueId(),
		UserID:             account.ID,
		Title:              "Prélèvement SEPA programmé",
		Message:            fmt.Sprintf("Prélèvement SEPA de %s € prévu le %s par %s", amount, formattedDate, creditorName),
		NotificationType:   "sddr_reception",
		SepaTransactionID:  transactionID,
		SepaMandateID:      mandateID,
		SepaAmount:         amount,
		SepaCreditorName:   creditorName,
		SepaCollectionDate: collectionDate,
		CreatedAt:          now,
		IsRead:             false,
	}

	// Store in database
	if _, err := notificationsColl.Insert(notification); err != nil {
		L.Error("Error while inserting notification into database", zap.Error(err))
		return
	}
	L.Info("Notification inserted into database", zap.Any("notification", notification))

	// Send Firebase push notification
	data := map[string]string{
		"notificationType": "sddr_reception",
		"transactionID":    transactionID,
		"amount":           amount,
		"creditorName":     creditorName,
		"collectionDate":   collectionDate,
	}
	if err := sendFirebaseNotification(account.PersonalTopic, notification.Title, notification.Message, data); err != nil {
		L.Error("Error while sending Firebase notification", zap.Error(err), zap.Any("data", data))
		return
	}
	L.Info("Firebase notification sent", zap.Any("data", data))
}

func handleSDDRReturn(busrpc bus.RpcClient, notificationsColl *NotificationsCollection, eventData []byte, L *zap.Logger) {
	// Parse the return event - note the array structure
	walletID := gjson.GetBytes(eventData, "sepaSddrs.0.wallet_id").Int()
	transactionID := gjson.GetBytes(eventData, "sepaSddrs.0.transaction_id").String()
	amount := gjson.GetBytes(eventData, "sepaSddrs.0.interbank_settlement_amount").String()
	reasonCode := gjson.GetBytes(eventData, "sepaSddrs.0.reason_code").String()
	creditorName := gjson.GetBytes(eventData, "sepaSddrs.0.creditor_name").String()

	L.Info("Processing SDDR return",
		zap.Int64("walletID", walletID),
		zap.String("transactionID", transactionID),
		zap.String("amount", amount),
		zap.String("reasonCode", reasonCode),
		zap.String("creditorName", creditorName))

	// Get the account associated with this wallet
	account, err := getAccountByWalletID(busrpc, walletID)
	if err != nil {
		L.Error("Failed to get account for wallet", zap.Int64("walletID", walletID), zap.Error(err))
		return
	}
	if account == nil {
		L.Error("Account not found for wallet", zap.Int64("walletID", walletID))
		return
	}
	if account.PersonalTopic == "" {
		L.Error("User's personalTopic not set", zap.String("accountID", account.ID))
		return
	}

	// Get user-friendly French reason text
	reasonText := notifications.GetFrenchReasonText(reasonCode)

	// Create the notification
	now := time.Now()
	notification := &notifications.Notification{
		ID:                notificationsColl.GenerateUniqueId(),
		UserID:            account.ID,
		Title:             "Prélèvement SEPA rejeté",
		Message:           fmt.Sprintf("Prélèvement SEPA rejeté : %s - Montant %s € par %s", reasonText, amount, creditorName),
		NotificationType:  "sddr_return",
		SepaTransactionID: transactionID,
		SepaAmount:        amount,
		SepaReasonCode:    reasonCode,
		SepaReasonText:    reasonText,
		SepaCreditorName:  creditorName,
		CreatedAt:         now,
		IsRead:            false,
	}

	// Store in database
	if _, err := notificationsColl.Insert(notification); err != nil {
		L.Error("Error while inserting notification into database", zap.Error(err))
		return
	}
	L.Info("Notification inserted into database", zap.Any("notification", notification))

	// Send Firebase push notification
	data := map[string]string{
		"notificationType": "sddr_return",
		"transactionID":    transactionID,
		"amount":           amount,
		"reasonCode":       reasonCode,
		"reasonText":       reasonText,
		"creditorName":     creditorName,
	}
	if err := sendFirebaseNotification(account.PersonalTopic, notification.Title, notification.Message, data); err != nil {
		L.Error("Error while sending Firebase notification", zap.Error(err), zap.Any("data", data))
		return
	}
	L.Info("Firebase notification sent", zap.Any("data", data))
}

// Helper function to get account by wallet ID
func getAccountByWalletID(busrpc bus.RpcClient, walletID int64) (*accounts.Account, error) {
	// Step 1: Get the wallet details from Treezor service
	wallet, err := treezor.GetWalletByTreezorId(busrpc, walletID)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet details: %w", err)
	}

	// Step 2: Get the account using the treezorUserId from the wallet
	account, err := accounts.GetByTreezorUser(busrpc, wallet.TreezorUserId)
	if err != nil {
		return nil, fmt.Errorf("failed to get account for treezorUserId %d: %w", wallet.TreezorUserId, err)
	}
	if account == nil {
		return nil, fmt.Errorf("account not found for treezorUserId %d", wallet.TreezorUserId)
	}

	return account, nil
}

// Helper function to format collection date for display
func formatCollectionDate(dateStr string) string {
	// Parse the date string (format: "2025-07-22 00:00:00")
	t, err := time.Parse("2006-01-02 15:04:05", dateStr)
	if err != nil {
		// If parsing fails, return the original string
		return dateStr
	}

	// Format to French date format (22/07/2025)
	return t.Format("02/01/2006")
}
