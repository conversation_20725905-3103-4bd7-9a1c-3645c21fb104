package main

import (
	"yochbee/_base"
	"yochbee/_base/config"
	"yochbee/_base/web"
)

func main() {
	config.Microservice = "notifications"
	web.ServerEndpoint = ":8090"

	app := _base.SetupAppWithCustomProvidersAndInvokes("Notifications",
		[]interface{}{
			setupNotificationsCollection,
		},
		[]interface{}{
			setupTwilio,
			setupMailerNoReply,
			setupFirebaseApp,
			subscribeToCardTxAuthEvents,
			subscribeToBeneficiaryEvents,
			subscribeToSDDREvents,
			registerBusMethods,
			registerNotificationsRESTAPIs,
		},
	)

	app.Run()
}
