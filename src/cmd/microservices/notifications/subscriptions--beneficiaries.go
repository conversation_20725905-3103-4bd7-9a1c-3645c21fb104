package main

import (
	"fmt"
	"time"

	"yochbee/_base/bus"
	"yochbee/common/accounts"
	"yochbee/common/notifications"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToBeneficiaryEvents(busrpc bus.RpcClient, signaling bus.Client, notificationsColl *NotificationsCollection) error {
	notificationsQueue, err := signaling.DeclareSubscriptionQueue("Notifications.Subscriptions-beneficiary", true, false,
		[]string{
			"banking.beneficiary.create-from-treezor", // from Banking
		},
	)
	if err != nil {
		return err
	}

	return signaling.SubscribeToEventForever(notificationsQueue, "notifications/subscriptions--beneficiaries.go", false, func(event *bus.Event, ack func() error) {
		L := logging.L.Named("Consumer(beneficiary)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		// Unmarshal the data as Beneficiary object
		beneficiaries, err := treezor.UnwrapBeneficiariesFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Beneficiary from Treezor webhook", zap.Error(err))
			return
		}

		// Get the first Beneficiary object
		beneficiary := beneficiaries[0]

		// Get the account via the bus, from the `accounts` microservice
		// - We need this to get the `personalTopic` of the user, for the Firebase notification
		account, err := accounts.GetByTreezorUser(busrpc, beneficiary.TreezorUserId)
		if err != nil {
			L.Error("Error while getting account from accounts microservice", zap.Error(err))
			return
		}
		if account == nil {
			L.Error("Account not found", zap.Int64("treezorUserID", beneficiary.TreezorUserId))
			return
		}
		if account.PersonalTopic == "" {
			L.Error("User's personalTopic not set", zap.Int64("treezorUserID", beneficiary.TreezorUserId))
			if err := ack(); err != nil {
				L.Error("Error while acknowledging message", zap.Error(err))
			}
			return
		}
		L.Info("Account found", zap.Any("account", account), zap.String("personalTopic", account.PersonalTopic))

		now := time.Now()
		notification := &notifications.Notification{
			ID:            notificationsColl.GenerateUniqueId(),
			UserID:        fmt.Sprintf("%d", beneficiary.TreezorUserId),
			BeneficiaryID: fmt.Sprintf("%d", beneficiary.ID),
			Title:         "Nouveau bénéficiaire ajouté",
			Message:       "Un nouveau bénéficiaire a été ajouté à votre compte : " + beneficiary.Name,
			CreatedAt:     now,
			IsRead:        false,
		}

		if _, err := notificationsColl.Insert(notification); err != nil {
			L.Error("Error while inserting notification into database", zap.Error(err))
			return
		}
		L.Info("Notification inserted into database", zap.Any("notification", notification))

		// Send the Firebase notification
		data := map[string]string{
			"beneficiaryID": fmt.Sprintf("%d", beneficiary.ID),
		}
		if err := sendFirebaseNotification(account.PersonalTopic, notification.Title, notification.Message, data); err != nil {
			L.Error("Error while sending Firebase notification", zap.Error(err), zap.Any("data", data))
			return
		}
		L.Info("Firebase notification sent", zap.Any("data", data))

		if err := ack(); err != nil {
			L.Error("Error while acknowledging message", zap.Error(err))
		}
	})
}
