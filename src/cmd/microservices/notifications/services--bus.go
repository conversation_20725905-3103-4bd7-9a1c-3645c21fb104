package main

import (
	"encoding/json"
	"os"
	"strings"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/common/notifications"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func registerBusMethods(rpc bus2.RpcClient) error {
	fakeSMS := strings.ToLower(strings.TrimSpace(os.Getenv("FAKE_SEND_SMS"))) == "true"
	if fakeSMS {
		logging.L.Warn("FAKE_SEND_SMS has been set")
	}

	// ---------------
	// ╔═╗┌─┐┌┐┌┌┬┐  ╔═╗╔╦╗╔═╗
	// ╚═╗├┤ │││ ││  ╚═╗║║║╚═╗
	// ╚═╝└─┘┘└┘─┴┘  ╚═╝╩ ╩╚═╝
	if err := rpc.Register(notifications.Method_SendSMS, func(payload []byte) ([]byte, error) {
		request := notifications.SendSMSRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		if config.IsDebugMode() || fakeSMS {
			logging.L.Info("SMS body", zap.String("body", request.Body), zap.String("to", request.ToPhoneNumber))
			if fakeSMS {
				return bus2.OK()
			}
		}

		// Send the actual SMS
		if _, err := sendSMS(request.ToPhoneNumber, request.Body); err != nil {
			return nil, err
		}

		return bus2.OK()
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╦  ╦┌─┐┬  ┬┌┬┐┌─┐┌┬┐┌─┐  ╔═╗╔╦╗╔═╗  ┌┐┌┬ ┬┌┬┐┌┐ ┌─┐┬─┐
	// ╚╗╔╝├─┤│  │ ││├─┤ │ ├┤   ╚═╗║║║╚═╗  ││││ ││││├┴┐├┤ ├┬┘
	//  ╚╝ ┴ ┴┴─┘┴─┴┘┴ ┴ ┴ └─┘  ╚═╝╩ ╩╚═╝  ┘└┘└─┘┴ ┴└─┘└─┘┴└─
	if err := rpc.Register(notifications.Method_ValidateSMSNumber, func(payload []byte) ([]byte, error) {
		request := notifications.ValidateSMSNumberRequest{}
		if err := json.Unmarshal(payload, &request); err != nil {
			return nil, err
		}

		// Validate
		validated, countrycode, err := validatePhoneNumber(request.LocalOrInternationalPhoneNumber, request.Iso3166CountryCode)
		if err != nil {
			return nil, err
		}

		return json.Marshal(notifications.ValidateSMSNumberResponse{
			ValidatedStandardPhoneNumber: validated,
			CountryIso3166:               countrycode,
		})
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┐┌┌┬┐  ╔╗╔╔═╗╦═╗╔═╗╔═╗╦ ╦ ╦  ┌─┐┌┬┐┌─┐┬┬
	// ╚═╗├┤ │││ ││  ║║║║ ║╠╦╝║╣ ╠═╝║ ╚╦╝  ├┤ │││├─┤││
	// ╚═╝└─┘┘└┘─┴┘  ╝╚╝╚═╝╩╚═╚═╝╩  ╩═╝╩   └─┘┴ ┴┴ ┴┴┴─┘
	fake := strings.ToLower(strings.TrimSpace(os.Getenv("FAKE_SEND_EMAIL"))) == "true"
	if fake {
		logging.L.Warn("FAKE_SEND_EMAIL has been set")
	}
	if err := bus2.RegisterUM(notifications.Method_SendEmailNoReply, rpc, func(request *notifications.SendEmailNoReplyRequest) (*notifications.SendEmailNoReplyResponse, error) {
		if config.IsDebugMode() || fake {
			logging.L.Warn("NOREPLY email", zap.String("body", request.Body), zap.Strings("to", request.ToEmailAddresses))
			if fake {
				return &notifications.SendEmailNoReplyResponse{Ok: true}, nil
			}
		}

		// Send a real email
		if err := sendEmailNoReply(request.ToEmailAddresses, request.Subject, request.Body, request.ContentType); err != nil {
			return nil, err
		}
		logging.L.Info("Sent email", zap.Strings("to", request.ToEmailAddresses), zap.String("subject", request.Subject))
		return &notifications.SendEmailNoReplyResponse{Ok: true}, nil
	}, true); err != nil {
		return err
	}

	// ---------------
	// ╔═╗┌─┐┌┐┌┌┬┐  ┌─┐┌┬┐┌─┐┬┬    ╔═╗╦═╗╔═╗╔╦╗
	// ╚═╗├┤ │││ ││  ├┤ │││├─┤││    ╠╣ ╠╦╝║ ║║║║
	// ╚═╝└─┘┘└┘─┴┘  └─┘┴ ┴┴ ┴┴┴─┘  ╚  ╩╚═╚═╝╩ ╩  ooo
	//
	fakeFrom := strings.ToLower(strings.TrimSpace(os.Getenv("FAKE_SEND_EMAIL_FROM"))) == "true"
	if fakeFrom {
		logging.L.Warn("FAKE_SEND_EMAIL_FROM has been set")
	}
	if err := bus2.RegisterUM(notifications.Method_SendEmailFrom, rpc, func(request *notifications.SendEmailFromRequest) (*notifications.SendEmailFromResponse, error) {
		if config.IsDebugMode() || fake {
			logging.L.Warn("FROM email", zap.String("from", request.FromEmailAddress), zap.String("body", request.Body), zap.Strings("to", request.ToEmailAddresses))
			if fakeFrom {
				return &notifications.SendEmailFromResponse{Ok: true}, nil
			}
		}

		// Send a real email
		if err := sendEmailFrom(request.ToEmailAddresses, request.Subject, request.Body, request.ContentType, request.FromEmailAddress); err != nil {
			return nil, err
		}
		return &notifications.SendEmailFromResponse{Ok: true}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
