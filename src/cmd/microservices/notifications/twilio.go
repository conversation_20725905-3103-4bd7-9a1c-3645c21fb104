package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"yochbee/_base/config"
	"yochbee/_base/utils"

	"github.com/twilio/twilio-go"
	openapi "github.com/twilio/twilio-go/rest/api/v2010"
	openapi2 "github.com/twilio/twilio-go/rest/lookups/v1"
)

type twilioConfiguration struct {
	AccountSid  string `json:"accountSid"`
	AuthToken   string `json:"authToken"`
	PhoneNumber string `json:"phoneNumber"`
}

var (
	twilioConfig twilioConfiguration
	twilioClient *twilio.RestClient
)

func setupTwilio(config config.Provider) error {
	return config.GetLive(context.Background(), "notifications.sms.twilio", func(bytestr []byte) {
		if len(bytestr) == 0 {
			panic("please configure `notifications.sms.twilio` with a JSON like {\"accountSid\": ..., \"authToken\": ..., \"phoneNumber\": ...}")
		}
		if err := json.Unmarshal(bytestr, &twilioConfig); err != nil {
			panic("invalid JSON in configuration `notifications.sms.twilio`")
		}

		// Create a new client
		twilioClient = twilio.NewRestClientWithParams(twilio.ClientParams{
			Username: twilioConfig.AccountSid,
			Password: twilioConfig.AuthToken,
		})
	})
}

func sendSMS(toPhoneNumber, body string) (string, error) {
	params := &openapi.CreateMessageParams{}
	params.SetTo(toPhoneNumber)
	params.SetFrom(twilioConfig.PhoneNumber)
	params.SetBody(body)

	resp, err := twilioClient.ApiV2010.CreateMessage(params)
	if err != nil {
		return "", err
	}
	return *resp.Sid, nil
}

func validatePhoneNumber(raw string, countryCode string) (standardized, countrycode string, err error) {
	countryCode = strings.TrimSpace(countryCode)
	raw = utils.RemoveSpaces(raw)
	if len(raw) < 2 {
		return "", "", errors.New("invalid phone number")
	}
	international := strings.HasPrefix(raw, "+") || strings.HasPrefix(raw, "00")
	if !international && countryCode == "" {
		return "", "", errors.New("local number requires `countryCode` to be set")
	}

	result, err := twilioClient.LookupsV1.FetchPhoneNumber(raw, &openapi2.FetchPhoneNumberParams{CountryCode: &countryCode})
	if err != nil {
		return "", "", fmt.Errorf("error calling Twilio API: %w", err)
	}

	return *result.PhoneNumber, *result.CountryCode, nil
}
