package main

import (
	"context"
	"errors"
	"sync"
	"yochbee/_base/config"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

var (
	clientMutex        sync.Mutex
	client             *messaging.Client
	firebaseConfigLock sync.Mutex
)

// setupFirebaseApp initializes and updates Firebase app configuration dynamically.
func setupFirebaseApp(config config.Provider) error {
	err := config.GetLive(context.Background(), "config.firebase", func(data []byte) {
		if len(data) == 0 {
			logging.L.Error("Empty Firebase configuration")
			return
		}

		firebaseConfigLock.Lock()
		defer firebaseConfigLock.Unlock()

		opt := option.WithCredentialsJSON(data)
		app, err := firebase.NewApp(context.Background(), nil, opt)
		if err != nil {
			logging.L.Error("Error initializing Firebase app", zap.Error(err))
			return
		}

		// Create a messaging Client
		client, err = app.Messaging(context.Background())
		if err != nil {
			logging.L.Error("Error initializing Firebase messaging client", zap.Error(err))
			return
		}

		logging.L.Info("Firebase configuration updated")
	})

	if err != nil {
		return err
	}

	return nil
}

func sendFirebaseNotification(topic, title, message string, data map[string]string) error {
	// Acquire a messaging client
	clientMutex.Lock()
	defer clientMutex.Unlock()
	if client == nil {
		logging.L.Error("Firebase messaging client not initialized, configuration may be missing")
		return errors.New("the Firebase messaging client is not initialized")
	}

	m := &messaging.Message{
		Android: &messaging.AndroidConfig{
			Priority: "high",
		},
		APNS: &messaging.APNSConfig{
			Headers: map[string]string{
				"apns-priority": "10", // High priority
			},
			Payload: &messaging.APNSPayload{
				Aps: &messaging.Aps{
					Alert: &messaging.ApsAlert{
						Title: title,
						Body:  message,
					},
					Sound: "default",
				},
			},
		},
		Notification: &messaging.Notification{
			Title: title,
			Body:  message,
		},
		Data:  data,
		Topic: topic,
	}

	_, err := client.Send(context.Background(), m)
	return err
}
