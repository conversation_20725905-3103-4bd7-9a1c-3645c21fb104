package main

import (
	"encoding/base64"
	"encoding/json"
	"time"

	"yochbee/_base/bus"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/notifications"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToCardTxAuthEvents(busrpc bus.RpcClient, signaling bus.Client, notificationsColl *NotificationsCollection) error {
	notificationsQueue, err := signaling.DeclareSubscriptionQueue("Notifications.Subscriptions-cardTxAuth", true, false,
		[]string{
			"treezor.custom.cardTxAuth.create",
		},
	)
	if err != nil {
		return err
	}

	return signaling.SubscribeToEventForever(notificationsQueue, "notifications/subscriptions--cardTxAuths.go", false, func(event *bus.Event, ack func() error) {
		L := logging.L.Named("Consumer(cardTxAuth)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		// Unmarshal the data as CardTxAuth object
		payload := event.Data.(string)
		cardTxAuth := &treezor.CardTxAuth{}
		if err := json.Unmarshal([]byte(payload), cardTxAuth); err != nil {
			L.Error("Error while unmarshalling CardTxAuth object", zap.Error(err))
			return
		}

		// Get the account via the bus, from the `accounts` microservice
		// - We need this to get the `personalTopic` of the user, for the Firebase notification
		account, err := accounts.GetByTreezorUser(busrpc, cardTxAuth.UserID)
		if err != nil {
			L.Error("Error while getting account from accounts microservice", zap.Error(err))
			return
		}
		if account == nil {
			L.Error("Account not found", zap.Int64("treezorUserID", cardTxAuth.UserID))
			return
		}
		if account.PersonalTopic == "" {
			L.Error("User's personalTopic not set", zap.Int64("treezorUserID", cardTxAuth.UserID))
			if err := ack(); err != nil {
				L.Error("Error while acknowledging message", zap.Error(err))
			}
			return
		}
		L.Info("Account found", zap.Any("account", account), zap.String("personalTopic", account.PersonalTopic))

		// Get the card via the bus, from the `banking` microservice
		// - We need this to assign the correct `cardId` for the Notification object
		card, err := banking.GetCardByTreezorID(busrpc, cardTxAuth.CardID)
		if err != nil {
			L.Error("Error while getting card from banking microservice", zap.Error(err))
			return
		}
		if card == nil {
			L.Error("Card not found", zap.Int64("treezorCardID", cardTxAuth.CardID))
			if err := ack(); err != nil {
				L.Error("Error while acknowledging message", zap.Error(err))
			}
			return
		}
		L.Info("Card found", zap.Any("card", card))

		now := time.Now()
		notification := &notifications.Notification{
			ID:           notificationsColl.GenerateUniqueId(),
			UserID:       account.ID,
			CardID:       card.ID,
			CardTxAuthID: cardTxAuth.ID, // we'll ask `treezor` microservice for this object when the mobile app asks for it
			Title:        "Demande d'autorisation de transaction",
			Message:      "Autoriser la transaction ?",
			CreatedAt:    now,
			IsRead:       false,
		}

		if _, err := notificationsColl.Insert(notification); err != nil {
			L.Error("Error while inserting notification into database", zap.Error(err))
			return
		}
		L.Info("Notification inserted into database", zap.Any("notification", notification))

		// Convert the cardTxAuth object to JSON and base64 encode it
		cardTxAuthJSON, err := json.Marshal(cardTxAuth)
		if err != nil {
			L.Error("Error while marshalling cardTxAuth object to JSON", zap.Error(err))
			return
		}
		cardTxAuthBase64 := base64.StdEncoding.EncodeToString(cardTxAuthJSON)

		// Send the Firebase notification
		data := map[string]string{
			"cardTxAuthB64": cardTxAuthBase64,
		}
		if err := sendFirebaseNotification(account.PersonalTopic, notification.Title, notification.Message, data); err != nil {
			L.Error("Error while sending Firebase notification", zap.Error(err), zap.Any("data", data))
			return
		}
		L.Info("Firebase notification sent", zap.Any("data", data))

		if err := ack(); err != nil {
			L.Error("Error while acknowledging message", zap.Error(err))
		}
	})
}
