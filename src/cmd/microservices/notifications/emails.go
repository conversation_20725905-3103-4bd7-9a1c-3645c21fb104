package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"yochbee/_base/config"

	"gopkg.in/gomail.v2"
)

type mailerConfiguration struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	From     string `json:"from"`
	Username string `json:"username"`
	Password string `json:"password"`
	Insecure bool   `json:"insecure"`
}

var (
	mailerConfigNoReply mailerConfiguration
)

func setupMailerNoReply(config config.Provider) error {
	return config.GetLive(context.Background(), "notifications.emails.noreply", func(bytestr []byte) {
		if len(bytestr) == 0 {
			panic("please configure `notifications.emails.noreply` with a JSON like {\"host\": ..., \"port\": ..., \"from\": ...,\"username\": ..., \"password\": ..., \"insecure\": ...}")
		}
		if err := json.Unmarshal(bytestr, &mailerConfigNoReply); err != nil {
			panic("invalid JSON in configuration `notifications.emails.noreply`")
		}
	})
}

func sendGomailMessage(m *gomail.Message) error {
	d := gomail.NewDialer(mailerConfigNoReply.Host, mailerConfigNoReply.Port, mailerConfigNoReply.Username, mailerConfigNoReply.Password)
	d.TLSConfig = &tls.Config{
		InsecureSkipVerify: mailerConfigNoReply.Insecure,
		ServerName:         mailerConfigNoReply.Host,
	}
	return d.DialAndSend(m)
}

func sendEmailNoReply(recipients []string, subject, body, bodyContentType string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", mailerConfigNoReply.From)
	m.SetHeader("To", recipients...)
	m.SetHeader("Subject", subject)
	m.SetBody(bodyContentType, body)

	return sendGomailMessage(m)
}

func sendEmailFrom(recipients []string, subject, body, bodyContentType, from string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", mailerConfigNoReply.From)
	m.SetHeader("Reply-To", from) // because we can't send from a different address
	m.SetHeader("To", recipients...)
	m.SetHeader("Subject", subject)
	m.SetBody(bodyContentType, body)

	return sendGomailMessage(m)
}
