package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/notifications"
)

type NotificationsCollection struct {
	dbnosql.DataCollection
}

func setupNotificationsCollection(db dbnosql.Database) (*NotificationsCollection, error) {
	coll := db.DeclareCollection("notifications", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &notifications.Notification{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "isRead", Order: dbnosql.OrderASC},
		},
		Name: "isRead",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
		},
		Name: "createdAt",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "userId", Order: dbnosql.OrderASC},
		},
		Name: "userId",
	}); err != nil {
		return nil, err
	}

	return &NotificationsCollection{coll}, nil
}
