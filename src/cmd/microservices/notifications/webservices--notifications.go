package main

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/notifications"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func registerNotificationsRESTAPIs(
	busrpc bus2.RpcClient,
	e *echo.Echo,
	notificationsColl *NotificationsCollection,
) {
	// Add common dependencies into the context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("busrpc", busrpc)
			return next(c)
		}
	})

	e.GET("/notifications/:accountId", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)
		L := logging.L.Named("get-notifications").With(zap.String("accountId", account.ID))
		L.Info("Starting ...")
		defer L.Info("Done")

		entries, totalCount, remainingCount, err := web.FetchPaginatedEntitiesBasedOnContext(c, notificationsColl, func(builder dbnosql.QueryBuilder) {
			builder.Set(&dbnosql.Condition{"userId", "==", account.ID})

			// Latest notifications are first
			builder.SetOrderByField("createdAt", dbnosql.OrderDESC)
		}, nil, "notifications")
		if err != nil {
			return err
		}

		// Now, we need to add the actual `CardTxAuth` objects within the notifications
		//

		cardTxAuthIds := make([]string, 0, len(entries))
		for i := range entries {
			if cardTxAuth, ok := entries[i].(*notifications.Notification); ok {
				cardTxAuthIds = append(cardTxAuthIds, cardTxAuth.CardTxAuthID)
			}
		}
		// Ask `treezor` microservice to get the `CardTxAuth` objects
		cardTxAuths, err := treezor.GetCardTxAuthsByIDs(busrpc, cardTxAuthIds)
		if err != nil {
			L.Error("Failed to get the `CardTxAuth` objects", zap.Error(err))
			return err // HTTP 500
		}
		cardTxAuthsMap := make(map[string]*treezor.CardTxAuth, len(cardTxAuths))
		for i := range cardTxAuths {
			cardTxAuthsMap[cardTxAuths[i].ID] = cardTxAuths[i]
		}

		// Convert to JSON, then inject the `CardTxAuth` objects in each one, using sjson
		rawJsonList, err := json.Marshal(entries)
		if err != nil {
			L.Error("Failed to marshal the notifications", zap.Error(err))
			return err // HTTP 500
		}
		for i := range entries {
			notification := entries[i].(*notifications.Notification)
			cardTxAuth, ok := cardTxAuthsMap[notification.CardTxAuthID]
			if ok {
				rawJsonList, err = sjson.SetBytes(rawJsonList, strconv.Itoa(i)+".cardTxAuth", cardTxAuth)
				if err != nil {
					L.Error("Failed to set the `CardTxAuth` object", zap.Error(err))
					return err // HTTP 500
				}
			}
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"notifications":  json.RawMessage(rawJsonList),
			"totalCount":     totalCount,
			"remainingCount": remainingCount,
		})
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.PUT("/notifications/:accountId/mark-as-read", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)
		L := logging.L.Named("mark-as-read").With(zap.String("accountId", account.ID))
		L.Info("Starting ...")
		defer L.Info("Done")

		req := struct {
			NotificationId string `json:"notificationId" validate:"required,min=16"`
			IsRead         bool   `json:"isRead"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return err // HTTP 500
		}

		// Verify that the notification exists and it belongs to the user
		notification, err := dbnosql.GetOneByID[*notifications.Notification](notificationsColl, req.NotificationId)
		if err != nil {
			L.Error("Failed to get the notification", zap.String("notificationId", req.NotificationId), zap.Error(err))
			return err // HTTP 500
		}
		if notification == nil || notification.UserID != account.ID {
			return web.Error(c, common.ErrorCodeNotFound, "Notification not found")
		}

		// Update
		rawColl := notificationsColl.GetRawCollection().(*mongo.Collection)
		if _, err := rawColl.UpdateOne(c.Request().Context(), bson.M{
			"_id": req.NotificationId,
		}, bson.M{"$set": bson.M{"isRead": req.IsRead}}); err != nil {
			L.Error("Failed to update the notification", zap.String("notificationId", req.NotificationId), zap.Error(err))
			return err // HTTP 500
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.PUT("/notifications/:accountId/mark-all-as-read", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)
		L := logging.L.Named("mark-all-as-read").With(zap.String("accountId", account.ID))
		L.Info("Starting ...")
		defer L.Info("Done")

		req := struct {
			IsRead bool `json:"isRead"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return err // HTTP 500
		}

		// Update all the user's notifications
		rawColl := notificationsColl.GetRawCollection().(*mongo.Collection)
		if _, err := rawColl.UpdateMany(c.Request().Context(), bson.M{
			"userId": account.ID,
		}, bson.M{"$set": bson.M{"isRead": req.IsRead}}); err != nil {
			L.Error("Failed to update the notifications", zap.Error(err))
			return err // HTTP 500
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.DELETE("/notifications/:accountId/delete/:id", func(c echo.Context) error {
		id := strings.TrimSpace(c.Param("id"))
		account := c.Get("account").(*accounts.Account)
		L := logging.L.Named("delete-notification").With(zap.String("accountId", account.ID), zap.String("notificationId", id))
		L.Info("Starting ...")
		defer L.Info("Done")

		// Verify that the notification exists and it belongs to the user
		notification, err := dbnosql.GetOneByID[*notifications.Notification](notificationsColl, id)
		if err != nil {
			L.Error("Failed to get the notification", zap.String("notificationId", id), zap.Error(err))
			return err // HTTP 500
		}
		if notification == nil || notification.UserID != account.ID {
			return web.Error(c, common.ErrorCodeNotFound, "Notification not found")
		}

		// Delete
		if err = notificationsColl.DeleteById(id); err != nil {
			L.Error("Failed to delete the notification", zap.String("notificationId", id), zap.Error(err))
			return err // HTTP 500
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.POST("/notifications/:accountId/authenticate-oob-transaction", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)
		L := logging.L.Named("authenticate-oob-transaction").With(zap.String("accountId", account.ID))
		L.Info("Starting ...")
		defer L.Info("Done")

		// Parse the form
		req := struct {
			CardTxAuthId string `json:"cardTxAuthId" validate:"required"`
			Result       string `json:"authenticationResult" validate:"required"`
			Signature    string `json:"authenticationSignature"` // optional if the result is not "OK"
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &req); err != nil {
			return err // HTTP 500
		}

		// Get the CardTxAuth object from `treezor` microservice
		cardTxAuths, err := treezor.GetCardTxAuthsByIDs(busrpc, []string{req.CardTxAuthId})
		if err != nil {
			L.Error("Failed to get the CardTxAuth object", zap.String("cardTxAuthId", req.CardTxAuthId), zap.Error(err))
			return err // HTTP 500
		}
		if len(cardTxAuths) == 0 {
			L.Error("CardTxAuth not found", zap.String("cardTxAuthId", req.CardTxAuthId))
			return web.Error(c, common.ErrorCodeNotFound, "CardTxAuth not found")
		}
		cardTxAuth := cardTxAuths[0]

		// Ask Treezor to validate the transaction according to the request
		response, err := treezor.AuthenticateCardTransaction(busrpc, account.TreezorUserId, cardTxAuth.ID, req.Result, req.Signature)
		if err != nil {
			L.Error("Failed to authenticate the transaction", zap.String("cardTxAuthId", req.CardTxAuthId), zap.Error(err))
			return err // HTTP 500
		}
		if !response.Ok {
			L.Error("Failed to authenticate the transaction", zap.String("cardTxAuthId", req.CardTxAuthId), zap.Any("response", response))

			// Get the value of the `error` field from Treezor
			message := gjson.Get(response.RawResponse, "error")
			return c.JSON(http.StatusBadRequest, map[string]interface{}{
				"error":       message,
				"code":        common.ErrorCodeTemporaryServerError,
				"rawStatus":   response.StatusCode,
				"rawResponse": response.RawResponse,
			})
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareRequireAndCacheSCAProof)
}
