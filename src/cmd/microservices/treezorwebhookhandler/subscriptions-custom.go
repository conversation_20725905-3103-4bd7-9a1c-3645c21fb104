package main

import (
	"context"
	"encoding/json"
	bus2 "yochbee/_base/bus"

	"github.com/eliezedeck/gobase/logging"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

func subscribeToWebhookEvents(signaling bus2.Client, webhookEventsColl *WebhookEventsCollection) error {
	wpQueue, err := signaling.DeclareSubscriptionQueue("TreezorWebhookHandler.Subscriptions-webhooks", true, false,
		[]string{
			"webhook.processed",
		})
	if err != nil {
		return err
	}

	if err = signaling.SubscribeToEventForever(wpQueue, "treezorwebhookhandler/subscriptions--custom.go", false, func(event *bus2.Event, ack func() error) {
		if event == nil {
			logging.L.Error("Received nil event")
			return
		}
		data, isString := event.Data.(string)
		if !isString {
			// Check if the data is a byte array
			dataBytes, isBytes := event.Data.([]byte)
			if isBytes {
				data = string(dataBytes)
			} else {
				logging.L.Error("Failed to convert event data to string", zap.Any("event", event))

				// Don't want to see this message anymore
				_ = ack()
				return
			}
		}

		L := logging.L.Named("Consumer(webhook.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", data))
		defer L.Info("Finished")

		// Unwrap the `treezorWebhookID` from the payload
		var payload struct {
			TreezorWebhookID string `json:"treezorWebhookID"`
		}
		if err := json.Unmarshal([]byte(data), &payload); err != nil {
			L.Error("Failed to unmarshal event data", zap.Error(err))
			return
		}

		// - Mark the Card Transaction event as processed
		rawColl := webhookEventsColl.GetRawCollection().(*mongo.Collection)
		if _, err := rawColl.UpdateOne(context.Background(), bson.M{"treezorId": payload.TreezorWebhookID}, bson.M{"$set": bson.M{"processed": true}}); err != nil {
			L.Error("Failed to mark the Card Transaction event as processed (db ☢️)", zap.Error(err), zap.String("treezorWebhookID", payload.TreezorWebhookID))
			return
		}

		// Don't forget to acknowledge the message
		_ = ack()
	}); err != nil {
		return err
	}

	return nil
}
