package main

import (
	"fmt"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func processOldCardTransactions(busrpc bus2.RpcClient, signaling bus2.Client, webhookEventsCollection *WebhookEventsCollection) {
	L := logging.L.Named("processOldCardTransactions")

	// Get the list of all unprocessed card transactions events
	q := webhookEventsCollection.GetQueryBuilder()
	err := q.SetAnd(
		&dbnosql.Condition{"eventName", "==", "cardtransaction.create"},
		&dbnosql.Condition{"processed", "==", false},
	)
	if err != nil {
		L.Error("Failed building query", zap.Error(err))
		return
	}
	q.SetOrderByField("treezorTimestamp", dbnosql.OrderASC) // oldest first
	list, err := webhookEventsCollection.Find(q)
	if err != nil {
		L.<PERSON>rror("Failed fetching unprocessed webhook events", zap.Error(err))
		return
	}
	webhookEvents := dbnosql.ConvertDocsList[*treezor.WebhookEvent](list)

	// Process them
	for _, webhookEvent := range webhookEvents {
		L.Info("Processing old webhook event", zap.String("id", webhookEvent.ID))

		// Unwrap the CardTransaction from the Treezor response
		cardTransactions, err := treezor.UnwrapCardTransactionsFromTreezorResponse([]byte(webhookEvent.Payload.(string)))
		if err != nil {
			L.Error("Failed unwrapping CardTransactions from the old event", zap.Error(err))
			continue
		}
		if len(cardTransactions) == 0 {
			L.Error("No CardTransaction found in Treezor response")
			continue
		}

		// Process each CardTransaction
		for _, cardTransaction := range cardTransactions {
			// Find a matching Treezor Transaction event (which is converted to Banking Transaction)
			q := webhookEventsCollection.GetQueryBuilder()
			err := q.SetAnd(
				&dbnosql.Condition{"eventName", "==", "transaction.create"},
				&dbnosql.Condition{"payload", "regex", fmt.Sprintf("\"foreignId\":\"%d\"", cardTransaction.TreezorId)},
			)
			if err != nil {
				L.Error("Failed building query", zap.Error(err))
				return
			}
			list, err := webhookEventsCollection.Find(q)
			if err != nil {
				L.Error("Failed fetching unprocessed webhook events matching the Card Transaction", zap.Error(err), zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID))
				continue
			}
			// If no matching Treezor Transaction event is found, we don't process the Card Transaction
			if len(list) == 0 {
				L.Warn("No matching Treezor Transaction event found for the Card Transaction", zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID))
				continue
			}

			// We now have a matching Treezor Transaction event
			treezorTransactionEvent := list[0].(*treezor.WebhookEvent)
			L.Info("Found matching Treezor Transaction event", zap.String("treezorTransactionEventId", treezorTransactionEvent.ID))

			// - Re-trigger the processing of the Card Transaction
			entityType := determineEntityType(webhookEvent, L)
			if entityType != "" {
				_, err := sendToTreezorForIngestThenPublish(busrpc, signaling, L, webhookEvent, entityType, webhookEvent.Payload.(string))
				if err != nil {
					L.Error("Failed to re-trigger the processing of the Card Transaction", zap.Error(err), zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID))
					continue
				}
			}

			// - Re-trigger the processing of the Treezor Transaction
			entityType = determineEntityType(treezorTransactionEvent, L)
			if entityType != "" {
				_, err := sendToTreezorForIngestThenPublish(busrpc, signaling, L, treezorTransactionEvent, entityType, treezorTransactionEvent.Payload.(string))
				if err != nil {
					L.Error("Failed to re-trigger the processing of the Treezor Transaction", zap.Error(err), zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID))
					continue
				}
			}

			// - Mark the Card Transaction event as processed
			if err := webhookEventsCollection.UpdateById(webhookEvent.ID, map[string]interface{}{"processed": true}); err != nil {
				L.Error("Failed to mark the Card Transaction event as processed", zap.Error(err), zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID))
				continue
			}

			// - Mark the Treezor Transaction event as processed
			if err := webhookEventsCollection.UpdateById(treezorTransactionEvent.ID, map[string]interface{}{"processed": true}); err != nil {
				L.Error("Failed to mark the Treezor Transaction event as processed", zap.Error(err), zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID))
				continue
			}

			L.Info("Successfully processed the Card Transaction and its corresponding Transaction", zap.Int64("cardTransactionId", cardTransaction.TreezorId), zap.String("webhookEventId", webhookEvent.ID), zap.String("treezorTransactionEventId", treezorTransactionEvent.ID))
		}
	}
}
