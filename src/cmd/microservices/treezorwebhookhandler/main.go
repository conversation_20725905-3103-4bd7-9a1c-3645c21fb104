package main

import (
	"yochbee/_base"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"

	"github.com/labstack/echo/v4"
)

func registerAllServices(busrpc bus2.RpcClient, signaling bus2.Client, e *echo.Echo, db dbnosql.Database) error {
	// ---------------------------------
	webhookEventsCollection, err := setupWebhookEventsCollection(db)
	if err != nil {
		return err
	}

	// ---------------------------------
	if err = registerWebhookEndpoint(busrpc, signaling, e, webhookEventsCollection); err != nil {
		return err
	}

	// For retriggering webhook events
	if err = registerBusMethods(signaling, busrpc, webhookEventsCollection); err != nil {
		return err
	}

	e.POST("/_bus/call-method/:methodName", bus2.RegisterBusMethodCallingFromREST(busrpc))

	// ---------------------------------
	// Subscribe to webhook events
	if err = subscribeToWebhookEvents(signaling, webhookEventsCollection); err != nil {
		return err
	}

	// ---------------------------------
	// Process webhook events (post processing)
	// go func() {
	// 	// Delay the start of the post-processing by 1 minute
	// 	// This is to allow the system to start up properly
	// 	time.Sleep(1 * time.Minute)

	// 	L := logging.L.Named("WebhooksPostProcessing")
	// 	L.Info("Started old card transactions post-processing ...")
	// 	processOldCardTransactions(busrpc, signaling, webhookEventsCollection)
	// 	L.Info("Finished processing old card transactions")
	// }()

	return nil
}

func main() {
	config.Microservice = "treezor-webhook-handler"

	web.ServerEndpoint = ":8082"

	app := _base.SetupApp("TreezorWebhookHandler",
		setupWebhookVerification,
		registerAllServices)
	app.Run()
}
