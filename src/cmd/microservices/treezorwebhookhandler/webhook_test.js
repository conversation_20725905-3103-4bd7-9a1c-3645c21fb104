const crypto = require('crypto');

const encodeUTF8ToCodePoint = (s) => {
    return s.replace(
        /[^\x20-\x7F]/g,
        x => "\\u" + ("000"+x.codePointAt(0).toString(16)).slice(-4)
    )
}

const payload2 = '{"webhook":"user.kycreview","object":"user","object_id":"3953922","object_payload":{"users":[{"userId":"3953922","userTypeId":"1","userStatus":"VALIDATED","clientId":"39625","userTag":"6419634dc718c14f06aad117","parentUserId":null,"parentType":null,"title":"M","firstname":"Ocean","lastname":"Frank","middleNames":"","birthday":"1996-02-18","email":"<EMAIL>","address1":"<PERSON> Défen<PERSON>","address2":"","address3":null,"postcode":"50800","city":"Paris","state":"","country":"TN","countryName":"Tunisia","phone":"+33644621315","mobile":"+33644621315","nationality":"FR","nationalityOther":"","placeOfBirth":"Paris","birthCountry":"FR","secondaryAddress1":null,"secondaryAddress2":null,"secondaryAddress3":null,"secondaryPostcode":null,"secondaryCity":null,"secondaryState":null,"secondaryCountry":null,"occupation":"Employee/Worker","incomeRange":"24-27","legalName":"","legalNameEmbossed":"","legalRegistrationNumber":"","legalTvaNumber":"","legalRegistrationDate":"0000-00-00","legalForm":"","legalShareCapital":"0","legalSector":"","legalAnnualTurnOver":"","legalNetIncomeRange":"","legalNumberOfEmployeeRange":"","effectiveBeneficiary":"0.00","position":"","personalAssets":"129-319","taxResidence":"","taxNumber":"","kycLevel":"0","kycReview":"0","kycReviewComment":"","isFreezed":"0","language":null,"specifiedUSPerson":"0","employeeType":"0","entityType":"0","controllingPersonType":"0","activityOutsideEu":null,"economicSanctions":null,"residentCountriesSanctions":null,"involvedSanctions":null,"sanctionsQuestionnaireDate":null,"timezone":null,"occupationType":null,"isOnStockExchange":null,"createdDate":"2023-03-21 08:00:11","modifiedDate":"2023-03-21 08:01:10","codeStatus":"110009","informationStatus":"","sepaCreditorIdentifier":null,"walletCount":"1","payinCount":"0"}]},"webhook_created_at":16793856704508,"webhook_id":"27d4661e-f795-4113-9972-541b48feecd8","object_payload_signature":"uIJDObMQ+rcl8e7rVQfTwtLkQW5AjCosl+4YwA4Sl2U="}'
const body = JSON.parse(payload2);
const stringified = JSON.stringify(body.object_payload)
    .replaceAll('/', '\\/'); // this was not in the official documentation
console.log('Stringified:', stringified);
const utfed = encodeUTF8ToCodePoint(stringified);
console.log()
console.log('UTFed:', utfed);
const computedSignature = crypto.createHmac('sha256', '2e0cdfb2de84d8b25aa67173659c96558f67e1d3')
    .update(utfed)
    .digest('base64');

console.log()
if(computedSignature !== body.object_payload_signature){
    console.log("Signatures do not match");
} else {
    console.log("Signatures match");
}

console.log(`${computedSignature}  ---  ${body.object_payload_signature}`);
