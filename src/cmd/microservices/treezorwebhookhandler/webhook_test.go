package main

import "testing"

func TestWebhookSigVerificationCase1(t *testing.T) {
	webhookSecret = []byte("2e0cdfb2de84d8b25aa67173659c96558f67e1d3")

	casePayload := []byte(`{"webhook":"user.update","object":"user","object_id":"3923259","object_payload":{"users":[{"userId":"3923259","userTypeId":"1","userStatus":"VALIDATED","clientId":"39625","userTag":"63ffaad3cf2751d7df232519","parentUserId":null,"parentType":null,"title":"M","firstname":"Nasser","lastname":"BENKHEMIS.validated","middleNames":"","birthday":"1977-01-21","email":"<EMAIL>","address1":"11 BD DU GÉNÉRAL DE GAULLE","address2":"","address3":null,"postcode":"92120","city":"Montrouge","state":"","country":"AE","countryName":"United Arab Emirates","phone":"+33608498018","mobile":"+33608498018","nationality":"FR","nationalityOther":"","placeOfBirth":"Paris","birthCountry":"FR","secondaryAddress1":null,"secondaryAddress2":null,"secondaryAddress3":null,"secondaryPostcode":null,"secondaryCity":null,"secondaryState":null,"secondaryCountry":null,"occupation":"other","incomeRange":"57-*","legalName":"","legalNameEmbossed":"","legalRegistrationNumber":"","legalTvaNumber":"","legalRegistrationDate":"0000-00-00","legalForm":"","legalShareCapital":"0","legalSector":"","legalAnnualTurnOver":"","legalNetIncomeRange":"","legalNumberOfEmployeeRange":"","effectiveBeneficiary":"0.00","position":"","personalAssets":"23-128","taxResidence":"","taxNumber":"","kycLevel":"0","kycReview":"0","kycReviewComment":"","isFreezed":"0","language":null,"specifiedUSPerson":"0","employeeType":"0","entityType":"0","controllingPersonType":"0","activityOutsideEu":null,"economicSanctions":null,"residentCountriesSanctions":null,"involvedSanctions":null,"sanctionsQuestionnaireDate":null,"timezone":null,"occupationType":null,"isOnStockExchange":null,"createdDate":"2023-03-01 19:44:52","modifiedDate":"2023-03-02 06:43:33","codeStatus":"110009","informationStatus":"","sepaCreditorIdentifier":null,"walletCount":"1","payinCount":"1","totalRows":"1"}]},"webhook_created_at":16777394133081,"webhook_id":"0c40a2b3-834c-4635-8bf1-583a88ce8027","object_payload_signature":"PUM9XQB7k8dh+cM9nHjIZlX4arA05vezIELa3nMGBmA="}`)
	computedSig := ""
	treezorSig := ""
	_, _, err := verifyWebhookSignature(casePayload, nil, &treezorSig, &computedSig)
	if err != nil {
		t.Error(err)
	}
	if computedSig != treezorSig {
		t.Errorf("computedSig != treezorSig: %s != %s", computedSig, treezorSig)
	}
}

func TestWebhookSigVerificationCase2(t *testing.T) {
	webhookSecret = []byte("2e0cdfb2de84d8b25aa67173659c96558f67e1d3")

	casePayload := []byte(`{"webhook":"user.kycreview","object":"user","object_id":"3953922","object_payload":{"users":[{"userId":"3953922","userTypeId":"1","userStatus":"VALIDATED","clientId":"39625","userTag":"6419634dc718c14f06aad117","parentUserId":null,"parentType":null,"title":"M","firstname":"Ocean","lastname":"Frank","middleNames":"","birthday":"1996-02-18","email":"<EMAIL>","address1":"La Défense","address2":"","address3":null,"postcode":"50800","city":"Paris","state":"","country":"TN","countryName":"Tunisia","phone":"+33644621315","mobile":"+33644621315","nationality":"FR","nationalityOther":"","placeOfBirth":"Paris","birthCountry":"FR","secondaryAddress1":null,"secondaryAddress2":null,"secondaryAddress3":null,"secondaryPostcode":null,"secondaryCity":null,"secondaryState":null,"secondaryCountry":null,"occupation":"Employee/Worker","incomeRange":"24-27","legalName":"","legalNameEmbossed":"","legalRegistrationNumber":"","legalTvaNumber":"","legalRegistrationDate":"0000-00-00","legalForm":"","legalShareCapital":"0","legalSector":"","legalAnnualTurnOver":"","legalNetIncomeRange":"","legalNumberOfEmployeeRange":"","effectiveBeneficiary":"0.00","position":"","personalAssets":"129-319","taxResidence":"","taxNumber":"","kycLevel":"0","kycReview":"0","kycReviewComment":"","isFreezed":"0","language":null,"specifiedUSPerson":"0","employeeType":"0","entityType":"0","controllingPersonType":"0","activityOutsideEu":null,"economicSanctions":null,"residentCountriesSanctions":null,"involvedSanctions":null,"sanctionsQuestionnaireDate":null,"timezone":null,"occupationType":null,"isOnStockExchange":null,"createdDate":"2023-03-21 08:00:11","modifiedDate":"2023-03-21 08:01:10","codeStatus":"110009","informationStatus":"","sepaCreditorIdentifier":null,"walletCount":"1","payinCount":"0"}]},"webhook_created_at":16793856704508,"webhook_id":"27d4661e-f795-4113-9972-541b48feecd8","object_payload_signature":"uIJDObMQ+rcl8e7rVQfTwtLkQW5AjCosl+4YwA4Sl2U="}`)
	computedSig := ""
	treezorSig := ""
	_, _, err := verifyWebhookSignature(casePayload, nil, &treezorSig, &computedSig)
	if err != nil {
		t.Error(err)
	}
	if computedSig != treezorSig {
		t.Errorf("computedSig != treezorSig: %s != %s", computedSig, treezorSig)
	}
}
