package main

import (
	"errors"
	"io"
	"net/http"
	"time"
	"yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

func registerWebhookEndpoint(busrpc bus.RpcClient, signaling bus.Client, e *echo.Echo, webhookEventsCollection *WebhookEventsCollection) error {

	// Here is an overview of how webhook events are processed:
	// - Only valid messages that have been signed by <PERSON><PERSON> will be processed.
	// - Redis will store ONLY the very latest version of each entity, for example, the latest version of a user/wallet/etc...
	e.POST("/webhook", func(c echo.Context) error {
		L := logging.L.Named("Live")
		messId := c.Request().Header.Get("X-Amz-Sns-Message-Id")
		if messId != "" {
			L = L.With(zap.String("Sns-Message-Id", messId))
		}
		now := time.Now()
		L.Info("Started", zap.Time("now", now))
		defer func() {
			nowDone := time.Now()
			L.Info("Finished", zap.Time("now", nowDone), zap.Duration("duration", nowDone.Sub(now)))
		}()

		// Get the rawPayload as bytes
		reader := io.LimitReader(c.Request().Body, 256*1024) // at most, the rawPayload should not exceed 256KB
		rawPayload, err := io.ReadAll(reader)
		if err != nil {
			L.Error("Payload read error", zap.Error(err))
			return err // HTTP 500 ... called again in 1 minutes (max 30 times)
		}
		if config.IsDebugMode() {
			L.Info("Raw Payload", zap.ByteString("payload", rawPayload))
		}

		// Check if it's a Confirmation request message
		crm := gjson.GetBytes(rawPayload, "SubscribeURL")
		if crm.Exists() {
			// Log the URL for manual verification and confirmation
			L.Warn("Webhook confirmation required", zap.String("url", crm.String()))
			return c.String(http.StatusOK, "ok")
			// ------------------------------------
		}

		event, payloadStr, err := verifyWebhookSignature(rawPayload, L, nil, nil)
		if err != nil {
			if errors.Is(err, ErrInvalidSignature) {
				return c.String(http.StatusInternalServerError, "invalid signature") // HTTP 500 ... called again in 1 minute (max 30 times)
			}
			return err
		}
		// ----------------------------------------

		// Check if we've already received this exact event
		qb := webhookEventsCollection.GetQueryBuilder()
		_ = qb.SetAnd(&dbnosql.Condition{Field: "treezorId", Comparator: "==", Value: event.TreezorId})
		_ = qb.SetAnd(&dbnosql.Condition{Field: "treezorTimestamp", Comparator: "==", Value: event.TreezorTimestamp})
		existing, err := webhookEventsCollection.Find(qb)
		if err != nil {
			L.Error("Existing event check error", zap.Error(err))
			return err // HTTP 500 ... called again in 1 minute (max 30 times)
		}
		if len(existing) > 0 {
			// Respond with a success
			L.Info("Successfully handled (already seen before)")
			return c.String(http.StatusOK, "ok")
		}

		// Store the payload, it will be processed shortly after. Regardless of whether it was new or not, we always store
		// each message for the records.
		event.ID = webhookEventsCollection.GenerateUniqueId()
		event.Payload = payloadStr // store the string version of the payload, and not the probable struct
		event.CreatedAt = time.Now()
		if _, err = webhookEventsCollection.Insert(event); err != nil {
			L.Error("Recording error", zap.Error(err))
			return err // HTTP 500, will cause a retry every second until success, up to 30 times
		}
		L = L.With(zap.String("event", event.EventName), zap.String("payload", payloadStr))
		L.Info("🔷 Verified event {event} / {treezorTimestamp}",
			zap.String("id", event.ID),
			zap.String("treezorId", event.TreezorId),
			zap.Int64("treezorTimestamp", event.TreezorTimestamp))

		payload := event.Payload.(string)
		entityType := determineEntityType(event, L)
		if entityType != "" {
			go func() {
				L.Info("Ingesting the event in the background", zap.String("entityType", entityType))
				_, err := sendToTreezorForIngestThenPublish(busrpc, signaling, L, event, entityType, payload)
				if err != nil {
					L.Error("Error ingesting the event in the background", zap.Error(err))
				}
			}()

			L.Info("Successfully handled")
		} else {
			L.Warn("No entityType matched", zap.Any("event", event))
		}

		// Respond with a success
		return c.String(http.StatusOK, "ok")
	})

	return nil
}
