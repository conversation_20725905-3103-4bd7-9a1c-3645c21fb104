package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"
)

type WebhookEventsCollection struct {
	dbnosql.DataCollection
}

func setupWebhookEventsCollection(db dbnosql.Database) (*WebhookEventsCollection, error) {
	webhookEventsColl := db.DeclareCollection("treezor-webhook-events", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &treezor.WebhookEvent{}
	})

	if err := webhookEventsColl.SetMultipleIndexesOn("treezorId", "treezorTimestamp", "eventName", "processed"); err != nil {
		return nil, err
	}

	return &WebhookEventsCollection{webhookEventsColl}, nil
}
