package main

import (
	"errors"
	"fmt"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func registerBusMethods(signaling bus2.Client, busrpc bus2.RpcClient, webhookEventsColl dbnosql.DataCollection) error {
	// ---------------
	if err := bus2.RegisterUM(treezor.Method_RetriggerWebhookEvent, busrpc, func(request *treezor.RetriggerWebhookEventRequest) (*treezor.RetriggerWebhookEventResponse, error) {
		L := logging.L.Named("RetriggerWebhookEvent").With(zap.String("webhookEventId", request.WebhookEventID))
		L.Info("Started")
		defer L.Info("Finished")

		event, err := dbnosql.GetOneByID[*treezor.WebhookEvent](webhookEventsColl, request.WebhookEventID)
		if err != nil {
			<PERSON><PERSON>r("Failed to get WebhookEvent", zap.Error(err))
			return nil, err
		}
		if event == nil {
			L.Error("WebhookEvent not found")
			return nil, errors.New("webhook event not found")
		}
		L = L.With(zap.String("event", event.EventName))

		payload := event.Payload.(string)
		entityType := determineEntityType(event, L)
		if entityType != "" {
			response, err := sendToTreezorForIngestThenPublish(busrpc, signaling, L, event, entityType, payload)
			if err != nil {
				return nil, err
			}
			return response, nil
		}

		return &treezor.RetriggerWebhookEventResponse{}, nil
	}, false); err != nil {
		return err
	}
	return nil
}

func sendToTreezorForIngestThenPublish(busrpc bus2.RpcClient, signaling bus2.Client, L *zap.Logger, event *treezor.WebhookEvent, entityType string, payload string) (*treezor.RetriggerWebhookEventResponse, error) {
	// Treezor ingestion happens first
	if _, err := bus2.CallMethodMU[treezor.IngestFreshEntityResponse](busrpc, treezor.Method_IngestFreshEntity, &treezor.IngestFreshEntityRequest{
		Event:      event,
		EntityType: entityType,
	}, 15*time.Minute); err != nil {
		L.Error("Failed to call method treezor.Method_IngestFreshEntity", zap.Error(err), zap.Any("eventObj", event), zap.String("entityType", entityType))
		return nil, err
	}

	// Then, we generate an event, just like the original behavior; any interested services can subscribe
	// to this event and it will have the same name as the original event, for example "treezor.beneficiary.create"
	tevent := fmt.Sprintf("treezor.%s", event.EventName)
	if err := signaling.PublishEvent(&bus2.Event{
		Topic:            tevent,
		Data:             payload, // already string
		Retrigger:        true,
		Time:             event.CreatedAt,
		WebhookTimestamp: event.TreezorTimestamp,
	}); err != nil {
		L.Error("Failed to re-publish event", zap.Error(err))
		return nil, err
	}
	L.Info("Published on the Bus: {tevent}", zap.String("tevent", tevent), zap.String("data", payload))
	return &treezor.RetriggerWebhookEventResponse{}, nil
}

func determineEntityType(event *treezor.WebhookEvent, L *zap.Logger) string {
	entityType := ""

	if strings.HasPrefix(event.EventName, "card.") {
		entityType = "card"
	} else {
		switch event.EventName {
		case "user.create", "user.update", "user.cancel", "user.kycrequest", "user.kycreview":
			entityType = "user"
		case "document.create", "document.update", "document.cancel":
			entityType = "document"
		case "wallet.create", "wallet.update", "wallet.cancel":
			entityType = "wallet"
		case "kycliveness.create", "kycliveness.update":
			entityType = "kycliveness"
		case "payout.create", "payout.update", "payout.cancel":
			entityType = "payout"
		case "payoutrefund.create", "payoutrefund.update", "payoutrefund.cancel":
			entityType = "payoutrefund"
		case "payin.create", "payin.update", "payin.cancel":
			entityType = "payin"
		case "transaction.create":
			entityType = "transaction"
		case "balance.update":
			entityType = "balance"
		case "beneficiary.create", "beneficiary.update", "beneficiary.cancel":
			entityType = "beneficiary"
		case "sepaSctrInst.reject_sctr_inst":
			entityType = "sepaSctrInst"
		case "sepa_sddr.reception", "sepa.return_sddr":
			entityType = "sepa_sddr"
		case "cardtransaction.create", "cardtransaction.update":
			entityType = "cardtransaction"
		case "sca.wallet.create":
			entityType = "sca.wallet"
		case "card3DSv2Authentication.create", "card3DSv2Authentication.update":
			entityType = "card3DSv2Authentication"
		default:
			L.Warn("Unknown event, thus: unknown entity type", zap.String("event", event.EventName))
		}
	}

	return entityType
}
