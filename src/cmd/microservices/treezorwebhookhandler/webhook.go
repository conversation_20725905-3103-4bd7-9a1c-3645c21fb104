package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"sync"
	"yochbee/_base/config"
	"yochbee/common/treezor"

	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

var (
	configLock    = &sync.Mutex{}
	webhookSecret = []byte("")
)

func setupWebhookVerification(config config.Provider) error {
	err := config.GetLive(context.Background(), "config.treezor", func(data []byte) {
		da := gjson.GetManyBytes(data, "webhookSecret")
		configLock.Lock()
		defer configLock.Unlock()

		webhookSecret = []byte(da[0].String())
		if !da[0].Exists() {
			panic("incomplete keys in config.treezor")
		}
	})
	if err != nil {
		return err
	}

	return nil
}

func encodeRunesToASCII(orig string) (string, error) {
	b := strings.Builder{}
	for _, c := range orig {
		if c < 31 || c > 127 {
			q := strconv.QuoteRuneToASCII(c)
			if _, err := b.WriteString(q[1 : len(q)-1]); err != nil {
				return "", err
			}
		} else {
			if _, err := b.WriteRune(c); err != nil {
				return "", err
			}
		}
	}
	return b.String(), nil
}

var (
	// ErrInvalidSignature is returned when the signature is invalid
	ErrInvalidSignature = errors.New("invalid signature")
)

func verifyWebhookSignature(rawPayload []byte, L *zap.Logger, outTreezorSig, outComputedSig *string) (*treezor.WebhookEvent, string, error) {
	event := treezor.WebhookEvent{}
	if err := json.Unmarshal(rawPayload, &event); err != nil {
		if L != nil {
			L.Error("Payload unmarshall error", zap.Error(err), zap.ByteString("payload", rawPayload))
		}
		return nil, "", err // HTTP 500, will cause a retry every second until success, up to 30 times
	}

	if strings.TrimSpace(event.PayloadSignature) == "" {
		if L != nil {
			L.Error("Payload signature missing", zap.ByteString("payload", rawPayload))
		}
		return nil, "", ErrInvalidSignature
	}

	// ----- Verify the webhook signature -----
	payloadStr := gjson.GetBytes(rawPayload, "object_payload")
	payloadEscaped := strings.ReplaceAll(payloadStr.Raw, "/", "\\/") // this was not in the official documentation
	payloadEncoded, err := encodeRunesToASCII(payloadEscaped)
	if err != nil {
		if L != nil {
			L.Error("Payload re-encoding error", zap.Error(err), zap.ByteString("payload", rawPayload))
		}
		return nil, "", err // HTTP 500 ... called again in 1 minute (max 30 times)
	}
	mac := hmac.New(sha256.New, webhookSecret)
	mac.Write([]byte(payloadEncoded))
	sigb := mac.Sum(nil)
	sig64 := base64.StdEncoding.EncodeToString(sigb)
	if outTreezorSig != nil {
		*outTreezorSig = event.PayloadSignature
	}
	if outComputedSig != nil {
		*outComputedSig = sig64
	}
	if sig64 != event.PayloadSignature {
		if L != nil {
			L.Error("Payload signature mismatch", zap.String("sig64", sig64), zap.String("incoming64", event.PayloadSignature), zap.ByteString("payload", rawPayload))
		}
		return nil, "", ErrInvalidSignature
	}
	return &event, payloadStr.Raw, nil
}
