package main

import (
	"net/http"
	"strings"
	bus2 "yochbee/_base/bus"
	json2 "yochbee/_base/json"
	web2 "yochbee/_base/web"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
)

func registerMandateRESTAPIs(busrpc bus2.RpcClient, e *echo.Echo) {

	// --------------------
	e.GET("/mandates/:accountId", func(c echo.Context) error {
		// Get the account from the context (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		resp, err := treezor.SearchMandates(busrpc, account.TreezorUserId, 0)
		if err != nil {
			return err // HTTP 500
		}

		mandates := make([]any, len(resp.Mandates))
		for i, mandate := range resp.Mandates {
			mandates[i] = mandate
		}
		return web2.ListOfMobileJsoner(c, "mandates", mandates)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// --------------------
	e.POST("/mandates/:accountId", func(c echo.Context) error {
		L := logging.L.Named("create-mandate")
		L.Info("Started...")
		defer L.Info("Finished")

		// Get the account from the context (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		request := struct {
			Label         string `json:"label" validate:"required"`
			SddType       string `json:"sddType" validate:"required"`
			SequenceType  string `json:"sequenceType" validate:"required"`
			DebtorName    string `json:"debtorName" validate:"required"`
			DebtorAddress string `json:"debtorAddress" validate:"required"`
			DebtorCity    string `json:"debtorCity" validate:"required"`
			DebtorZipCode string `json:"debtorZipCode" validate:"required"`
			DebtorCountry string `json:"debtorCountry" validate:"required"`
			DebtorIban    string `json:"debtorIban" validate:"required"`
			SignatureDate string `json:"signatureDate" validate:"required"` // YYYY-MM-DD — just inherits the format from Treezor
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid request body")
		}

		// Call Treezor to create the Mandate
		createMandateRequest := &treezor.CreateMandateRequest{
			TreezorUserId: account.TreezorUserId,
			Label:         request.Label,
			SddType:       request.SddType,
			SequenceType:  request.SequenceType,
			DebtorName:    request.DebtorName,
			DebtorAddress: request.DebtorAddress,
			DebtorCity:    request.DebtorCity,
			DebtorZipCode: request.DebtorZipCode,
			DebtorCountry: request.DebtorCountry,
			DebtorIban:    request.DebtorIban,
			SignatureDate: request.SignatureDate,
			CreatedIp:     c.RealIP(),
		}

		resp, err := treezor.CreateMandate(busrpc, createMandateRequest)
		if err != nil {
			return err // HTTP 500
		}

		return web2.SingleEntityForMobile(c, resp.Mandate, "mandate")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareRequireAndCacheSCAProof)

	// --------------------
	e.GET("/mandates/:accountId/:mandateId", func(c echo.Context) error {
		// Get the account from the context (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		mandateId := strings.TrimSpace(c.Param("mandateId"))

		resp, err := treezor.GetMandateByID(busrpc, account.TreezorUserId, mandateId)
		if err != nil {
			return err // HTTP 500
		}

		return web2.SingleEntityForMobile(c, resp.Mandate, "mandate")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// --------------------
	e.DELETE("/mandates/:accountId/:mandateId", func(c echo.Context) error {
		mandateId := strings.TrimSpace(c.Param("mandateId"))

		// Get the account from the context (thanks to the middleware)
		account := c.Get("account").(*accounts.Account)

		resp, err := treezor.DeleteMandate(busrpc, account.TreezorUserId, mandateId)
		if err != nil {
			return err // HTTP 500
		}

		if !resp.Ok {
			return web2.Error(c, http.StatusInternalServerError, "Failed to delete mandate")
		}

		return web2.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)
}
