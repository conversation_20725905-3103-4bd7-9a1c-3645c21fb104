package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToTreezorPayouts(signaling bus2.Client, transactionsColl *banking.TransactionsCollection) error {
	payoutsQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-payouts", true, false,
		[]string{
			// We also allow handling of the `payout.create` event because the initial event after the API call can be
			// incomplete, and the `payout.create` is the event that will properly complete the Payout object.
			"treezor.payout.create",
			"treezor.payout.update",
			"treezor.payout.cancel",
		})
	if err != nil {
		return err
	}

	if err = signaling.SubscribeToEventForever(payoutsQueue, "treezor.payout.*", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.payout.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		tpayouts, err := treezor.UnwrapPayoutsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Payouts from webhook event", zap.Error(err))
			return
		}

		for _, tpayout := range tpayouts {
			// Transfer the Webhook timestamp
			tpayout.WebhookTimestamp = event.WebhookTimestamp

			// Get the matching Transaction
			transaction, err := transactionsColl.GetByTreezorPayoutId(tpayout.TreezorPayoutId)
			if err != nil {
				L.Error("Error while getting Transaction from DB", zap.Error(err))
				continue
			}
			if transaction == nil {
				L.Warn("No matching transaction found (yet) for this Payout", zap.Any("treezorPayout", tpayout))
				continue
			}

			// If Payout is newer than Transaction, update Transaction
			if tpayout.EnrichBankingTransaction(transaction) {
				transaction.UpdatedAt = dbnosql.PNow()
				if err = transactionsColl.Update(transaction); err != nil {
					L.Error("Error while updating Transaction", zap.Error(err))
					continue
				}
			}
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}
	return nil
}
