package main

import (
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToTreezorCardTransactions(signaling bus2.Client, transactionsColl *banking.TransactionsCollection) error {
	ctQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-cardtransactions", true, false,
		[]string{
			"treezor.cardtransaction.create",
			"treezor.cardtransaction.update",
		})
	if err != nil {
		return err
	}

	// --------------------
	if err = signaling.SubscribeToEventForever(ctQueue, "banking/subscriptions--cardtransactions.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.cardtransaction.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		tcts, err := treezor.UnwrapCardTransactionsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping CardTransactions from Treezor webhook", zap.Error(err))
			return
		}

		for _, tct := range tcts {
			// Transfer the timestamp from the incoming Webhook
			tct.TreezorWebhookTimestamp = event.WebhookTimestamp

			// Get the matching Transaction
			transaction, err := transactionsColl.GetByTreezorCardTransactionId(tct.TreezorId)
			if err != nil {
				L.Error("Error while getting Transaction from DB", zap.Error(err))
				continue
			}
			newBankingTransactionCreated := false
			if transaction == nil {
				switch tct.PaymentStatus {
				case "A", "R", "M", "I", "V":
					// We only create a new Banking Transaction if the status is one of these
					// See also: https://docs.treezor.com/guide/cards/transactions.html#statuses-paymentstatus

					// Create a new Banking Transaction: our own version until we get the webhook for the actual Transaction (not Card Transaction) from Treezor
					newBankingTransactionCreated = true
					now := time.Now()
					transaction = &banking.Transaction{
						ID:                                     transactionsColl.GenerateUniqueId(),
						TreezorId:                              0, // this is not yet known
						TreezorCardTransactionId:               tct.TreezorId,
						TreezorCardTransactionWebhookTimestamp: tct.TreezorWebhookTimestamp,

						Amount:   tct.PaymentAmount,
						Currency: tct.PaymentCurrency,

						CreatedAt: now,
						UpdatedAt: &now,

						WebhookTimestamp: tct.TreezorWebhookTimestamp,
					}
					L.Info("Creating new Banking Transaction", zap.Any("transaction", transaction))
				case "S", "C":
					// We ignore these statuses
					L.Info("Ignoring CardTransaction with status because they should already match actual Treezor Transaction", zap.String("status", tct.PaymentStatus))
				default:
					L.Info("Ignoring CardTransaction with status", zap.String("status", tct.PaymentStatus))
				}
			}

			// Enrich the Banking Transaction from the information received from Treezor
			if transaction != nil && tct.EnrichBankingTransaction(transaction) {
				if newBankingTransactionCreated {
					if _, err = transactionsColl.Insert(transaction); err != nil {
						L.Error("Error while inserting a new Transaction in DB", zap.Error(err))
						continue
					}
				} else {
					transaction.UpdatedAt = dbnosql.PNow()
					if err = transactionsColl.Update(transaction); err != nil {
						L.Error("Error while updating a Transaction in DB", zap.Error(err))
						continue
					}
				}
			} else {
				continue
			}
		}

		// Mark this event as processed
		if err = treezor.MarkWebhookEventAsProcessed(signaling, event.WebhookID); err != nil {
			L.Error("Error while marking CardTransaction event as processed (bus queue ☢️)", zap.Error(err), zap.String("webhookEventId", event.WebhookID))
			return
		}

		// Don't forget to acknowledge the message
		_ = ack()
	}); err != nil {
		return err
	}

	return nil
}
