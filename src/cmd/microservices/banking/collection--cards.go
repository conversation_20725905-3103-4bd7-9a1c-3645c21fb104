package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/banking"
)

type CardsCollection struct {
	dbnosql.DataCollection
}

func setupCardsCollection(db dbnosql.Database) (*CardsCollection, error) {
	coll := db.DeclareCollection("banking-cards", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &banking.Card{}
	})

	if err := coll.SetMultipleIndexesOn("accountId", "createdAt"); err != nil {
		return nil, err
	}

	// This is a restriction at the DB level where only one card type can be possible for any given account. So, basically
	// a user can have 1 physical card and 1 virtual card.
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "accountId", Order: dbnosql.OrderASC},
			{Name: "isPhysical", Order: dbnosql.OrderASC},
			{Name: "virtualConverted", Order: dbnosql.OrderASC},
		},
		Name: "userCardType",
	}); err != nil {
		return nil, err
	}

	return &CardsCollection{coll}, nil
}

func (c *CardsCollection) GetByTreezorCardId(treezorCardID int64) (*banking.Card, error) {
	return dbnosql.GetOneByField[*banking.Card](c, "treezorCardId", treezorCardID)
}

func (c *CardsCollection) Update(card *banking.Card) error {
	if card.ID == "" {
		return dbnosql.ErrMissingID
	}
	return c.UpdateById(card.ID, card)
}

func (c *CardsCollection) GetCardsForTreezorUserId(treezorUserId int64) ([]*banking.Card, error) {
	q := c.GetQueryBuilder()
	_ = q.Set(&dbnosql.Condition{"treezorUserId", "==", treezorUserId})
	list, err := c.Find(q)
	if err != nil {
		return nil, err
	}
	return dbnosql.ConvertDocsList[*banking.Card](list), nil
}

func getUserCurrentCard(coll *CardsCollection, accountId string, isPhysical bool) (*banking.Card, error) {
	q := coll.GetQueryBuilder()
	_ = q.Set(&dbnosql.Condition{"accountId", "==", accountId})
	_ = q.Set(&dbnosql.Condition{"isPhysical", "==", isPhysical})
	docs, err := coll.Find(q)
	if err != nil {
		return nil, err
	}

	if len(docs) == 0 {
		return nil, nil
	}
	return docs[0].(*banking.Card), nil
}
