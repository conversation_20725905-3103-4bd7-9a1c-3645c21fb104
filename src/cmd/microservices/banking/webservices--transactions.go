package main

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/utils"
	web2 "yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"github.com/tidwall/sjson"
	"go.uber.org/zap"
)

func registerTransactionsRESTAPIs(
	busrpc bus2.RpcClient,
	e *echo.Echo,
	cardsColl *CardsCollection,
) {
	checkDates := func(c echo.Context, L *zap.Logger) (time.Time, time.Time, error) {
		// Load Paris location for proper timezone handling with daylight saving
		parisLoc, err := time.LoadLocation("Europe/Paris")
		if err != nil {
			L.Error("Failed to load Europe/Paris location", zap.Error(err))
			// Fallback to UTC if Paris location can't be loaded
			parisLoc = time.UTC
		}

		// Parse and validate the request parameters
		startDateStr := strings.TrimSpace(c.QueryParam("startDate"))
		endDateStr := strings.TrimSpace(c.QueryParam("endDate"))
		var dateFrom time.Time
		if startDateStr != "" {
			startDateInt, err := strconv.ParseInt(startDateStr, 10, 64)
			if err != nil {
				L.Error("Error parsing startDate", zap.Error(err))
				return time.Time{}, time.Time{}, web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid startDate")
			}
			// Parse as UTC first, then interpret components as Paris local time
			utcTime := time.Unix(startDateInt, 0)
			year, month, day := utcTime.Date()
			hour, min, sec := utcTime.Clock()
			// Create time in Paris timezone - this will automatically handle DST
			dateFrom = time.Date(year, month, day, hour, min, sec, 0, parisLoc)
		} else {
			// Default to 7 days ago in Paris timezone
			dateFrom = time.Now().In(parisLoc).Add(-7 * 24 * time.Hour)
		}
		var dateTo time.Time
		if endDateStr != "" {
			endDateInt, err := strconv.ParseInt(endDateStr, 10, 64)
			if err != nil {
				L.Error("Error parsing endDate", zap.Error(err))
				return time.Time{}, time.Time{}, web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid endDate")
			}
			// Parse as UTC first, then interpret components as Paris local time
			utcTime := time.Unix(endDateInt, 0)
			year, month, day := utcTime.Date()
			hour, min, sec := utcTime.Clock()
			// Create time in Paris timezone - this will automatically handle DST
			dateTo = time.Date(year, month, day, hour, min, sec, 0, parisLoc)
		} else {
			// Default to 5 minutes in the future in Paris timezone
			dateTo = time.Now().In(parisLoc).Add(5 * time.Minute)
		}

		// Ensure that dateFrom is before dateTo
		if dateFrom.After(dateTo) {
			L.Error("createdDateFrom is after createdDateTo")
			return time.Time{}, time.Time{}, web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "createdDateFrom is after createdDateTo")
		}

		return dateFrom, dateTo, nil
	}

	// ╔═╗┌─┐┌┬┐  ┌─┐┬─┐┌─┐┌─┐┬ ┬  ╔═╗╔═╗╔═╗╦═╗╔═╗╔╦╗╦╔═╗╔╗╔╔═╗
	// ║ ╦├┤  │   ├┤ ├┬┘├┤ └─┐├─┤  ║ ║╠═╝║╣ ╠╦╝╠═╣ ║ ║║ ║║║║╚═╗
	// ╚═╝└─┘ ┴   └  ┴└─└─┘└─┘┴ ┴  ╚═╝╩  ╚═╝╩╚═╩ ╩ ╩ ╩╚═╝╝╚╝╚═╝
	e.GET("/operations/:accountId", func(c echo.Context) error {
		L := logging.L.Named("get.operations")
		L.Info("Started")
		defer L.Info("Done.")

		dateFrom, dateTo, err := checkDates(c, L)
		if err != nil {
			return err
		}
		pageSize := strings.TrimSpace(c.QueryParam("pageSize"))
		pageSizeInt := 25
		if pageSize != "" {
			pageSizeInt, err = strconv.Atoi(pageSize)
			if err != nil {
				L.Error("Error parsing pageSize", zap.Error(err))
				return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid pageSize")
			}
		}
		cursor := strings.TrimSpace(c.QueryParam("cursor"))

		account := c.Get("account").(*accounts.Account)
		if account.TreezorPrimaryWalletId == 0 {
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Account not yet associated with Payment Provider")
		}

		result, err := treezor.GetFreshOperations(busrpc, account.TreezorUserId, account.TreezorPrimaryWalletId, pageSizeInt, cursor, dateFrom, dateTo)
		if err != nil {
			if err.Error() == treezor.ErrorNoMoreScaSessionJWT.Error() { // we do a string comparison because IPC
				return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCASessionExpired, "SCA session expired")
			}

			L.Error("Error getting fresh operations from Treezor", zap.Error(err))
			return err // HTTP 500
		}

		return c.JSON(http.StatusOK, result)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareConditionalSCAProofForDates)

	// ╔═╗┌─┐┌┬┐  ┌─┐┬─┐┌─┐┌─┐┬ ┬  ╔═╗╦═╗╦╔╦╗╔═╗╦═╗╦ ╦  ┌┬┐┬─┐┌─┐┌┐┌┌─┐┌─┐┌─┐┌┬┐┬┌─┐┌┐┌┌─┐
	// ║ ╦├┤  │   ├┤ ├┬┘├┤ └─┐├─┤  ╠═╝╠╦╝║║║║╠═╣╠╦╝╚╦╝   │ ├┬┘├─┤│││└─┐├─┤│   │ ││ ││││└─┐
	// ╚═╝└─┘ ┴   └  ┴└─└─┘└─┘┴ ┴  ╩  ╩╚═╩╩ ╩╩ ╩╩╚═ ╩    ┴ ┴└─┴ ┴┘└┘└─┘┴ ┴└─┘ ┴ ┴└─┘┘└┘└─┘
	e.GET("/transactions/:accountId/primary", func(c echo.Context) error {
		L := logging.L.Named("get.transactions")
		L.Info("Started")
		defer L.Info("Done.")

		dateFrom, dateTo, err := checkDates(c, L)
		if err != nil {
			return err
		}

		account := c.Get("account").(*accounts.Account)
		transactionsResponse, err := treezor.GetFreshTransactions(busrpc, account.TreezorUserId, account.TreezorPrimaryWalletId, dateFrom, dateTo)
		if err != nil {
			if err.Error() == treezor.ErrorNoMoreScaSessionJWT.Error() { // we do a string comparison because IPC
				return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCASessionExpired, "SCA session expired")
			}

			L.Error("Error getting fresh transactions from Treezor", zap.Error(err))
			return err // HTTP 500
		}

		// Skip settled Card transactions
		primaryWalletTransactions := banking.SkipCardTransactions(transactionsResponse.Transactions)

		// Get the associated Payin for each transaction (if applicable), via Treezor microservice
		payinIds := make([]interface{}, 0, len(primaryWalletTransactions))
		for _, v := range primaryWalletTransactions {
			if v.TreezorPayinId == nil {
				continue
			}
			payinIds = append(payinIds, v.TreezorPayinId)
		}
		payinsResponse, err := treezor.GetMultiplePayinsByTreezorIds(busrpc, payinIds)
		if err != nil {
			L.Error("Error getting multiple Payins by Treezor IDs", zap.Error(err), zap.Any("payinIds", payinIds))
			return err // HTTP 500
		}

		// Sort by transaction.createdAt
		transactions := banking.SortTransactionsByCreatedAt(primaryWalletTransactions)
		anyTransactions := make([]interface{}, len(transactions))
		for i, v := range transactions {
			// Check if this transaction has an associated Payin
			payin, ok := payinsResponse.Payins[utils.InterfaceToString(v.TreezorPayinId)]

			var (
				b   []byte
				err error
				vi  interface{} = v
			)
			if j, ok := vi.(web2.MobileJSONer); ok {
				b, err = j.ToMobileJSON()
				if err != nil {
					return err // HTTP 500
				}
			} else {
				// Not an AppJsoner, just use the original value
				b, err = json.Marshal(v)
				if err != nil {
					return err // HTTP 500
				}
			}

			if ok {
				pb, err := json.Marshal(payin)
				if err != nil {
					return err // HTTP 500
				}

				// Add the Payin details to the transaction
				b, err = sjson.SetRawBytes(b, "payin", pb)
				if err != nil {
					return err // HTTP 500
				}
				anyTransactions[i] = json.RawMessage(b)
			} else {
				// Set as null
				b, err = sjson.SetBytes(b, "payin", nil)
				if err != nil {
					return err // HTTP 500
				}
				anyTransactions[i] = json.RawMessage(b)
			}
		}

		return web2.ListOfMobileJsonerWithoutKey(c, anyTransactions)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareConditionalSCAProofForDates)

	// ╔═╗┌─┐┌┬┐  ┌─┐┬─┐┌─┐┌─┐┬ ┬  ╔═╗╔═╗╦═╗╔╦╗  ┌┬┐┬─┐┌─┐┌┐┌┌─┐┌─┐┌─┐┌┬┐┬┌─┐┌┐┌┌─┐
	// ║ ╦├┤  │   ├┤ ├┬┘├┤ └─┐├─┤  ║  ╠═╣╠╦╝ ║║   │ ├┬┘├─┤│││└─┐├─┤│   │ ││ ││││└─┐
	// ╚═╝└─┘ ┴   └  ┴└─└─┘└─┘┴ ┴  ╚═╝╩ ╩╩╚══╩╝   ┴ ┴└─┴ ┴┘└┘└─┘┴ ┴└─┘ ┴ ┴└─┘┘└┘└─┘
	e.GET("/transactions/:accountId/on-card", func(c echo.Context) error {
		L := logging.L.Named("get.transactions.card.fresh")
		L.Info("Started")
		defer L.Info("Done.")

		// Search for the card
		account := c.Get("account").(*accounts.Account)
		card, err := getUserCurrentCard(cardsColl, account.ID, true)
		if err != nil {
			L.Error("Error retrieving card", zap.Error(err))
			return err // HTTP 500
		}
		if card == nil {
			L.Warn("No Card found")
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 404, "No Card found")
		}

		dateFrom, dateTo, err := checkDates(c, L)
		if err != nil {
			return err
		}

		cardTransactionsResponse, err := treezor.GetFreshCardTransactions(busrpc, account.TreezorUserId, card.TreezorCardId, dateFrom, dateTo)
		if err != nil {
			if err.Error() == treezor.ErrorNoMoreScaSessionJWT.Error() { // we do a string comparison because IPC
				return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCASessionExpired, "SCA session expired")
			}

			L.Error("Error getting fresh card transactions from Treezor", zap.Error(err))
			return err // HTTP 500
		}

		if cardTransactionsResponse == nil {
			L.Warn("No card transactions found")
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "No card transactions found")
		}

		// Sort by transaction.createdAt
		cardTransactions := treezor.SortCardTransactionsByCreatedAt(cardTransactionsResponse.CardTransactions)
		anyCardTransactions := make([]interface{}, len(cardTransactions)) // needed to get a []any
		for i, v := range cardTransactions {
			anyCardTransactions[i] = v
		}
		return web2.ListOfMobileJsonerWithoutKey(c, anyCardTransactions)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareConditionalSCAProofForDates)
}
