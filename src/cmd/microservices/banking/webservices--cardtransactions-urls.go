package main

import (
	"fmt"
	"net/http"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/web"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func registerCardTransactionsURLsRESTAPIs(busrpc bus2.RpcClient, e *echo.Echo) {
	// ╔═╗╔═╗╦═╗╔╦╗  ╔╦╗╦═╗╔═╗╔╗╔╔═╗╔═╗╔═╗╔╦╗╦╔═╗╔╗╔╔═╗  ╦ ╦╦═╗╦
	// ║  ╠═╣╠╦╝ ║║   ║ ╠╦╝╠═╣║║║╚═╗╠═╣║   ║ ║║ ║║║║╚═╗  ║ ║╠╦╝║
	// ╚═╝╩ ╩╩╚══╩╝   ╩ ╩╚═╩ ╩╝╚╝╚═╝╩ ╩╚═╝ ╩ ╩╚═╝╝╚╝╚═╝  ╚═╝╩╚═╩═╝
	// ╔═╗╔═╗╔╗╔╔═╗╦═╗╔═╗╔╦╗╦╔═╗╔╗╔
	// ║ ╦║╣ ║║║║╣ ╠╦╝╠═╣ ║ ║║ ║║║║
	// ╚═╝╚═╝╝╚╝╚═╝╩╚═╩ ╩ ╩ ╩╚═╝╝╚╝
	e.GET("/card-transactions-url/:cardId", func(c echo.Context) error {
		L := logging.L.Named("get.card-transactions-url")
		L.Info("Started")
		defer L.Info("Finished")

		// Get cardId from URL parameters
		cardIdStr := strings.TrimSpace(c.Param("cardId"))
		if cardIdStr == "" {
			L.Error("Missing cardId parameter")
			return web.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "cardId parameter is required")
		}

		// Parse cardId to int64 (assuming it's a Treezor card ID)
		var treezorCardId int64
		if _, err := fmt.Sscanf(cardIdStr, "%d", &treezorCardId); err != nil {
			L.Error("Invalid cardId format", zap.Error(err))
			return web.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "cardId must be a valid number")
		}

		// Get date parameters from query string
		dateFrom := strings.TrimSpace(c.QueryParam("dateFrom"))
		dateTo := strings.TrimSpace(c.QueryParam("dateTo"))

		// Validate date parameters
		if dateFrom == "" || dateTo == "" {
			L.Error("Missing date parameters", zap.String("dateFrom", dateFrom), zap.String("dateTo", dateTo))
			return web.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "dateFrom and dateTo query parameters are required (YYYY-MM-DD format)")
		}

		// Basic date format validation
		if _, err := time.Parse("2006-01-02", dateFrom); err != nil {
			L.Error("Invalid dateFrom format", zap.Error(err))
			return web.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "dateFrom must be in YYYY-MM-DD format")
		}

		if _, err := time.Parse("2006-01-02", dateTo); err != nil {
			L.Error("Invalid dateTo format", zap.Error(err))
			return web.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "dateTo must be in YYYY-MM-DD format")
		}

		L.Info("Calling Treezor service for URL generation",
			zap.Int64("treezorCardId", treezorCardId),
			zap.String("dateFrom", dateFrom),
			zap.String("dateTo", dateTo))

		// Call the Treezor microservice to generate the URL
		response, err := bus2.CallMethodMU[treezor.GenerateCardTransactionsURLResponse](busrpc, treezor.Method_GenerateCardTransactionsURL, &treezor.GenerateCardTransactionsURLRequest{
			TreezorCardId: treezorCardId,
			DateFrom:      dateFrom,
			DateTo:        dateTo,
		}, 10*time.Second)
		if err != nil {
			L.Error("Error calling Treezor service", zap.Error(err))
			return err // HTTP 500
		}

		L.Info("URL generation successful", zap.String("generatedURL", response.URL))

		// Return the response with generated URL and converted dates
		return c.JSON(http.StatusOK, map[string]interface{}{
			"success":             true,
			"url":                 response.URL,
			"convertedDateFromTZ": response.ConvertedDateFromTZ,
			"convertedDateToTZ":   response.ConvertedDateToTZ,
			"timezone":            "Europe/Paris",
			"treezorCardId":       treezorCardId,
		})
	})
}
