package main

import (
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToTreezorBeneficiaryEvents(signaling bus2.Client, busrpc bus2.RpcClient, beneficiariesColl *BeneficiariesCollection) error {
	bQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-beneficiaries", true, false,
		[]string{
			"treezor.beneficiary.create", // from Treezor
		})
	if err != nil {
		return err
	}

	if err = signaling.SubscribeToEventForever(bQueue, "banking/subscriptions--beneficiaries.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.beneficiary.create)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		beneficiaries, err := treezor.UnwrapBeneficiaryRawFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Beneficiary from Treezor webhook", zap.Error(err))
			return
		}
		b := beneficiaries[0]

		// Check if the beneficiary already exists in the database
		beneficiary, err := beneficiariesColl.GetOneByField("treezorBeneficiaryId", b.ID)
		if err != nil {
			L.Error("Error while getting Beneficiary from DB", zap.Error(err))
			return
		}

		if beneficiary == nil {
			// Get the account ID from the Treezor User ID
			account, err := accounts.GetByTreezorUser(busrpc, b.UserId)
			if err != nil {
				L.Error("Error while getting account from accounts microservice", zap.Error(err))
				return
			}
			if account == nil {
				L.Error("Account not found", zap.Int64("treezorUserID", b.UserId))
				return
			}

			// Create a new Beneficiary
			now := time.Now()
			beneficiary = &banking.Beneficiary{
				ID:                   beneficiariesColl.GenerateUniqueId(),
				AccountId:            account.ID,
				TreezorUserId:        b.UserId,
				TreezorBeneficiaryId: b.ID,
				NickName:             b.NickName,
				Name:                 b.Name,
				Address:              b.Address,
				IBAN:                 b.IBAN,
				BIC:                  b.BIC,
				IsActive:             b.IsActive,
				IsUsableForSCT:       b.UsableForSCT,
				CreatedAt:            now,
				UpdatedAt:            &now,
			}
			L.Info("Creating new Beneficiary", zap.Any("beneficiary", beneficiary))

			if _, err = beneficiariesColl.Insert(beneficiary); err != nil {
				L.Error("Error while inserting a new Beneficiary in DB", zap.Error(err))
				return
			}

			// Send a push notification to the user about the new beneficiary
			eventName := "banking.beneficiary.create-from-treezor"
			if err := signaling.PublishEvent(&bus2.Event{
				Topic:            eventName,
				Data:             event.Data,
				Retrigger:        true,
				WebhookTimestamp: event.WebhookTimestamp,
			}); err != nil {
				L.Error("Failed to re-publish event", zap.Error(err))
				return
			}
		}

		// Mark this event as processed
		if err = treezor.MarkWebhookEventAsProcessed(signaling, event.WebhookID); err != nil {
			L.Error("Error while marking Beneficiary event as processed (bus queue ☢️)", zap.Error(err), zap.String("webhookEventId", event.WebhookID))
			return
		}

		// Don't forget to acknowledge the message
		_ = ack()
	}); err != nil {
		return err
	}

	return nil
}
