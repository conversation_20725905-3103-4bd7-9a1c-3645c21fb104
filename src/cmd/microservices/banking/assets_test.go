package main

import (
	"testing"
	"time"

	"github.com/goodsign/monday"
)

func TestCurrencyFormatting(t *testing.T) {
	v := 1000000.12345
	expected := "1 000 000.12"
	actual := formatFrenchCurrencyValue(v)
	if actual != expected {
		t.<PERSON><PERSON><PERSON>("Expected %s, got %s", expected, actual)
	}
}

func TestFrenchMonth(t *testing.T) {
	year := 2024
	month := time.Month(4)
	monthStr := monday.Format(time.Date(year, month, 1, 0, 0, 0, 0, time.UTC), "January", monday.LocaleFrFR)
	if monthStr != "avril" {
		t.<PERSON><PERSON><PERSON>("Expected avril, got %s", monthStr)
	}
}
