package main

import (
	"net/http"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/labstack/echo/v4"
)

func registerBeneficiaryRESTAPIs(busrpc bus2.RpcClient, e *echo.Echo, beneficiariesColl dbnosql.DataCollection) {

	// --------------------
	e.GET("/beneficiaries/:accountId", func(c echo.Context) error {
		accountId := strings.TrimSpace(c.Param("accountId"))
		return web.PaginatedEntitiesForMobile(c, beneficiariesColl, func(q dbnosql.QueryBuilder) {
			_ = q.Set(&dbnosql.Condition{"accountId", "==", accountId})
		}, nil, nil, "beneficiaries")
	}, accounts.MiddlewareRequireAccountId)

	// --------------------
	e.POST("/beneficiaries/:accountId", func(c echo.Context) error {
		accountId := strings.TrimSpace(c.Param("accountId"))
		newBeneficiary, err := web.Form2Entity[banking.BeneficiaryFormDTO](c.Request().Body, func() (*banking.Beneficiary, error) {
			account, err := accounts.Get(busrpc, accountId)
			if err != nil {
				return nil, err
			}
			return &banking.Beneficiary{
				ID:            beneficiariesColl.GenerateUniqueId(),
				AccountId:     accountId,
				TreezorUserId: account.TreezorUserId,
				//TreezorBeneficiaryId: "",
				//NickName:             "",
				//Name:                 "",
				//Address:              "",
				//IBAN:                 "",
				//BIC:                  "",
				IsActive:       true,
				IsUsableForSCT: true,
				CreatedAt:      time.Now(),
			}, nil
		})
		if err != nil {
			return err // HTTP 500
		}

		// Sync with Treezor before saving to the database
		resp, err := bus2.CallMethodMU[treezor.SyncBeneficiaryToTreezorResponse](busrpc, treezor.Method_SyncBeneficiaryToTreezor, &treezor.SyncBeneficiaryToTreezorRequest{
			Beneficiary: newBeneficiary,
		}, 10*time.Second)
		if err != nil {
			str := err.Error()
			if strings.Contains(str, "Invalid BIC") {
				return web.Error(c, common.ErrorCodeBeneficiaryInvalidBIC, "Invalid BIC")
			}
			if strings.Contains(str, "IBAN validation failed") {
				return web.Error(c, common.ErrorCodeBeneficiaryInvalidIBAN, "Invalid IBAN")
			}
			return err // HTTP 500
		}

		// Insert into the database
		newBeneficiary.TreezorBeneficiaryId = resp.TreezorBeneficiary.ID
		if _, err := beneficiariesColl.Insert(newBeneficiary); err != nil {
			return err // HTTP 500
		}

		return web.SingleEntityForMobile(c, newBeneficiary, "beneficiary")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareRequireAndCacheSCAProof)

	// --------------------
	e.PUT("/beneficiaries/:accountId/update/:beneficiaryId", func(c echo.Context) error {
		updatedBeneficiary, err := web.Form2Entity[banking.BeneficiaryFormDTO](c.Request().Body, func() (*banking.Beneficiary, error) {
			id := c.Param("beneficiaryId")
			existing, err := dbnosql.GetOneByID[*banking.Beneficiary](beneficiariesColl, id)
			if err != nil {
				return nil, err
			}
			if existing == nil {
				return nil, web.Error(c, http.StatusNotFound, "Beneficiary not found")
			}

			now := time.Now()
			existing.UpdatedAt = &now
			return existing, nil
		})
		if err != nil {
			return err // HTTP 500, but possibily a proper error message from web.Error()
		}

		// Sync with Treezor before saving to the database
		if _, err = bus2.CallMethodMU[treezor.SyncBeneficiaryToTreezorResponse](busrpc, treezor.Method_SyncBeneficiaryToTreezor, &treezor.SyncBeneficiaryToTreezorRequest{
			Beneficiary: updatedBeneficiary,
		}, 10*time.Second); err != nil {
			return err // HTTP 500
		}

		// Update DB copy
		if err = beneficiariesColl.UpdateById(updatedBeneficiary.ID, updatedBeneficiary); err != nil {
			return err // HTTP 500
		}

		return web.SingleEntityForMobile(c, updatedBeneficiary, "beneficiary")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, treezor.MiddlewareRequireAndCacheSCAProof)

	// --------------------
	e.DELETE("/beneficiaries/:accountId/delete/:beneficiaryId", func(c echo.Context) error {
		// Get the beneficiary to delete
		id := c.Param("beneficiaryId")
		existing, err := dbnosql.GetOneByID[*banking.Beneficiary](beneficiariesColl, id)
		if err != nil {
			return err // HTTP 500
		}
		if existing == nil {
			return web.Error(c, http.StatusNotFound, "Beneficiary not found")
		}

		// See https://www.treezor.com/api-documentation/#/beneficiaries
		// This isn't an official API supported by Treezor, it doesn't exist. When it does, we're ready ;)
		//// Delete from Treezor
		//if _, err = bus2.CallMethodMU[treezor.DeleteBeneficiaryFromTreezorResponse](busrpc, treezor.Method_DeleteBeneficiaryFromTreezor, &treezor.DeleteBeneficiaryFromTreezorRequest{
		//	Beneficiary: existing,
		//}, 10*time.Second); err != nil {
		//	return err // HTTP 500
		//}

		// Beneficiary for SDD* purpose cannot be deleted
		if !existing.IsUsableForSCT {
			return web.Error(c, common.ErrorCodeBeneficiaryCannotDelete, "Beneficiary not deletable")
		}

		// Delete from DB
		if err = beneficiariesColl.DeleteById(id); err != nil {
			return err // HTTP 500
		}

		return web.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)
}
