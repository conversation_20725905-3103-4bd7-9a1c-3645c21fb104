package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/common/banking"
)

func registerSubscriptions(signaling bus2.Client, busrpc bus2.RpcClient, transactionsColl *banking.TransactionsCollection, cardsColl *CardsCollection, beneficiariesColl *BeneficiariesCollection) error {
	var err error

	if err = subscribeToTreezorPayouts(signaling, transactionsColl); err != nil {
		return err
	}

	// FIXME: Reactivate when working on Payout refunds
	//if err = subscribeToPayoutRefundsTreezorEvents(signaling); err != nil {
	//	return err
	//}

	if err = subscribeToTransactionEvents(signaling, busrpc, transactionsColl); err != nil {
		return err
	}

	if err = subscribeToTreezorPayins(signaling, transactionsColl); err != nil {
		return err
	}

	if err = subscribeToTreezorCards(signaling, cardsColl); err != nil {
		return err
	}

	if err = subscribeToSepa(signaling, busrpc); err != nil {
		return err
	}

	if err = subscribeToTreezorCardTransactions(signaling, transactionsColl); err != nil {
		return err
	}

	if err = subscribeToTreezorBeneficiaryEvents(signaling, busrpc, beneficiariesColl); err != nil {
		return err
	}

	return nil
}
