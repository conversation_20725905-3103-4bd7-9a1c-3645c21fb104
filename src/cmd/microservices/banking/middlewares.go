package main

import (
	"errors"
	"net/http"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func middlewareRetrieveCard(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		// Retrieve the existing card
		cardId := strings.TrimSpace(c.Param("cardId"))
		if cardId == "" {
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get the cardsColl from the context (this was set by a middleware before routes setup)
		cardsColl := c.Get("cardsColl").(dbnosql.DataCollection)

		card, err := dbnosql.GetOneByID[*banking.Card](cardsColl, cardId)
		if err != nil {
			return err // HTTP 500
		}
		if card == nil {
			// Didn't exist
			return web.Error(c, common.ErrorCodeCardNotFound, "Card not found")
		}

		// Ensure that the card belongs to the current user. This requires that the account is present in the context
		// (which is provided by middlewareRetrieveAccount)
		account, ok := c.Get("account").(*accounts.Account)
		if ok {
			if card.AccountId != account.ID {
				logging.L.Warn("👮️Attempt to access a card that does not belong to the user", zap.String("cardId", cardId), zap.String("accountId", account.ID))
				return web.Error(c, common.ErrorCodeCardNotFound, "Card not found")
			}
		} else {
			logging.L.Warn("accounts.MiddlewareRetrieveAccount was not used before banking.MiddlewareRetrieveCard")
		}

		c.Set("card", card)
		return next(c)
	}
}

// middlewareRetrieveTreezorWallet adds the current user's primary Treezor wallet to the context. It requires the other
// accounts.MiddlewareRetrieveAccount to be used first.
func middlewareRetrieveTreezorWallet(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		// Retrieve the existing account from the context
		account, ok := c.Get("account").(*accounts.Account)
		if !ok {
			logging.L.Error("Missing account in context", zap.String("path", c.Path()))
			return errors.New("missing account in context")
		}

		busrpc, ok := c.Get("busrpc").(bus2.RpcClient)
		if !ok {
			logging.L.Error("Missing busrpc in context", zap.String("path", c.Path()))
			return errors.New("missing busrpc in context")
		}

		// Via the bus, get the matching wallet
		response, err := bus2.CallMethodMU[treezor.GetWalletByTreezorIdResponse](busrpc, treezor.Method_GetWalletByTreezorId, &treezor.GetWalletByTreezorIdRequest{
			TreezorWalletId: account.TreezorPrimaryWalletId,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}
		if response.TreezorWallet == nil {
			logging.L.Error("Wallet not found", zap.Int64("treezorWalletId", account.TreezorPrimaryWalletId))
			return web.ErrorWithStatusAndCode(c, http.StatusNotFound, common.ErrorCodeWalletNotFound, "Wallet not found")
		}

		// Add the wallet to the context
		c.Set("wallet", response.TreezorWallet)

		// Add the treezorUserId to the context for SCA to work
		c.Set("treezorUserId", account.TreezorUserId)

		return next(c)
	}
}
