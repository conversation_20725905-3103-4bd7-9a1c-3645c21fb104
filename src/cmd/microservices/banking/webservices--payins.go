package main

import (
	"net/http"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	json2 "yochbee/_base/json"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func registerPayinRESTAPIs(busrpc bus2.RpcClient, e *echo.Echo) {

	// --------------------
	e.POST("/payins/:accountId/sdde", func(c echo.Context) error {
		request := struct {
			Amount        float64 `json:"amount"        validate:"required"`
			MandateId     int64   `json:"mandateId"     validate:"required"`
			PayinDate     string  `json:"payinDate"     validate:"required"`
			MessageToUser string  `json:"messageToUser"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			return err // HTTP 500
		}

		L := logging.L.Named("create-payin-sdde").With(zap.Int64("mandateId", request.MandateId))
		L.Info("Starting a Payin (SDDE) ...", zap.Any("request", request))

		// Check that the user is KYC validated
		account := c.Get("account").(*accounts.Account)
		progress := c.Get("progress").(*kyc.Progress)
		if !progress.HasCompletedKYCAndInitialPayment() {
			L.Error("👮 User not KYC validated: attempt to make a Payin", zap.String("accountId", account.ID))
			return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeKYCCompletionRequired, "User not KYC validated")
		}

		// Verify that the Wallet is in good state
		response, err := bus2.CallMethodMU[treezor.GetWalletByUserIdResponse](busrpc, treezor.Method_GetWalletByUserId, &treezor.GetWalletByUserIdRequest{
			TreezorUserId: account.TreezorUserId,
			WalletType:    treezor.WalletTypePaymentAccountWallet,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}
		if response.TreezorWallet.Status != "VALIDATED" {
			L.Error("Wallet not valid", zap.String("treezorWalletId", response.TreezorWallet.ID))
			return web.Error(c, common.ErrorCodeInvalidWalletState, "Invalid Wallet state")
		}

		// Ask the `treezor` microservice to create a new SDDE Payin
		resp, err := treezor.SendSDDEToUser(busrpc, &treezor.SendSDDEToUserRequest{
			TreezorUserId: account.TreezorUserId,
			WalletID:      response.TreezorWallet.TreezorWalletId,
			MandateID:     request.MandateId,
			Amount:        request.Amount,
			Currency:      "EUR",
			PayinDate:     request.PayinDate,
			MessageToUser: request.MessageToUser,
		})
		if err != nil {
			str := err.Error()
			if strings.Contains(str, "Amount too high for the wallet current balance") {
				L.Error("No enough balance", zap.String("treezorWalletId", response.TreezorWallet.ID))
				return web.Error(c, common.ErrorCodeNoEnoughWalletBalance, "No enough balance")
			}
			return err // HTTP 500
		}

		return web.SingleEntityForMobile(c, resp.Payin, "result")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId)
}
