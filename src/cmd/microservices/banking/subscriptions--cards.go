package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToTreezorCards(signaling bus2.Client, cardsColl *CardsCollection) error {
	cardsQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-cards", true, false,
		[]string{
			"treezor.card.*",
		})
	if err != nil {
		return err
	}

	// --------------------
	if err = signaling.SubscribeToEventForever(cardsQueue, "banking/subscriptions--cards.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.card.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		tcards, err := treezor.UnwrapCardsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.<PERSON>rror("Error while unwrapping Cards from Treezor webhook", zap.Error(err))
			return
		}

		switch event.Topic {
		case "treezor.card.createvirtual", "treezor.card.createphysical":
			L.Info("Skipping card event {event}")

			// Don't forget to ...
			_ = ack()
			return
		}

		//
		// Update the Banking Card from the new information from Treezor
		//

		for _, tcard := range tcards {
			// Transfer the timestamp from the incoming Webhook
			tcard.WebhookTimestamp = event.WebhookTimestamp

			// Get the matching Banking Card
			card, err := cardsColl.GetByTreezorCardId(tcard.TreezorCardID)
			if err != nil {
				L.Error("Error while getting Card from DB", zap.Error(err))
				continue
			}
			if card == nil {
				L.Error("Skipping sync: Banking Card not found in the DB", zap.Int64("treezorCardId", tcard.TreezorCardID))
				continue
			}

			previousBankingCardTimestamp := card.WebhookTimestamp
			if tcard.SyncToBankingCard(L, card) {
				card.UpdatedAt = dbnosql.PNow()
				if err := cardsColl.Update(card); err != nil {
					L.Error("Error while updating Card in DB", zap.Error(err))
					continue
				}
				L.Info("Banking Card updated 👍", zap.Int64("previousBankingCardTimestamp", previousBankingCardTimestamp), zap.Int64("newBankingCardTimestamp", card.WebhookTimestamp))
			}
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}

	return nil
}
