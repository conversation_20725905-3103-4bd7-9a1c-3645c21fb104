package main

import (
	"yochbee/_base/dbnosql"
	"yochbee/common/banking"
)

type BeneficiariesCollection struct {
	dbnosql.DataCollection
}

func setupBeneficiariesCollection(db dbnosql.Database) (*BeneficiariesCollection, error) {
	coll := db.DeclareCollection("banking-beneficiaries", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &banking.Beneficiary{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "accountId", Order: dbnosql.OrderASC},
			{Name: "treezorUserId", Order: dbnosql.OrderASC},
		},
		Name: "owner",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
			{Name: "updatedAt", Order: dbnosql.OrderDESC},
		},
		Name: "dates",
	}); err != nil {
		return nil, err
	}

	return &BeneficiariesCollection{coll}, nil
}
