package main

import (
	"net/http"
	"os"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"
	"yochbee/_base/utils"
	web2 "yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/eliezedeck/gobase/web"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func registerCardsRESTApis(busrpc bus2.RpcClient, e *echo.Echo,
	cardsColl *CardsCollection) {

	// Add common dependencies into the context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("busrpc", busrpc)
			c.Set("cardsColl", cardsColl)
			return next(c)
		}
	})

	doOrderCard := func(L *zap.Logger, c echo.Context, account *accounts.Account, isPhysical bool) error {
		// Get the request
		request := struct {
			Pin         string `json:"pin"       validate:"required"`
			Language    string `json:"language"  validate:"required"`
			Foreign     bool   `json:"foreign"   validate:"required"`
			Online      bool   `json:"online"    validate:"required"`
			Atm         bool   `json:"atm"`
			Nfc         bool   `json:"nfc"`
			DailyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"dailyLimits"`
			WeeklyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"weeklyLimits"`
			MonthlyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"monthlyLimits"`
			YearlyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"yearlyLimits"`
			GlobalLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"globalLimits"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Warn("Invalid request", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Ensure that this user can actually order a card
		progress := c.Get("progress").(*kyc.Progress)
		if !progress.HasCompletedKYCAndInitialPayment() {
			return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeKYCCompletionRequired, "You must pass KYC first")
		}

		// Get the user's current card
		card, err := getUserCurrentCard(cardsColl, account.ID, isPhysical)
		if err != nil {
			return err // HTTP 500
		}

		// Do not allow if the user already has a card (only one Physical/Virtual card per user)
		if card != nil && !card.IsBlocked() {
			return web.ErrorWithCode(c, common.ErrorCodeCardAlreadyOrdered, "You already have a Card")
		}

		// Ask the Treezor microservice to issue the card order
		response, err := bus2.CallMethodMU[treezor.OrderCardResponse](busrpc, treezor.Method_OrderCard, &treezor.OrderCardRequest{
			// Method is aware of both physical / virtual card types
			IsPhysical: isPhysical,

			Account:       account,
			PinCode:       request.Pin,
			Language:      request.Language,
			OptionForeign: utils.IntFromBool(request.Foreign),
			OptionOnline:  utils.IntFromBool(request.Online),
			OptionAtm:     utils.IntFromBool(request.Atm),
			OptionNfc:     utils.IntFromBool(request.Nfc),
			DailyLimits: &treezor.CardLimit{
				Withdrawals: request.DailyLimits.Withdrawals,
				Payments:    request.DailyLimits.Payments,
			},
			WeeklyLimits: &treezor.CardLimit{
				Withdrawals: request.WeeklyLimits.Withdrawals,
				Payments:    request.WeeklyLimits.Payments,
			},
			MonthlyLimits: &treezor.CardLimit{
				Withdrawals: request.MonthlyLimits.Withdrawals,
				Payments:    request.MonthlyLimits.Payments,
			},
			YearlyLimits: &treezor.CardLimit{
				Withdrawals: request.YearlyLimits.Withdrawals,
				Payments:    request.YearlyLimits.Payments,
			},
			GlobalLimits: &treezor.CardLimit{
				Withdrawals: request.GlobalLimits.Withdrawals,
				Payments:    request.GlobalLimits.Payments,
			},
		}, 15*time.Second)
		if err != nil {
			str := err.Error()
			if strings.Contains(str, "User informations are incomplete") {
				return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, common.ErrorCodeCardRequiresMoreProfileInfo, "Please fill in your profile information")
			}
			return err // HTTP 500
		}

		// Insert the Banking Card into the DB. There is a restriction where the user can only have one type of card at
		// a time, applies to both Physical and Virtual cards.
		card = &banking.Card{} // new card
		response.Card.SyncToBankingCard(L, card)
		card.ID = cardsColl.GenerateUniqueId()
		card.AccountId = account.ID
		if card.IsPhysical != isPhysical {
			logging.L.Warn("doOrderCard(): Card physical mismatch", zap.Bool("isPhysical", isPhysical), zap.Bool("card.IsPhysical", card.IsPhysical))
		}
		card.CreatedAt = time.Now()
		card.UpdatedAt = &card.CreatedAt
		card.WebhookTimestamp = card.CreatedAt.UnixMilli()
		if _, err = cardsColl.Insert(card); err != nil {
			return err // HTTP 500
		}

		// Return the Banking Card object to the user. Obviously, the status is not yet activated.
		return web2.SingleEntityForMobile(c, card, "card")
	}

	saveUpdatedActualCard := func(L *zap.Logger, account *accounts.Account, tcard *treezor.Card, oldCard *banking.Card) (updateCard *banking.Card, err error) {
		// Update the Banking Card in the DB
		updateCard = &banking.Card{}
		tcard.SyncToBankingCard(L, updateCard)
		updateCard.ID = oldCard.ID
		updateCard.AccountId = account.ID
		// updateCard.IsPhysical // upstream, as the card could be converted
		updateCard.Is3DSRegistered = oldCard.Is3DSRegistered
		updateCard.IsSCAEnabled = oldCard.IsSCAEnabled
		updateCard.CreatedAt = account.CreatedAt
		updateCard.UpdatedAt = dbnosql.PNow()
		if err := cardsColl.UpdateById(oldCard.ID, &updateCard); err != nil {
			return nil, err // HTTP 500
		}
		return updateCard, nil
	}

	e.POST("/cards/:accountId/order/physical", func(c echo.Context) error {
		L := logging.L.Named("order-physical-card")
		L.Info("Started ...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		return doOrderCard(L, c, account, true)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId, treezor.MiddlewareRequireAndCacheSCAProof)

	e.POST("/cards/:accountId/order/virtual", func(c echo.Context) error {
		L := logging.L.Named("order-virtual-card")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		return doOrderCard(L, c, account, false)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId, treezor.MiddlewareRequireAndCacheSCAProof)

	e.GET("/cards/:accountId", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)

		return web2.PaginatedEntitiesForMobile(c, cardsColl,
			func(q dbnosql.QueryBuilder) {
				_ = q.Set(&dbnosql.Condition{"accountId", "==", account.ID})
			},
			func(entries []interface{}) []interface{} {
				// Gather the IDs of the matching Wallets
				cardIds := make([]int64, 0, len(entries))
				cardsMap := make(map[int64]*banking.Card, len(entries))
				for _, e := range entries {
					bcard := e.(*banking.Card)
					cardIds = append(cardIds, bcard.WalletCardTransactionID)
					cardsMap[bcard.WalletCardTransactionID] = bcard
				}

				// Ask the Treezor microservice to get the Wallets
				response, err := bus2.CallMethodMU[treezor.GetWalletsByTreezorIdsResponse](busrpc, treezor.Method_GetWalletsByTreezorIds, &treezor.GetWalletsByTreezorIdsRequest{
					TreezorWalletIds: cardIds,
				}, 5*time.Second)
				if err != nil {
					logging.L.Warn("cards: failed to get matching wallets", zap.Error(err))
					return entries
				}

				// Update the Banking Cards that will be returned to the user by adding the Wallets to each
				for _, tcard := range response.TreezorWallets {
					if bcard, ok := cardsMap[tcard.TreezorWalletId]; ok {
						bcard.Wallet = tcard
					}
				}
				return entries
			}, nil, "cards")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.PUT("/cards/:accountId/activate/:cardId", func(c echo.Context) error {
		L := logging.L.Named("activate-card")
		L.Info("Started ...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		card := c.Get("card").(*banking.Card)
		if card.IsActivated() {
			L.Warn("Card is already activated")
			return web.ErrorWithCode(c, common.ErrorCodeCardAlreadyActivated, "Card already activated")
		}

		request := struct {
			CardLastFourDigits string `json:"cardLastFourDigits" validate:"required,min=4,max=4"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Warn("Invalid request", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		if len(card.MaskedPan) < 4 {
			L.Error("activate-card: Card MaskedPan is invalid", zap.String("MaskedPan", card.MaskedPan))
			return web.ErrorWithCode(c, common.ErrorCodeCardBadStatus, "Card is invalid, please contact support")
		}
		// Compare the last 4 digits of the masked page with the inputted last 4 digits
		if card.MaskedPan[len(card.MaskedPan)-4:] != request.CardLastFourDigits {
			L.Warn("Incorrect last 4-digits", zap.String("maskedPan", card.MaskedPan), zap.String("request", request.CardLastFourDigits))
			return web2.ErrorWithStatusAndCode(c, http.StatusForbidden, common.ErrorCodeCardBadStatus, "Provided Card last 4 digits are incorrect")
		}

		// Ask the Treezor microservice to do the card activation on our behalf
		_, err := bus2.CallMethodMU[treezor.ActivateCardResponse](busrpc, treezor.Method_ActivateCard, &treezor.ActivateCardRequest{
			TreezorCardId: card.TreezorCardId,
			Account:       account,
		}, 15*time.Second)
		if err != nil {
			L.Error("Failed to activate card", zap.Error(err))
			return err // HTTP 500
		}

		// Ask the Treezor microservice to register 3DS for this card
		// See discussion: https://wawashi.slack.com/archives/C06363YPLS0/p1719565066169319?thread_ts=**********.503929&cid=C06363YPLS0
		response, err := bus2.CallMethodMU[treezor.RegisterCard3DSResponse](busrpc, treezor.Method_RegisterCard3DS, &treezor.RegisterCard3DSRequest{
			TreezorCardId: card.TreezorCardId,
			Account:       account,
		}, 15*time.Second)
		if err != nil {
			L.Error("Failed to register 3DS: ", zap.Error(err))
			return err // HTTP 500
		}
		// Update the Banking Card in the DB ... this is different from the saveUpdatedActualCard() function above
		if response.Card.SyncToBankingCard(L, card) {
			card.Is3DSRegistered = true     // registered 3DS
			card.UpdatedAt = dbnosql.PNow() // update the timestamp
			if err = cardsColl.UpdateById(card.ID, &card); err != nil {
				L.Error("Failed to update card", zap.Error(err), zap.Any("treezorCard", response.Card))
				return err // HTTP 500
			}
		}

		isSCAEnabled := os.Getenv("IS_SCA_ENABLED") == "true"

		if isSCAEnabled {
			response, err := bus2.CallMethodMU[treezor.EnrollCardResponse](busrpc, treezor.Method_EnrollCard, &treezor.EnrollCardRequest{
				TreezorCardId: card.TreezorCardId,
				Account:       account,
			}, 10*time.Second)
			if err != nil {
				L.Error("Failed to enroll card (SCA)", zap.Error(err))
				return err // HTTP 500
			}

			// Card enrollment is successful
			if response.Ok {
				// Get the Treezor Card by card.TreezorCardId
				response, err := bus2.CallMethodMU[treezor.GetFreshCardResponse](busrpc, treezor.Method_GetFreshCard, &treezor.GetFreshCardRequest{
					TreezorCardId: card.TreezorCardId,
					TreezorUserId: account.TreezorUserId,
				}, 10*time.Second)
				if err != nil {
					L.Error("Failed to get card", zap.Error(err), zap.Any("card", card))
					return err // HTTP 500
				}
				// Update the Banking Card in the DB
				if response.Card.SyncToBankingCard(L, card) {
					card.IsSCAEnabled = true
					card.UpdatedAt = dbnosql.PNow() // update the timestamp
					if err = cardsColl.UpdateById(card.ID, &card); err != nil {
						L.Error("Failed to update card", zap.Error(err), zap.Any("card", card))
						return err // HTTP 500
					}
				}
			}
		}
		// Return the Banking Card object to the user. Obviously, the status is not yet activated.
		return web2.SingleEntityForMobile(c, card, "card")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.PUT("/cards/:accountId/pin/:cardId", func(c echo.Context) error {
		L := logging.L.Named("pin-card")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		card := c.Get("card").(*banking.Card)

		request := struct {
			OldPIN string `json:"oldPin" validate:"required"`
			NewPIN string `json:"newPin" validate:"required"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Warn("Invalid request", zap.Error(err))
			return err // HTTP 500
		}

		response, err := bus2.CallMethodMU[treezor.UpdateKnownCardPINResponse](busrpc, treezor.Method_UpdateKnownCardPIN, &treezor.UpdateKnownCardPINRequest{
			TreezorCardId: card.TreezorCardId,
			CurrentPIN:    request.OldPIN,
			NewPIN:        request.NewPIN,
			Account:       account,
		}, 10*time.Second)
		if err != nil {
			if strings.Contains(err.Error(), "Impossible to change the card's PIN") {
				return web2.ErrorWithStatusAndCode(c, http.StatusForbidden, common.ErrorCodeCardIncorrectPIN, "Old PIN might be incorrect")
			}
			return err // HTTP 500
		}

		updateCard, err := saveUpdatedActualCard(L, account, response.Card, card)
		if err != nil {
			L.Error("Failed to save updated card", zap.Error(err), zap.Any("treezorCard", response.Card))
			return err // HTTP 500
		}

		// Return the updated card
		return web2.SingleEntityForMobile(c, updateCard, "card")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.PUT("/cards/:accountId/lock-status/:cardId", func(c echo.Context) error {
		L := logging.L.Named("update-card-lock-status")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		card := c.Get("card").(*banking.Card)

		request := struct {
			LockStatus int `json:"lockStatus" validate:"oneof=0 1 2 3 4"` // see https://docs.treezor.com/guide/cards/modification.html#block-a-card
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Error("Error while validating request body", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		response, err := bus2.CallMethodMU[treezor.UpdateCardBlockingStatusResponse](busrpc, treezor.Method_UpdateCardBlockingStatus, &treezor.UpdateCardBlockingStatusRequest{
			TreezorCardId: card.TreezorCardId,
			Account:       account,
			LockStatus:    request.LockStatus,
		}, 10*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		updateCard, err := saveUpdatedActualCard(L, account, response.Card, card)
		if err != nil {
			L.Error("Error while saving updated card", zap.Error(err), zap.Any("treezorCard", response.Card))
			return err // HTTP 500
		}

		// Return the updated card
		return web2.SingleEntityForMobile(c, updateCard, "card")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.PUT("/cards/:accountId/update-limits/:cardId", func(c echo.Context) error {
		L := logging.L.Named("update-card-limits")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		card := c.Get("card").(*banking.Card)
		if !card.IsActivated() {
			L.Error("Card is not activated")
			return web.ErrorWithCode(c, common.ErrorCodeCardBadStatus, "Bad Card status")
		}

		request := struct {
			DailyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"dailyLimits"`
			WeeklyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"weeklyLimits"`
			MonthlyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"monthlyLimits"`
			YearlyLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			} `json:"yearlyLimits"`
			GlobalLimits struct {
				Withdrawals float64 `json:"atm"       validate:"gte=0"`
				Payments    float64 `json:"payments"  validate:"gte=0"`
			}
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Error("Error while validating request body", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		response, err := bus2.CallMethodMU[treezor.UpdateCardLimitsResponse](busrpc, treezor.Method_UpdateCardLimits, &treezor.UpdateCardLimitsRequest{
			TreezorCardId: card.TreezorCardId,
			DailyLimits: &treezor.CardLimit{
				Withdrawals: request.DailyLimits.Withdrawals,
				Payments:    request.DailyLimits.Payments,
			},
			WeeklyLimits: &treezor.CardLimit{
				Withdrawals: request.WeeklyLimits.Withdrawals,
				Payments:    request.WeeklyLimits.Payments,
			},
			MonthlyLimits: &treezor.CardLimit{
				Withdrawals: request.MonthlyLimits.Withdrawals,
				Payments:    request.MonthlyLimits.Payments,
			},
			YearlyLimits: &treezor.CardLimit{
				Withdrawals: request.YearlyLimits.Withdrawals,
				Payments:    request.YearlyLimits.Payments,
			},
			GlobalLimits: &treezor.CardLimit{
				Withdrawals: request.GlobalLimits.Withdrawals,
				Payments:    request.GlobalLimits.Payments,
			},
			Account: account,
		}, 10*time.Second)
		if err != nil {
			str := err.Error()
			if strings.Contains(str, "Please fill in at least one limit among these") {
				L.Warn("One or more limits need to be specified")
				return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, common.ErrorCodeCardLimitsRequired, "Specify at least a daily, weekly or monthly limit")
			}
			return err // HTTP 500
		}

		updateCard, err := saveUpdatedActualCard(L, account, response.Card, card)
		if err != nil {
			L.Error("Error while saving updated card", zap.Error(err), zap.Any("treezorCard", response.Card))
			return err // HTTP 500
		}

		// Return the updated card
		return web2.SingleEntityForMobile(c, updateCard, "card")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.PUT("/cards/:accountId/update-features/:cardId", func(c echo.Context) error {
		L := logging.L.Named("update-card-features")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		card := c.Get("card").(*banking.Card)
		if !card.IsActivated() {
			L.Error("Card is not activated")
			return web.ErrorWithCode(c, common.ErrorCodeCardBadStatus, "Bad Card status")
		}

		request := struct {
			OptionForeign bool `json:"foreign"`
			OptionOnline  bool `json:"online"`
			OptionAtm     bool `json:"atm"`
			OptionNfc     bool `json:"nfc"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Error("Error while validating request body", zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}

		response, err := bus2.CallMethodMU[treezor.UpdateCardFeaturesResponse](busrpc, treezor.Method_UpdateCardFeatures, &treezor.UpdateCardFeaturesRequest{
			TreezorCardId: card.TreezorCardId,
			OptionForeign: utils.IntFromBool(request.OptionForeign),
			OptionOnline:  utils.IntFromBool(request.OptionOnline),
			OptionAtm:     utils.IntFromBool(request.OptionAtm),
			OptionNfc:     utils.IntFromBool(request.OptionNfc),
			Account:       account,
		}, 10*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		updateCard, err := saveUpdatedActualCard(L, account, response.Card, card)
		if err != nil {
			L.Error("Error while saving updated card", zap.Error(err), zap.Any("treezorCard", response.Card))
			return err // HTTP 500
		}

		// Return the updated card
		return web2.SingleEntityForMobile(c, updateCard, "card")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.PUT("/cards/:accountId/convert/:cardId", func(c echo.Context) error {
		L := logging.L.Named("convert-to-physical-card")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		card := c.Get("card").(*banking.Card)
		if card.IsPhysical || card.IsVirtualConverted {
			L.Error("Card is already physical", zap.Any("card", card))
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, common.ErrorCodeCardBadStatus, "It's already a Physical card")
		}

		response, err := bus2.CallMethodMU[treezor.ConvertVirtualCardToPhysicalResponse](busrpc, treezor.Method_ConvertVirtualCardToPhysical, &treezor.ConvertVirtualCardToPhysicalRequest{
			TreezorUserId: account.TreezorUserId,
			TreezorCardId: card.TreezorCardId,
		}, 10*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		updateCard, err := saveUpdatedActualCard(L, account, response.Card, card)
		if err != nil {
			L.Error("Failed to update card", zap.Error(err), zap.Any("treezorCard", response.Card))
			return err // HTTP 500
		}

		// Return the updated card
		return web2.SingleEntityForMobile(c, updateCard, "card")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.PUT("/cards/:accountId/unblock-pin/:cardId", func(c echo.Context) error {
		L := logging.L.Named("unblock-pin")
		L.Info("Started...")
		defer L.Info("Finished")

		card := c.Get("card").(*banking.Card)
		if card.PinTryExceeds != 1 {
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, common.ErrorCodeCardBadStatus, "Card PIN is not blocked")
		}

		account := c.Get("account").(*accounts.Account)
		response, err := bus2.CallMethodMU[treezor.UnblockPINResponse](busrpc, treezor.Method_UnblockPIN, &treezor.UnblockPINRequest{
			TreezorCardId: card.TreezorCardId,
			TreezorUserId: account.TreezorUserId,
		}, 10*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		updateCard, err := saveUpdatedActualCard(L, account, response.Card, card)
		if err != nil {
			L.Error("Failed to update card", zap.Error(err), zap.Any("treezorCard", response.Card))
			return err // HTTP 500
		}

		// Return the updated card
		return web2.SingleEntityForMobile(c, updateCard, "card")

	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	e.GET("/cards/:accountId/card-image/:cardId", func(c echo.Context) error {
		L := logging.L.Named("get-card-image")

		card := c.Get("card").(*banking.Card)
		account := c.Get("account").(*accounts.Account)
		response, err := bus2.CallMethodMU[treezor.GetCardImageResponse](busrpc, treezor.Method_GetCardImage, &treezor.GetCardImageRequest{
			TreezorCardId: card.TreezorCardId,
			TreezorUserId: account.TreezorUserId,
		}, 10*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		L.Info("Card image", zap.String("mime", response.Mime), zap.Int("size", len(response.Image)))
		return c.Blob(http.StatusOK, response.Mime, response.Image)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveCard, treezor.MiddlewareRequireAndCacheSCAProof)

	// ----------------------------------------------------

	// Special API to salvage cards that were not created from Yochbee
	// - Required when the Account was manually created in the API
	// - Required when the Card was manually created in the API
	e.POST("/debug-cards/:accountId/salvage", func(c echo.Context) error {
		L := logging.L.Named("salvage-card")
		L.Info("Started...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)

		// Get the latest cards of this user from the Treezor microservice
		resp, err := bus2.CallMethodMU[treezor.GetTreezorUserCardsResponse](busrpc, treezor.Method_GetTreezorUserCards, &treezor.GetTreezorUserCardsRequest{
			TreezorUserId: account.TreezorUserId,
		}, 10*time.Second)
		if err != nil {
			L.Error("Failed to get user cards", zap.Error(err))
			return err // HTTP 500
		}

		// Get the current cards (Yochbee side) of this user
		cards, err := cardsColl.GetCardsForTreezorUserId(account.TreezorUserId)
		if err != nil {
			L.Error("Failed to get user cards", zap.Error(err))
			return err // HTTP 500
		}

		// Make a new list of cards that need to be salvaged:
		// - Cards that are not in the `cards` list
		// - Cards whose type (physical/virtual) does not yet exist in the `cards` list
		salvagedCards := make([]*treezor.Card, 0, len(resp.Cards))
		for _, tcard := range resp.Cards {
			found := false
			for _, card := range cards {
				if card.TreezorCardId == tcard.TreezorCardID || card.IsPhysical == (tcard.IsPhysical != 0) {
					L.Info("Card already exists in Banking", zap.Any("card", card), zap.Any("treezorCard", tcard))
					found = true
					break
				}
			}
			if !found {
				salvagedCards = append(salvagedCards, tcard)
			}
		}

		// Insert the Banking Card(s) into the DB.
		for _, tcard := range salvagedCards {
			card := &banking.Card{} // new card
			tcard.SyncToBankingCard(L, card)
			card.ID = cardsColl.GenerateUniqueId()
			card.AccountId = account.ID
			card.CreatedAt = time.Now()
			card.UpdatedAt = &card.CreatedAt
			card.WebhookTimestamp = card.CreatedAt.UnixMilli()
			if _, err = cardsColl.Insert(card); err != nil {
				L.Error("Failed to insert card", zap.Error(err))
				return err // HTTP 500
			}
		}

		// Return the list of new cards that was salvaged
		lany := make([]any, 0, len(salvagedCards))
		for _, tcard := range salvagedCards {
			lany = append(lany, tcard)
		}
		return web2.ListOfMobileJsoner(c, "cards", lany)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)
}
