package main

import (
	"bytes"
	_ "embed"
	"fmt"
	"html/template"
	"strings"
	"time"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/goodsign/monday"
)

func templateFormatDateToFrench(value interface{}, layout string) string {
	return monday.Format(value.(time.Time), layout, monday.LocaleFrFR)
}

func formatFrenchCurrencyValue(num float64) string {
	formatted := fmt.Sprintf("%.2f", num)
	parts := strings.Split(formatted, ".")
	n := parts[0]
	sep := " "
	for i := len(n) - 3; i > 0; i -= 3 {
		n = n[:i] + sep + n[i:]
	}
	return n + "." + parts[1]
}

//go:embed assets/statement-page.html
var statementPageHTML string
var statementPageTemplate *template.Template

func generateStatementPage(account *accounts.Account, wallet *treezor.Wallet, dateStart, dateEnd time.Time, transactions []*banking.Transaction) ([]byte, error) {
	if statementPageTemplate == nil {
		statementPageTemplate = template.Must(template.
			New("relevé-page").
			Funcs(template.FuncMap{
				"formatDateToFrench": templateFormatDateToFrench,
				"formatCurrency":     formatFrenchCurrencyValue,
			}).
			Parse(statementPageHTML))
	}

	// Compute totalIn and totalOut
	var totalIn, totalOut float64
	for _, t := range transactions {
		if t.TreezorWalletCreditId == wallet.TreezorWalletId {
			totalIn += t.Amount
		} else if t.TreezorWalletDebitId == wallet.TreezorWalletId {
			totalOut += t.Amount
		}
	}

	writable := &bytes.Buffer{}
	if err := statementPageTemplate.Execute(writable, map[string]interface{}{
		"iban":            wallet.Iban,
		"bic":             wallet.Bic,
		"fullName":        account.FirstName + " " + account.LastName,
		"address":         account.PhysicalAddress + " " + account.ExtraAddressInfo,
		"zipCode":         account.PostalCode,
		"city":            account.City,
		"country":         account.Country,
		"treezorWalletId": wallet.TreezorWalletId,

		"startDate":    dateStart,
		"endDate":      dateEnd,
		"transactions": transactions,

		"totalIn":       totalIn,
		"totalOut":      totalOut,
		"actualBalance": wallet.Balance,

		"rightNow": time.Now(),
		"css":      template.CSS(styleCSS), // not escaped
	}); err != nil {
		return nil, err
	}
	return writable.Bytes(), nil
}

//go:embed assets/monthly-statement-page.html
var monthlyStatementPageHTML string
var monthlyStatementPageTemplate *template.Template

func generateMonthlyStatementPage(account *accounts.Account, wallet *treezor.Wallet, year int, month time.Month, transactions []*banking.Transaction) ([]byte, error) {
	if monthlyStatementPageTemplate == nil {
		monthlyStatementPageTemplate = template.Must(template.
			New("relevé-page").
			Funcs(template.FuncMap{
				"formatDateToFrench": templateFormatDateToFrench,
				"formatCurrency":     formatFrenchCurrencyValue,
			}).
			Parse(monthlyStatementPageHTML))
	}

	// Compute totalIn and totalOut
	var totalIn, totalOut float64
	for _, t := range transactions {
		if t.TreezorWalletCreditId == wallet.TreezorWalletId {
			totalIn += t.Amount
		} else if t.TreezorWalletDebitId == wallet.TreezorWalletId {
			totalOut += t.Amount
		}
	}

	// Map the month to a french string, such as "janvier"
	monthStr := monday.Format(time.Date(year, month, 1, 0, 0, 0, 0, time.UTC), "January", monday.LocaleFrFR)

	writable := &bytes.Buffer{}
	data := map[string]interface{}{
		"iban":            wallet.Iban,
		"bic":             wallet.Bic,
		"fullName":        account.FirstName + " " + account.LastName,
		"address":         account.PhysicalAddress + " " + account.ExtraAddressInfo,
		"zipCode":         account.PostalCode,
		"city":            account.City,
		"country":         account.Country,
		"treezorWalletId": wallet.TreezorWalletId,

		"month":        monthStr,
		"transactions": transactions,

		"totalIn":       totalIn,
		"totalOut":      totalOut,
		"actualBalance": wallet.Balance,

		"rightNow": time.Now(),
		"css":      template.CSS(styleCSS), // not escaped
	}
	if err := monthlyStatementPageTemplate.Execute(writable, data); err != nil {
		return nil, err
	}
	return writable.Bytes(), nil
}

//go:embed assets/rib-page.html
var ribPageHTML string
var ribPageTemplate *template.Template

func generateRibPage(account *accounts.Account, wallet *treezor.Wallet) ([]byte, error) {
	if ribPageTemplate == nil {
		ribPageTemplate = template.Must(template.New("rib-page").Parse(ribPageHTML))
	}
	writable := &bytes.Buffer{}
	if err := ribPageTemplate.Execute(writable, map[string]interface{}{
		"iban":     wallet.Iban,
		"bic":      wallet.Bic,
		"fullName": account.FirstName + " " + account.LastName,
		"address":  account.PhysicalAddress + " " + account.ExtraAddressInfo,
		"zipCode":  account.PostalCode,
		"city":     account.City,
		"country":  account.Country,

		"css": template.CSS(styleCSS), // not escaped
	}); err != nil {
		return nil, err
	}

	return writable.Bytes(), nil
}

//go:embed assets/style.css
var styleCSS string
