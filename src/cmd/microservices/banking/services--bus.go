package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/common/banking"
)

func registerBusMethods(rpc bus2.RpcClient, cardsColl *CardsCollection) error {
	//
	// ╔═╗┌─┐┌┬┐  ╔═╗╔═╗╦═╗╔╦╗  ┌┐ ┬ ┬  ╔╦╗┬─┐┌─┐┌─┐┌─┐┌─┐┬─┐  ╦╔╦╗
	// ║ ╦├┤  │   ║  ╠═╣╠╦╝ ║║  ├┴┐└┬┘   ║ ├┬┘├┤ ├┤ ┌─┘│ │├┬┘  ║ ║║
	// ╚═╝└─┘ ┴   ╚═╝╩ ╩╩╚══╩╝  └─┘ ┴    ╩ ┴└─└─┘└─┘└─┘└─┘┴└─  ╩═╩╝
	if err := bus2.RegisterUM(banking.Method_GetCardByTreezorID, rpc, func(request *banking.GetCardByTreezorIDRequest) (*banking.GetCardByTreezorIDResponse, error) {
		card, err := cardsColl.GetByTreezorCardId(request.TreezorCardID)
		if err != nil {
			return nil, err
		}

		return &banking.GetCardByTreezorIDResponse{
			Card: card,
		}, nil
	}, true); err != nil {
		return err
	}

	return nil
}
