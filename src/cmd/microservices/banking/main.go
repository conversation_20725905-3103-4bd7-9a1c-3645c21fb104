package main

import (
	"yochbee/_base"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"
	"yochbee/common/accounts"
	"yochbee/common/banking"

	"github.com/labstack/echo/v4"
	goredis "github.com/redis/go-redis/v9"
)

func registerAllServices(busrpc bus2.RpcClient, signaling bus2.Client, e *echo.Echo, db dbnosql.Database, cache *goredis.Client, acquiringTokensCollection *banking.AcquiringTokensCollection, transactionsColl *banking.TransactionsCollection, cardsColl *CardsCollection, beneficiariesCollection *BeneficiariesCollection) error {
	// ---------------------------------
	registerBeneficiaryRESTAPIs(busrpc, e, beneficiariesCollection)

	// ---------------------------------
	registerWalletRESTAPIs(busrpc, e)

	// ---------------------------------
	registerPayoutRESTAPIs(busrpc, e, beneficiariesCollection)

	// ---------------------------------
	registerTransactionsRESTAPIs(busrpc, e, cardsColl)

	// ---------------------------------
	registerCardsRESTApis(busrpc, e, cardsColl)

	// ---------------------------------
	registerAcquiringRESTApis(busrpc, e, acquiringTokensCollection)

	// ---------------------------------
	registerMandateRESTAPIs(busrpc, e)

	// ---------------------------------
	registerPayinRESTAPIs(busrpc, e)

	// ---------------------------------
	if err := registerReportsAPIs(busrpc, e); err != nil {
		return err
	}

	// ---------------------------------
	if err := registerTreezorReportsAPIs(busrpc, e); err != nil {
		return err
	}

	// ---------------------------------
	registerCardTransactionsURLsRESTAPIs(busrpc, e)

	// ---------------------------------
	if err := registerSubscriptions(signaling, busrpc, transactionsColl, cardsColl, beneficiariesCollection); err != nil {
		return err
	}

	e.POST("/_bus/call-method/:methodName", bus2.RegisterBusMethodCallingFromREST(busrpc))

	return nil
}

func main() {
	config.Microservice = "banking"
	web.ServerEndpoint = ":8086"

	app := _base.SetupAppWithCustomProvidersAndInvokes("Banking",
		[]interface{}{
			setupCardsCollection,
			setupBeneficiariesCollection,
		},
		[]interface{}{
			banking.SetupBeneficiaryRequirements,
			accounts.ConfigureJWT,
			registerAllServices,
			registerBusMethods,
		},
	)

	app.Run()
}
