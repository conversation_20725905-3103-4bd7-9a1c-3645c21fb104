package main

import (
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

const (
	MaxReceivedAmount = 10000
)

func subscribeToTreezorPayins(signaling bus2.Client, transactionsColl *banking.TransactionsCollection) error {
	refundsQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-payins", true, false,
		[]string{
			"treezor.payin.create",
			"treezor.payin.update",
			"treezor.payin.cancel",
		})
	if err != nil {
		return err
	}

	// --------------------
	if err = signaling.SubscribeToEventForever(refundsQueue, "banking/subscriptions--payins.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.payin.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		tpayins, err := treezor.UnwrapPayinsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Payins from Treezor webhook", zap.Error(err))
			return
		}

		for _, tpayin := range tpayins {
			// Transfer the timestamp from the incoming Webhook
			tpayin.WebhookTimestamp = event.WebhookTimestamp

			// Get the matching Transaction
			transaction, err := transactionsColl.GetByTreezorPayinId(tpayin.TreezorPayinID)
			if err != nil {
				L.Error("Error while getting Transaction from DB", zap.Error(err))
				continue
			}
			if transaction == nil {
				L.Warn("No matching transaction found (yet) for this Payin", zap.Any("treezorPayin", tpayin))
				continue
			}

			// Check if the amount of the received fund is too high, send an  alert
			// See also: https://docs.treezor.com/guide/api-basics/payin-payout.html#payin-status-codestatus
			if tpayin.Amount > MaxReceivedAmount && tpayin.CodeStatus == 140005 {
				// TODO: send an alert to the Yochbee team
				L.Warn("ALERT: 🚨 Too much fund is being received on Wallet {treezorWalletId}, amount = €{amount} 🚨",
					zap.Int64("treezorWalletId", tpayin.TreezorWalletID),
					zap.Int64("treezorUserId", tpayin.TreezorUserID),
					zap.Any("treezorPayinId", tpayin.TreezorPayinID),
					zap.Float64("amount", tpayin.Amount))
			}

			// Update/sync the Transaction with the Payin, but only if the Payin is newer
			if tpayin.EnrichBankingTransaction(transaction) {
				transaction.UpdatedAt = dbnosql.PNow() // this is an update
				if err := transactionsColl.Update(transaction); err != nil {
					L.Error("Error while updating Transaction", zap.Error(err), zap.String("transactionId", transaction.ID), zap.Any("treezorPayin", tpayin), zap.Any("transaction", transaction))
					continue
				}
			}
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}

	return nil
}
