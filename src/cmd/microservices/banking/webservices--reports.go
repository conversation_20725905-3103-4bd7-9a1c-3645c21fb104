package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/utils"
	"yochbee/_base/web"
	web2 "yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

func registerReportsAPIs(busrpc bus2.RpcClient, e *echo.Echo) error {
	// Add common dependencies into the context
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("busrpc", busrpc)
			return next(c)
		}
	})

	// Prepare the endpoint where the PDF generator service is listening
	currentHost, err := os.Hostname()
	if err != nil {
		return err
	}
	pdfGeneratorUrl := "http://pdf-generator-service:8080/converter/html-2-pdf"
	if !config.IsRunningInsideKubernetes() {
		if currentHost == "Elies-MacBook-Pro.local" {
			pdfGeneratorUrl = "http://yochbee-backend:8080/converter/html-2-pdf"
		} else if strings.HasSuffix(currentHost, ".gitpod.io") {
			pdfGeneratorUrl = "http://localhost:8080/converter/html-2-pdf"
		}
	}

	enrichTransactionsWithPayins := func(L *zap.Logger, transactions []*banking.Transaction, busrpc bus2.RpcClient) ([]*banking.Transaction, error) {
		// Get the associated Payin for each transaction (if applicable), via Treezor microservice
		payinIds := make([]interface{}, 0, len(transactions))
		for _, v := range transactions {
			if v.TreezorPayinId == nil {
				continue
			}
			payinIds = append(payinIds, v.TreezorPayinId)
		}
		payinsResponse, err := treezor.GetMultiplePayinsByTreezorIds(busrpc, payinIds)
		if err != nil {
			L.Error("Error getting multiple Payins by Treezor IDs", zap.Error(err), zap.Any("payinIds", payinIds))
			return nil, err // HTTP 500
		}

		// Sort by transaction.createdAt
		transactions = banking.SortTransactionsByCreatedAt(transactions)

		// Update the transactions with the associated Payin
		for i, v := range transactions {
			payin, ok := payinsResponse.Payins[utils.InterfaceToString(v.TreezorPayinId)]
			if ok {
				transactions[i].Name = payin.MessageToUser
			}
		}

		return transactions, nil
	}

	//
	// ╦═╗╦╔╗   ┌┬┐┌─┐┌─┐┬ ┬┌┬┐┌─┐┌┐┌┌┬┐
	// ╠╦╝║╠╩╗   │││ ││  │ ││││├┤ │││ │
	// ╩╚═╩╚═╝  ─┴┘└─┘└─┘└─┘┴ ┴└─┘┘└┘ ┴
	//
	e.GET("/html/rib/:accountId", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)

		// Get the Treezor wallet (via bus)
		response, err := bus2.CallMethodMU[treezor.GetWalletByTreezorIdResponse](busrpc, treezor.Method_GetWalletByTreezorId, &treezor.GetWalletByTreezorIdRequest{
			TreezorWalletId: account.TreezorPrimaryWalletId,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}
		if response.TreezorWallet == nil {
			return web.ErrorWithStatusAndCode(c, http.StatusNotFound, common.ErrorCodeWalletNotFound, "Wallet not found")
		}

		html, err := generateRibPage(account, response.TreezorWallet)
		if err != nil {
			return err // HTTP 500
		}
		return c.HTMLBlob(http.StatusOK, html)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	e.GET("/pdf/rib/:accountId", func(c echo.Context) error {
		// Send a GET request to /html/rib/:accountId with the same JWT token that was used for this request.
		endpoint := fmt.Sprintf("http://localhost%s/html/rib/%s", web.ServerEndpoint, c.Param("accountId"))
		request, _ := http.NewRequest(http.MethodGet, endpoint, nil)
		request.Header.Set("Authorization", c.Request().Header.Get("Authorization"))
		response, err := http.DefaultClient.Do(request)
		if err != nil {
			return err // HTTP 500
		}
		defer response.Body.Close()
		if response.StatusCode != http.StatusOK {
			return err // HTTP 500
		}

		// Get the whole HTML page
		html, err := io.ReadAll(response.Body)
		if err != nil {
			return err // HTTP 500
		}

		// Prepare the JSON payload to send to the PDF generator service
		payload := map[string]interface{}{
			"content": string(html),
			"params": map[string]interface{}{
				"screenMediaType": true,
				"format":          "A5",
				"landscape":       false,
				"printBackground": true,
				"timeout":         5000,
			},
		}
		jpayload, err := json.Marshal(payload)
		if err != nil {
			return err // HTTP 500
		}

		// Send the JSON payload to the PDF generator service
		request, _ = http.NewRequest(http.MethodPost, pdfGeneratorUrl, bytes.NewReader(jpayload))
		request.Header.Set("Content-Type", "application/json")
		response, err = http.DefaultClient.Do(request)
		if err != nil {
			return err // HTTP 500
		}
		defer response.Body.Close()
		if response.StatusCode != http.StatusOK {
			return web.ErrorWithStatusAndCode(c, http.StatusInternalServerError, common.ErrorCodeServiceFailure, "Service failure")
		}

		// Stream the PDF out
		return c.Stream(http.StatusOK, response.Header.Get("Content-Type"), response.Body)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	//
	// ╦═╗╔═╗╦  ╔═╗╦  ╦╔═╗    ╔═╗╔╦╗╔═╗╔╦╗╔═╗╔╦╗╔═╗╔╗╔╔╦╗  ┌┬┐┌─┐┌─┐┬ ┬┌┬┐┌─┐┌┐┌┌┬┐
	// ╠╦╝║╣ ║  ║╣ ╚╗╔╝║╣     ╚═╗ ║ ╠═╣ ║ ║╣ ║║║║╣ ║║║ ║    │││ ││  │ ││││├┤ │││ │
	// ╩╚═╚═╝╩═╝╚═╝ ╚╝ ╚═╝    ╚═╝ ╩ ╩ ╩ ╩ ╚═╝╩ ╩╚═╝╝╚╝ ╩   ─┴┘└─┘└─┘└─┘┴ ┴└─┘┘└┘ ┴
	//
	e.GET("/html/statement/:accountId", func(c echo.Context) error {
		L := logging.L.Named("html/statement/:accountId")
		L.Info("Started ...")
		defer L.Info("Finished")

		// Parse the request parameters
		startDate, endDate, err, aborted := web.GetStartEndDates(c)
		if err != nil || aborted {
			L.Error("Invalid request on `startDate` and `endDate`", zap.Error(err))
			return err
		}
		if startDate == nil || endDate == nil {
			L.Error("Invalid request on `startDate` or `endDate`", zap.Timep("startDate", startDate), zap.Timep("endDate", endDate))
			return common.ReturnInvalidAPICallResponse(c)
		}

		account := c.Get("account").(*accounts.Account)
		transactionsResponse, err := treezor.GetFreshTransactions(busrpc, account.TreezorUserId, account.TreezorPrimaryWalletId, *startDate, *endDate)
		if err != nil {
			if err.Error() == treezor.ErrorNoMoreScaSessionJWT.Error() { // we do a string comparison because IPC
				return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCASessionExpired, "SCA session expired")
			}

			L.Error("Error getting fresh transactions from Treezor", zap.Error(err))
			return err // HTTP 500
		}
		transactions, err := enrichTransactionsWithPayins(L, transactionsResponse.Transactions, busrpc)
		if err != nil {
			return err
		}

		wallet := c.Get("wallet").(*treezor.Wallet)
		html, err := generateStatementPage(account, wallet, *startDate, *endDate, transactions)
		if err != nil {
			return err
		}
		return c.HTMLBlob(http.StatusOK, html)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveTreezorWallet, treezor.MiddlewareConditionalSCAProofForDates)

	e.GET("/html/monthly-statement/:accountId/:year/:month", func(c echo.Context) error {
		L := logging.L.Named("html/monthly-statement/:accountId/:year/:month")
		L.Info("Started ...")
		defer L.Info("Finished")

		// Parse the request parameters and convert to numerical values
		yearStr := c.Param("year")
		monthStr := c.Param("month")
		year, err := strconv.Atoi(yearStr)
		if err != nil {
			L.Error("Invalid request on `year`", zap.String("year", yearStr), zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		month, err := strconv.Atoi(monthStr)
		if err != nil {
			L.Error("Invalid request on `month`", zap.String("month", monthStr), zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		if year < 2000 || year > 2100 || month < 1 || month > 12 {
			L.Error("Invalid request on `year` or `month`", zap.Int("year", year), zap.Int("month", month))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Get the start and end dates for the month
		startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
		endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

		account := c.Get("account").(*accounts.Account)
		transactionsResponse, err := treezor.GetFreshTransactions(busrpc, account.TreezorUserId, account.TreezorPrimaryWalletId, startDate, endDate)
		//transactionsResponse, err := treezor.GetFreshTransactions(busrpc, 0, account.TreezorPrimaryWalletId, startDate, endDate)
		if err != nil {
			if err.Error() == treezor.ErrorNoMoreScaSessionJWT.Error() { // we do a string comparison because IPC
				return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCASessionExpired, "SCA session expired")
			}

			L.Error("Error getting fresh transactions from Treezor", zap.Error(err))
			return err // HTTP 500
		}
		transactions, err := enrichTransactionsWithPayins(L, transactionsResponse.Transactions, busrpc)
		if err != nil {
			return err
		}

		// Sort by transaction.createdAt
		transactions = banking.SortTransactionsByCreatedAt(transactions)

		wallet := c.Get("wallet").(*treezor.Wallet)
		html, err := generateMonthlyStatementPage(account, wallet, year, time.Month(month), transactions)
		if err != nil {
			return err
		}
		return c.HTMLBlob(http.StatusOK, html)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveTreezorWallet, treezor.MiddlewareConditionalSCAProofForDates)

	e.GET("/pdf/statement/:accountId", func(c echo.Context) error {
		// Send a GET request to /html/statement/:accountId with the same JWT token that was used for this request.
		endpoint := fmt.Sprintf("http://localhost%s/html/statement/%s", web.ServerEndpoint, c.Param("accountId"))
		request, _ := http.NewRequest(http.MethodGet, endpoint, nil)
		request.Header.Set("Authorization", c.Request().Header.Get("Authorization"))
		// Also pass the GET parameters
		request.URL.RawQuery = c.Request().URL.RawQuery
		response, err := http.DefaultClient.Do(request)
		if err != nil {
			return err // HTTP 500
		}
		defer response.Body.Close()
		if response.StatusCode != http.StatusOK {
			// If the error corresponds to the "SCA session expired" error, we simply return the same error
			// The response needs to be checked for the exact error code
			data, err := io.ReadAll(response.Body)
			if err != nil {
				return err // HTTP 500
			}
			erroCode := gjson.GetBytes(data, "code").Int()
			if erroCode == common.ErrorCodeSCASessionExpired {
				return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, int(erroCode), "SCA session expired")
			}
		}

		// Get the whole HTML page
		html, err := io.ReadAll(response.Body)
		if err != nil {
			return err // HTTP 500
		}

		// Prepare the JSON payload to send to the PDF generator service
		payload := map[string]interface{}{
			"content": string(html),
			"params": map[string]interface{}{
				"screenMediaType": true,
				"format":          "A4",
				"landscape":       false,
				"printBackground": true,
				"timeout":         5000,
			},
		}
		jpayload, err := json.Marshal(payload)
		if err != nil {
			return err // HTTP 500
		}

		// Send the JSON payload to the PDF generator service
		request, _ = http.NewRequest(http.MethodPost, pdfGeneratorUrl, bytes.NewReader(jpayload))
		request.Header.Set("Content-Type", "application/json")
		response, err = http.DefaultClient.Do(request)
		if err != nil {
			return err // HTTP 500
		}
		defer response.Body.Close()
		if response.StatusCode != http.StatusOK {
			return web.ErrorWithStatusAndCode(c, http.StatusInternalServerError, common.ErrorCodeServiceFailure, "Service failure")
		}

		// Stream the PDF out
		return c.Stream(http.StatusOK, response.Header.Get("Content-Type"), response.Body)
	})

	e.GET("/pdf/monthly-statement/:accountId/:year/:month", func(c echo.Context) error {
		// Send a GET request to /html/monthly-statement/:accountId/:year/:month with the same JWT token that was used for this request.
		endpoint := fmt.Sprintf("http://localhost%s/html/monthly-statement/%s/%s/%s", web.ServerEndpoint, c.Param("accountId"), c.Param("year"), c.Param("month"))
		request, _ := http.NewRequest(http.MethodGet, endpoint, nil)
		request.Header.Set("Authorization", c.Request().Header.Get("Authorization"))
		// Also pass the GET parameters
		request.URL.RawQuery = c.Request().URL.RawQuery
		response, err := http.DefaultClient.Do(request)
		if err != nil {
			return err // HTTP 500
		}
		defer response.Body.Close()
		if response.StatusCode != http.StatusOK {
			// If the error corresponds to the "SCA session expired" error, we simply return the same error
			// The response needs to be checked for the exact error code
			data, err := io.ReadAll(response.Body)
			if err != nil {
				return err // HTTP 500
			}
			erroCode := gjson.GetBytes(data, "code").Int()
			if erroCode == common.ErrorCodeSCASessionExpired {
				return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, int(erroCode), "SCA session expired")
			}
		}

		// Get the whole HTML page
		html, err := io.ReadAll(response.Body)
		if err != nil {
			return err // HTTP 500
		}

		// Prepare the JSON payload to send to the PDF generator service
		payload := map[string]interface{}{
			"content": string(html),
			"params": map[string]interface{}{
				"screenMediaType": true,
				"format":          "A4",
				"landscape":       false,
				"printBackground": true,
				"timeout":         5000,
			},
		}
		jpayload, err := json.Marshal(payload)
		if err != nil {
			return err // HTTP 500
		}

		// Send the JSON payload to the PDF generator service
		request, _ = http.NewRequest(http.MethodPost, pdfGeneratorUrl, bytes.NewReader(jpayload))
		request.Header.Set("Content-Type", "application/json")
		response, err = http.DefaultClient.Do(request)
		if err != nil {
			return err // HTTP 500
		}
		defer response.Body.Close()
		if response.StatusCode != http.StatusOK {
			return web.ErrorWithStatusAndCode(c, http.StatusInternalServerError, common.ErrorCodeServiceFailure, "Service failure")
		}

		// Stream the PDF out
		return c.Stream(http.StatusOK, response.Header.Get("Content-Type"), response.Body)
	})

	return nil
}
