package main

import (
	"fmt"
	"strings"
	"testing"
	"time"
	"yochbee/_base/utils"
	"yochbee/_base/web"
)

func TestCardTransactionsURLGeneration(t *testing.T) {
	tests := []struct {
		name         string
		cardId       int64
		dateFrom     string
		dateTo       string
		expectedPath string
	}{
		{
			name:         "Simple date conversion test",
			cardId:       12345,
			dateFrom:     "2025-07-21",
			dateTo:       "2025-07-21",
			expectedPath: "/core-connect/cardtransactions/12345",
		},
		{
			name:         "Date range test",
			cardId:       67890,
			dateFrom:     "2025-07-01",
			dateTo:       "2025-07-31",
			expectedPath: "/core-connect/cardtransactions/67890",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Load Paris location for proper timezone handling with daylight saving
			parisLoc, err := time.LoadLocation("Europe/Paris")
			if err != nil {
				t.Fatalf("Failed to load Europe/Paris location: %v", err)
			}

			// Parse the input dates in YYYY-MM-DD format
			dateFromParsed, err := time.Parse("2006-01-02", tt.dateFrom)
			if err != nil {
				t.Fatalf("Failed to parse dateFrom: %v", err)
			}

			dateToParsed, err := time.Parse("2006-01-02", tt.dateTo)
			if err != nil {
				t.Fatalf("Failed to parse dateTo: %v", err)
			}

			// Convert dates to Paris timezone (start of day for dateFrom, end of day for dateTo)
			// This ensures we capture the full day in Paris timezone
			dateFromParis := time.Date(dateFromParsed.Year(), dateFromParsed.Month(), dateFromParsed.Day(), 0, 0, 0, 0, parisLoc)
			dateToParis := time.Date(dateToParsed.Year(), dateToParsed.Month(), dateToParsed.Day(), 23, 59, 59, 999999999, parisLoc)

			// Build the URL path for card transactions
			path := fmt.Sprintf("/core-connect/cardtransactions/%d", tt.cardId)

			// Add createdDateFrom query parameter
			createdDateFromString := dateFromParis.Format(utils.DateTimeFormat2)
			path, err = web.AddQueryParamToUrl(path, "createdDateFrom", createdDateFromString)
			if err != nil {
				t.Fatalf("Failed to add createdDateFrom query param: %v", err)
			}

			// Add createdDateTo query parameter
			createdDateToString := dateToParis.Format(utils.DateTimeFormat2)
			path, err = web.AddQueryParamToUrl(path, "createdDateTo", createdDateToString)
			if err != nil {
				t.Fatalf("Failed to add createdDateTo query param: %v", err)
			}

			// Verify the base path is correct
			if !strings.Contains(path, tt.expectedPath) {
				t.Errorf("URL should contain the expected base path %s, got %s", tt.expectedPath, path)
			}

			// Verify query parameters are present
			if !strings.Contains(path, "createdDateFrom=") {
				t.Errorf("URL should contain createdDateFrom parameter")
			}
			if !strings.Contains(path, "createdDateTo=") {
				t.Errorf("URL should contain createdDateTo parameter")
			}

			// Print the results for manual verification
			t.Logf("🗓️  Input Date From: %s", tt.dateFrom)
			t.Logf("🗓️  Input Date To: %s", tt.dateTo)
			t.Logf("🇫🇷 Paris Date From: %s", dateFromParis.Format("2006-01-02 15:04:05 MST"))
			t.Logf("🇫🇷 Paris Date To: %s", dateToParis.Format("2006-01-02 15:04:05 MST"))
			t.Logf("📅 Formatted Date From: %s", createdDateFromString)
			t.Logf("📅 Formatted Date To: %s", createdDateToString)
			t.Logf("🔗 Generated Treezor URL: %s", path)
		})
	}
}

func TestSpecificDateConversion_2025_07_21(t *testing.T) {
	// Test the specific date mentioned by the user: 2025-07-21
	dateInput := "2025-07-21"
	cardId := int64(12345)

	t.Logf("🎯 Testing specific date conversion for: %s", dateInput)

	// Load Paris timezone
	parisLoc, err := time.LoadLocation("Europe/Paris")
	if err != nil {
		t.Fatalf("Failed to load Europe/Paris location: %v", err)
	}

	// Parse the input date
	dateParsed, err := time.Parse("2006-01-02", dateInput)
	if err != nil {
		t.Fatalf("Failed to parse input date: %v", err)
	}

	// Convert to Paris timezone (full day range)
	dateFromParis := time.Date(dateParsed.Year(), dateParsed.Month(), dateParsed.Day(), 0, 0, 0, 0, parisLoc)
	dateToParis := time.Date(dateParsed.Year(), dateParsed.Month(), dateParsed.Day(), 23, 59, 59, 999999999, parisLoc)

	// Format for Treezor API
	createdDateFromString := dateFromParis.Format(utils.DateTimeFormat2)
	createdDateToString := dateToParis.Format(utils.DateTimeFormat2)

	// Build the complete URL
	path := fmt.Sprintf("/core-connect/cardtransactions/%d", cardId)
	path, err = web.AddQueryParamToUrl(path, "createdDateFrom", createdDateFromString)
	if err != nil {
		t.Fatalf("Failed to add createdDateFrom: %v", err)
	}
	path, err = web.AddQueryParamToUrl(path, "createdDateTo", createdDateToString)
	if err != nil {
		t.Fatalf("Failed to add createdDateTo: %v", err)
	}

	// Assertions
	if dateInput != "2025-07-21" {
		t.Errorf("Input date should be preserved, got %s", dateInput)
	}
	if dateFromParis.Year() != 2025 {
		t.Errorf("Year should be 2025, got %d", dateFromParis.Year())
	}
	if dateFromParis.Month() != time.July {
		t.Errorf("Month should be July, got %s", dateFromParis.Month())
	}
	if dateFromParis.Day() != 21 {
		t.Errorf("Day should be 21, got %d", dateFromParis.Day())
	}
	if dateFromParis.Hour() != 0 {
		t.Errorf("Start of day should be 00:00, got %d", dateFromParis.Hour())
	}
	if dateToParis.Hour() != 23 {
		t.Errorf("End of day should be 23:59, got %d", dateToParis.Hour())
	}

	// Check timezone offset for July 2025 (should be +02:00 for CEST - Central European Summer Time)
	_, offset := dateFromParis.Zone()
	expectedOffset := 2 * 60 * 60 // +02:00 in seconds
	if offset != expectedOffset {
		t.Errorf("Should be in CEST (+02:00) timezone for July, got UTC%+d", offset/3600)
	}

	// Output detailed results
	separator := strings.Repeat("=", 70)
	t.Logf(separator)
	t.Logf("🧪 DETAILED TEST RESULTS FOR DATE: %s", dateInput)
	t.Logf(separator)
	t.Logf("📥 Input:")
	t.Logf("   • Date: %s", dateInput)
	t.Logf("   • Card ID: %d", cardId)
	t.Logf("")
	t.Logf("🇫🇷 Paris Timezone Conversion:")
	t.Logf("   • From: %s", dateFromParis.Format("2006-01-02 15:04:05.999999999 MST (UTC-07:00)"))
	t.Logf("   • To:   %s", dateToParis.Format("2006-01-02 15:04:05.999999999 MST (UTC-07:00)"))
	t.Logf("   • Timezone Offset: %+d hours", offset/3600)
	t.Logf("")
	t.Logf("📅 Treezor API Format:")
	t.Logf("   • createdDateFrom: %s", createdDateFromString)
	t.Logf("   • createdDateTo:   %s", createdDateToString)
	t.Logf("")
	t.Logf("🔗 Final Treezor API URL:")
	t.Logf("   %s", path)
	t.Logf(separator)

	// Verify the URL contains expected components
	if !strings.Contains(path, fmt.Sprintf("/core-connect/cardtransactions/%d", cardId)) {
		t.Errorf("URL should contain card transactions path")
	}
	if !strings.Contains(path, "createdDateFrom=") {
		t.Errorf("URL should contain createdDateFrom parameter")
	}
	if !strings.Contains(path, "createdDateTo=") {
		t.Errorf("URL should contain createdDateTo parameter")
	}
	if !strings.Contains(path, "2025-07-21") {
		t.Errorf("URL should contain the date 2025-07-21")
	}
}

func TestTimezoneHandling(t *testing.T) {
	// Test different seasons to verify DST handling
	testCases := []struct {
		date           string
		expectedOffset int // in hours
		season         string
	}{
		{"2025-01-15", 1, "Winter (CET)"},  // Central European Time
		{"2025-07-21", 2, "Summer (CEST)"}, // Central European Summer Time
		{"2025-12-25", 1, "Winter (CET)"},  // Central European Time
		{"2025-06-15", 2, "Summer (CEST)"}, // Central European Summer Time
	}

	parisLoc, err := time.LoadLocation("Europe/Paris")
	if err != nil {
		t.Fatalf("Failed to load Europe/Paris location: %v", err)
	}

	t.Logf("🌍 TIMEZONE HANDLING TEST")
	t.Logf(strings.Repeat("=", 50))

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Date_%s_%s", tc.date, tc.season), func(t *testing.T) {
			dateParsed, err := time.Parse("2006-01-02", tc.date)
			if err != nil {
				t.Fatalf("Failed to parse date: %v", err)
			}

			dateInParis := time.Date(dateParsed.Year(), dateParsed.Month(), dateParsed.Day(), 12, 0, 0, 0, parisLoc)
			_, offset := dateInParis.Zone()
			offsetHours := offset / 3600

			if offsetHours != tc.expectedOffset {
				t.Errorf("Expected UTC%+d for %s, got UTC%+d", tc.expectedOffset, tc.season, offsetHours)
			}

			t.Logf("📅 %s -> %s (UTC%+d) ✅", tc.date, tc.season, offsetHours)
		})
	}
}
