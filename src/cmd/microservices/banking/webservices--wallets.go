package main

import (
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/labstack/echo/v4"
)

func registerWalletRESTAPIs(busrpc bus2.RpcClient, e *echo.Echo) {
	e.GET("/wallet/:accountId/bank-info", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]interface{}{
			"bank": map[string]interface{}{
				"name":          "Treezor",
				"domiciliation": "33 avenue de Wagram — 75017 Paris",
			},
		})
	})

	e.GET("/wallet/:accountId", func(c echo.Context) error {
		account := c.Get("account").(*accounts.Account)

		// Get the latest Treezor Wallet by the User ID, via the bus.
		// NOTE: There is 1 <-> 1 relationship for a User and a Wallet.
		response, err := bus2.CallMethodMU[treezor.GetWalletByUserIdResponse](busrpc, treezor.Method_GetWalletByUserId, &treezor.GetWalletByUserIdRequest{
			TreezorUserId: account.TreezorUserId,
			WalletType:    treezor.WalletTypePaymentAccountWallet,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		if response.TreezorWallet == nil {
			return web.ErrorWithStatusAndCode(c, http.StatusNotFound, common.ErrorCodeWalletNotFound, "Wallet not found")
		}

		return web.SingleEntityForMobile(c, response.TreezorWallet, "wallet")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)
}
