package main

import (
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToTransactionEvents(signaling bus2.Client, busrpc bus2.RpcClient, transactionsColl *banking.TransactionsCollection) error {
	transactionsQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-transactions", true, false,
		[]string{
			"treezor.transaction.create",
		})
	if err != nil {
		return err
	}

	if err = signaling.SubscribeToEventForever(transactionsQueue, "banking/subscriptions--transactions.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.transaction.*)").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		transactions, err := treezor.UnwrapTransactionsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Transactions from webhook event", zap.Error(err))
			return
		}

		for _, ttrasaction := range transactions {
			// Transfer the WebhookTimestamp to the Treezor Transaction
			ttrasaction.WebhookTimestamp = event.WebhookTimestamp

			var (
				isNew              bool = true
				bankingTransaction *banking.Transaction
			)

			// Check if a corresponding Banking transaction already exists
			if bankingTransaction, err = transactionsColl.GetOneByTreezorID(ttrasaction.TreezorId); err != nil {
				L.Error("Error while checking if Banking Transaction already exists", zap.Error(err))
				continue
			}
			if bankingTransaction != nil {
				if bankingTransaction.WebhookTimestamp > ttrasaction.WebhookTimestamp {
					L.Info("No need to create a new Banking Transaction: it already exists and is newer", zap.Any("ttrasaction", ttrasaction))
					continue
				}
				isNew = false
			} else {
				if ttrasaction.TransactionType == "Card transaction" {
					// Find the matching Card Transaction, probably our own version, by the TreezorCardTransactionId
					bankingTransaction, err = transactionsColl.GetByTreezorCardTransactionId(ttrasaction.TreezorForeignId)
					if err != nil {
						L.Error("Error while checking if Banking Transaction, based on the TreezorCardTransactionId, already exists", zap.Error(err))
						continue
					}
					if bankingTransaction != nil {
						L.Info("Found a matching Banking Transaction, based on the TreezorCardTransactionId", zap.Any("bankingTransaction", bankingTransaction), zap.Any("ttrasaction", ttrasaction))
					}
				}

				if bankingTransaction == nil {
					bankingTransaction = ttrasaction.MapToNewBankingTransaction()
					bankingTransaction.ID = transactionsColl.GenerateUniqueId()
					if bankingTransaction.CreatedAt.IsZero() {
						L.Warn("CreatedAt is zero, setting it to now", zap.Any("bankingTransaction", bankingTransaction), zap.Any("ttrasaction", ttrasaction))
						bankingTransaction.CreatedAt = time.Now()
					}
					L.Info("Creating new Banking Transaction", zap.Any("bankingTransaction", bankingTransaction), zap.Any("ttrasaction", ttrasaction))
				}
			}

			switch ttrasaction.TransactionType {
			case "Payin":
				// Check if there is a Payin matching this Transaction
				response, err := bus2.CallMethodMU[treezor.GetPayinByTreezorIDResponse](busrpc, treezor.Method_GetPayinByTreezorID, &treezor.GetPayinByTreezorIDRequest{TreezorID: ttrasaction.TreezorForeignId}, 5*time.Second)
				if err != nil {
					L.Error("Error while checking related Payin from Treezor", zap.Error(err))
					continue
				}
				payin := response.Payin
				if payin != nil {
					// NOTE: the UpdateAt field will not be set here because it's a new document
					payin.EnrichBankingTransaction(bankingTransaction)
				}

			case "Payout":
				// Check if there is a Payout matching this Transaction
				response, err := bus2.CallMethodMU[treezor.GetPayoutByTreezorIDResponse](busrpc, treezor.Method_GetPayoutByTreezorID, &treezor.GetPayoutByTreezorIDRequest{TreezorID: ttrasaction.TreezorForeignId}, 5*time.Second)
				if err != nil {
					L.Error("Error while checking related Payout from Treezor", zap.Error(err))
					continue
				}
				payout := response.Payout
				if payout != nil {
					// NOTE: the UpdateAt field will not be set here because it's a new document
					payout.EnrichBankingTransaction(bankingTransaction)
				}

			case "Card transaction":
				// Check if there is a CardTransaction matching this Transaction
				response, err := bus2.CallMethodMU[treezor.GetCardTransactionByTreezorIDResponse](busrpc, treezor.Method_GetCardTransactionByTreezorID, &treezor.GetCardTransactionByTreezorIDRequest{TreezorID: ttrasaction.TreezorForeignId}, 5*time.Second)
				if err != nil {
					L.Error("Error while checking related CardTransaction from Treezor", zap.Error(err))
					continue
				}
				cardTransaction := response.CardTransaction
				if cardTransaction != nil {
					// NOTE: the UpdateAt field will not be set here because it's a new document
					cardTransaction.EnrichBankingTransaction(bankingTransaction)
				}

			default:
				// FIXME: There should be a type of transactions specific to Card purchases and refunds

				L.Warn("Unknown TransactionType {TransactionType}", zap.String("TransactionType", ttrasaction.TransactionType), zap.Any("ttrasaction", ttrasaction))
				continue
			}

			if isNew {
				// Insert a new one
				if _, err = transactionsColl.Insert(bankingTransaction); err != nil {
					L.Error("Error while inserting Banking Transaction", zap.Error(err))
					continue
				}
				L.Info("New Banking Transaction has been inserted", zap.Any("btransaction", bankingTransaction))
			} else {
				// Perform an update of the existing
				if err = transactionsColl.Update(bankingTransaction); err != nil {
					L.Error("Error while updating Banking Transaction", zap.Error(err))
					continue
				}
				L.Info("Banking Transaction has been updated", zap.Any("btransaction", bankingTransaction))
			}

			L.Info("Saved new Banking Transaction on the DB from Treezor: {id}",
				zap.String("id", bankingTransaction.ID),
				zap.Int64("treezorId", ttrasaction.TreezorId),
				zap.Any("treezorTrasaction", ttrasaction),
				zap.Any("bankingTransaction", bankingTransaction))
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}
	return nil
}
