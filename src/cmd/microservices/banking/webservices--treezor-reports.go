package main

import (
	"fmt"
	"net/http"
	"strconv"
	"time"
	bus2 "yochbee/_base/bus"
	web2 "yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// registerTreezorReportsAPIs registers API endpoints for Treezor-specific reports, like account statements
func registerTreezorReportsAPIs(busrpc bus2.RpcClient, e *echo.Echo) error {
	// Account statements directly from Treezor
	e.GET("/pdf/treezor-statement/:accountId/:year/:month", func(c echo.Context) error {
		L := logging.L.Named("pdf/treezor-statement/:accountId/:year/:month")
		L.Info("Started ...")
		defer L.Info("Finished")

		// Parse the request parameters and convert to numerical values
		yearStr := c.Param("year")
		monthStr := c.Param("month")
		year, err := strconv.Atoi(yearStr)
		if err != nil {
			L.Error("Invalid request on `year`", zap.String("year", yearStr), zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		month, err := strconv.Atoi(monthStr)
		if err != nil {
			L.Error("Invalid request on `month`", zap.String("month", monthStr), zap.Error(err))
			return common.ReturnInvalidAPICallResponse(c)
		}
		if year < 2000 || year > 2100 || month < 1 || month > 12 {
			L.Error("Invalid request on `year` or `month`", zap.Int("year", year), zap.Int("month", month))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Ensure the year and month are not in the future
		now := time.Now()
		if year > now.Year() || (year == now.Year() && month > int(now.Month())) {
			L.Error("Invalid future date requested", zap.Int("year", year), zap.Int("month", month))
			return common.ReturnInvalidAPICallResponse(c)
		}

		// Ensure the year and month are not before Feb 2022 (earliest available from Treezor)
		if year < 2022 || (year == 2022 && month < 2) {
			L.Error("Date before Feb 2022 requested, not available from Treezor", zap.Int("year", year), zap.Int("month", month))
			return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Statements before February 2022 are not available")
		}

		account := c.Get("account").(*accounts.Account)

		// Request the statement PDF from Treezor
		statementResp, err := treezor.GetAccountStatement(busrpc, account.TreezorUserId, account.TreezorPrimaryWalletId, year, month)
		if err != nil {
			if err.Error() == treezor.ErrorNoMoreScaSessionJWT.Error() {
				return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCASessionExpired, "SCA session expired")
			}
			L.Error("Error getting account statement from Treezor", zap.Error(err))
			return err // HTTP 500
		}

		// Set Content-Disposition header for download
		fileName := fmt.Sprintf("statement-%s_%s-%04d-%02d.pdf", account.FirstName, account.LastName, year, month)
		c.Response().Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileName))

		// Redirect to Treezor's download URL
		return c.Redirect(http.StatusFound, statementResp.DownloadURL)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, middlewareRetrieveTreezorWallet, treezor.MiddlewareConditionalSCAProofForDates)

	return nil
}
