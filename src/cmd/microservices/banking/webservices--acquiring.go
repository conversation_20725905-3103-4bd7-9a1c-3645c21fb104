package main

import (
	"net/http"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"
	web2 "yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func registerAcquiringRESTApis(busrpc bus2.RpcClient, e *echo.Echo,
	tokensCollection *banking.AcquiringTokensCollection) {

	// Get the public authorization for HiPay
	e.GET("/acquiring/:accountId/public-authorization", func(c echo.Context) error {
		response, err := bus2.CallMethodMU[treezor.GetHiPayPublicAuthorizationResponse](busrpc, treezor.Method_GetHiPayPublicAuthorization, &treezor.GetHiPayPublicAuthorizationRequest{}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}

		// Get the account
		account := c.Get("account").(*accounts.Account)
		if account.TreezorUserId == 0 || account.TreezorPrimaryWalletId == 0 {
			logging.L.Error("Account not linked with Treezor", zap.String("accountId", account.ID))
			return web2.Error(c, common.ErrorCodeAccountNotLinkedWithTreezor, "Account not linked with Treezor")
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"authorization":   response.PublicAuthorization,
			"treezorUserId":   account.TreezorUserId,
			"treezorWalletId": account.TreezorPrimaryWalletId,
		})
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// List of card tokens that the user has added to his account
	e.GET("/acquiring/:accountId/tokens", func(c echo.Context) error {
		return web2.PaginatedEntitiesForMobile(c, tokensCollection, func(q dbnosql.QueryBuilder) {
			_ = q.Set(&dbnosql.Condition{"accountId", "==", c.Get("accountId").(string)})
			q.SetOrderByField("createdAt", dbnosql.OrderDESC)
		}, nil, nil, "tokens")
	}, accounts.MiddlewareRequireAccountId)

	// Add a card token to the user's account
	e.POST("/acquiring/:accountId/add-card-token", func(c echo.Context) error {
		L := logging.L.Named("add-card-token")
		L.Info("Starting ...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)

		// See https://docs.treezor.com/guide/cards/acquisition.html#step-1-tokenize-the-card for the structure of this
		// data. It is to be sent from the user's mobile device after the user has entered the card details to HiPay,
		// and the user has been redirected back to the app.
		request := struct {
			// CardLabel is a field to identify the card in the app
			CardLabel string `json:"cardLabel"  validate:"required,min=1"`

			Token     string `json:"token"                     validate:"required,min=20,max=64"`
			RequestId string `json:"request_id"                validate:"required,min=1,max=64"`
			CardId    string `json:"card_id"                   validate:"required,min=36,max=36"`
			MultiUse  int    `json:"multi_use"                 validate:"required,eq=1"` // must be == 1 for multi-use token
			Brand     string `json:"brand"                     validate:"required,min=1,max=20"`
			Pan       string `json:"pan"                       validate:"required,min=16,max=16"`

			CardCategory           string `json:"card_category"             validate:"max=15"`
			CardHolder             string `json:"card_holder"               validate:"required,min=1,max=40"`
			CardExpiryMonth        string `json:"card_expiry_month"         validate:"required,min=1,max=2"`
			CardExpiryYear         string `json:"card_expiry_year"          validate:"required,min=1,max=4"`
			Issuer                 string `json:"issuer"                    validate:"max=40"`
			Country                string `json:"country"                   validate:"required,min=1,max=2"`
			CardType               string `json:"card_type"                 validate:"required,min=1,max=10"`
			ForbiddenIssuerCountry bool   `json:"forbidden_issuer_country"`
		}{}
		if _, err := json2.ValidateBodyRelaxedInto(c.Request().Body, &request); err != nil {
			L.Error("Invalid request", zap.Error(err), zap.Any("request", request))
			return common.ReturnInvalidAPICallResponse(c)
		}

		token := &banking.AcquiringToken{
			ID: tokensCollection.GenerateUniqueId(),

			AccountId:     account.ID,
			TreezorUserId: account.TreezorUserId,

			CardLabel: request.CardLabel,

			Token:                  request.Token,
			RequestId:              request.RequestId,
			CardId:                 request.CardId,
			MultiUse:               request.MultiUse,
			Brand:                  request.Brand,
			Pan:                    request.Pan,
			CardCategory:           request.CardCategory,
			CardHolder:             request.CardHolder,
			CardExpiryMonth:        request.CardExpiryMonth,
			CardExpiryYear:         request.CardExpiryYear,
			Issuer:                 request.Issuer,
			Country:                request.Country,
			CardType:               request.CardType,
			ForbiddenIssuerCountry: request.ForbiddenIssuerCountry,

			CreatedAt: time.Now(),
		}
		if _, err := tokensCollection.Insert(token); err != nil {
			L.Error("Failed to insert token", zap.Error(err), zap.Any("token", token))
			return err // HTTP 500
		}

		L.Info("New Token inserted", zap.Any("token", token))
		return web2.SingleEntityForMobile(c, token, "token")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// Update basic information about a card token
	e.PUT("/acquiring/:accountId/update-card-token/:tokenId", func(c echo.Context) error {
		L := logging.L.Named("update-card-token")
		L.Info("Starting ...")
		defer L.Info("Finished")

		request := struct {
			CardLabel string `json:"cardLabel"  validate:"required,min=1"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			L.Error("Invalid request", zap.Error(err), zap.Any("request", request))
			return common.ReturnInvalidAPICallResponse(c)
		}

		account := c.Get("account").(*accounts.Account)
		tokenId := c.Param("tokenId")

		// Find the token
		result, err := tokensCollection.GetById(tokenId)
		if err != nil {
			L.Error("Failed to find token", zap.Error(err), zap.String("tokenId", tokenId))
			return err // HTTP 500
		}
		token, ok := result.(*banking.AcquiringToken)
		if !ok || token == nil {
			return common.ReturnResourceNotFoundWithMessageResponse(c, "Token not found")
		}

		// Check that the token belongs to the account
		if token.AccountId != account.ID {
			L.Error("👮‍POLICE: Token does not belong to account", zap.Any("token", token), zap.Any("account", account))
			return common.ReturnResourceNotFoundWithMessageResponse(c, "Token not found")
		}

		// Update the token
		token.CardLabel = request.CardLabel

		if err = tokensCollection.UpdateById(tokenId, token); err != nil {
			L.Error("Failed to update token", zap.Error(err), zap.Any("token", token))
			return err // HTTP 500
		}

		L.Info("Token updated", zap.Any("token", token))
		return web2.SingleEntityForMobile(c, token, "token")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)

	// Delete a card token
	e.DELETE("/acquiring/:accountId/delete-card-token/:tokenId", func(c echo.Context) error {
		L := logging.L.Named("delete-card-token")
		L.Info("Starting ...")
		defer L.Info("Finished")

		account := c.Get("account").(*accounts.Account)
		tokenId := c.Param("tokenId")

		// Find the token
		result, err := tokensCollection.GetById(tokenId)
		if err != nil {
			L.Error("Failed to find token", zap.Error(err), zap.String("tokenId", tokenId))
			return err // HTTP 500
		}
		token, ok := result.(*banking.AcquiringToken)
		if !ok || token == nil {
			return common.ReturnResourceNotFoundWithMessageResponse(c, "Token not found")
		}

		// Check that the token belongs to the account
		if token.AccountId != account.ID {
			L.Error("👮‍POLICE: Token does not belong to account", zap.Any("token", token), zap.Any("account", account))
			return common.ReturnResourceNotFoundWithMessageResponse(c, "Token not found")
		}

		// Delete the token
		if err = tokensCollection.DeleteById(token.ID); err != nil {
			L.Error("Failed to delete token", zap.Error(err), zap.Any("token", token))
			return err // HTTP 500
		}

		L.Info("Token deleted", zap.Any("token", token))
		return web2.OK(c)
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount)
}
