<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Relevé bancaire</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style rel="stylesheet">
        * {
            font-family: 'Roboto', sans-serif;
        }

            {
                {
                .css
            }
        }
    </style>
</head>

<body class="relative min-h-screen">
    <div class="container mx-auto">
        <div class="flex mb-20 ">
            <div class="basis-1/2">
                <div class="text-3xl font-bold">Yochbee </div>
                <div class="text-xs">4 AV ALBERT PETIT, 92220 BAGNEUX</div>
            </div>
            <div class="basis-1/2 text-right">
                <div class="text-3xl">Relevé bancaire (EUR)</div>
                <div class="text-xs">Généré le {{formatDateToFrench .rightNow "2 January 2006"}} à {{formatDateToFrench
                    .rightNow "15:04"}}</div>
            </div>
        </div>

        <div class="mb-5 text-right">
            <p class="font-bold text-xl">{{.fullName}}</p>
            <p>
                {{.address}}<br>
                {{.zipCode}} {{.city}}<br>
                {{.country}}
            </p>
        </div>

        <div class="flex mb-10">
            <div class="basis-1/2">&nbsp;</div>
            <div class="basis-1/2">
                <div class="flex">
                    <div class="basis-1/4">
                        <div class="font-bold">IBAN</div>
                        <div class="font-bold">BIC</div>
                    </div>
                    <div class="basis-3/4">
                        <div>{{.iban}}</div>
                        <div>{{.bic}}</div>
                    </div>
                </div>
            </div>
        </div>

        <!--    <div class="text-xl font-bold mb-2">Résumé du solde</div>-->
        <!--    <div class="mb-10">-->
        <!--        <table class="table-auto w-full">-->
        <!--            <thead class="border-b-4 text-sm">-->
        <!--            <tr>-->
        <!--                <th class="text-left">Compte</th>-->
        <!--                <th class="text-right">Ouverture</th>-->
        <!--                <th class="text-right">Argent sortant</th>-->
        <!--                <th class="text-right">Argent entrant</th>-->
        <!--                <th class="text-right">Clôture</th>-->
        <!--            </tr>-->
        <!--            </thead>-->

        <!--            <tbody>-->
        <!--            <tr class="border-b">-->
        <!--                <td class="text-left" >Client name</td>-->
        <!--                <td class="text-right">€8.00</td>-->
        <!--                <td class="text-right">€100.00</td>-->
        <!--                <td class="text-right">€100.00</td>-->
        <!--                <td class="text-right w-1/6">€8.00</td>-->
        <!--            </tr>-->
        <!--            </tbody>-->
        <!--        </table>-->
        <!--    </div>-->

        <div class="text-xl font-bold mb-2">Transactions du compte : du {{formatDateToFrench .startDate "2 January
            2006"}} au {{formatDateToFrench .endDate "2 January 2006"}}</div>
        <div class="mb-5">
            <table class="table-auto w-full">
                <thead class="border-b-4 text-sm">
                    <tr>
                        <th class="text-left">Date</th>
                        <th class="text-left">Description</th>
                        <th class="text-right">Argent sortant</th>
                        <th class="text-right">Argent entrant</th>
                        <!--                <th class="text-right">Solde</th>-->
                    </tr>
                </thead>

                <tbody>
                    {{$treezorWalletId := .treezorWalletId}}
                    {{range $i, $t := .transactions}}
                    <tr class="border-b">
                        <td class="text-left">{{formatDateToFrench $t.CreatedAt "2 Jan. 2006"}}</td>
                        <td class="text-left py-2">
                            {{ $t.Name }}
                            {{if $t.Description}}
                            <div class="text-xs">{{ $t.Description }}</div>
                            {{end}}
                        </td>
                        <td class="text-right">
                            {{if eq $t.TreezorWalletDebitId $treezorWalletId}}
                            €{{formatCurrency $t.Amount}}
                            {{end}}
                        </td>
                        <td class="text-right">
                            {{if eq $t.TreezorWalletCreditId $treezorWalletId}}
                            €{{formatCurrency $t.Amount}}
                            {{end}}
                        </td>
                        <!--                <td class="text-right w-1/6"></td>-->
                    </tr>
                    {{end}}
                    <tr class="border-b">
                        <td></td>
                        <td class="text-right">Total</td>
                        <td class="text-right">€{{formatCurrency .totalOut}} </td>
                        <td class="text-right">€{{formatCurrency .totalIn}}</td>
                    </tr>
                    <tr class="border-b">
                        <td class="text-left"></td>
                        <td class="text-right">Solde au {{formatDateToFrench .rightNow "02/01/2006"}}</td>
                        <td></td>
                        <td class="text-right">€{{formatCurrency .actualBalance}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <br>
    <br>
    <small>
        <div class="absolute inset-x-0 bottom-0 flex items-center justify-center">
            <div class="text-xs">
                <strong>YOCHBEE</strong>, au capital de 40 000 € - siège social 4 AV ALBERT PETIT, 92220 BAGNEUX - RCS
                Nanterre n°899 947 576.
                YOCHEBEE est enregistré auprès de l'Autorité de Contrôle Prudentiel et de Résolution (ACPR) en tant
                qu'agent de TREEZOR SAS,
                au capital de 5 307 513 € - siège social 33 AV DE WAGRAM, 75017 PARIS 17 - RCS Paris n°807 465 059,
                établissement de monnaie électronique agréé par l'ACPR sous le numéro 16798.
            </div>
        </div>
    </small>
</body>

</html>