package main

import (
	"net/http"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"
	"yochbee/_base/web"
	"yochbee/common"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/kyc"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

const (
	MaxAllowedPayoutAmount = 5000
)

func registerPayoutRESTAPIs(busrpc bus2.RpcClient, e *echo.Echo, beneficiariesColl *BeneficiariesCollection) {

	// --------------------
	e.POST("/payouts/:accountId", func(c echo.Context) error {
		request := struct {
			Amount        float64 `json:"amount"        validate:"required"`
			BeneficiaryId string  `json:"beneficiaryId" validate:"required"`
			Label         string  `json:"label"`
		}{}
		if _, err := json2.ValidateBodyStrictInto(c.Request().Body, &request); err != nil {
			return err // HTTP 500
		}

		L := logging.L.Named("create-payout").With(zap.String("beneficiaryId", request.BeneficiaryId))
		L.Info("Starting a Payout ...", zap.Any("request", request))

		// Check that the user is KYC validated
		account := c.Get("account").(*accounts.Account)
		progress := c.Get("progress").(*kyc.Progress)
		if !progress.HasCompletedKYCAndInitialPayment() {
			L.Error("👮 User not KYC validated: attempt to make a Payout", zap.String("accountId", account.ID))
			return web.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeKYCCompletionRequired, "User not KYC validated")
		}

		// Get requirements
		beneficiary, err := dbnosql.GetOneByID[*banking.Beneficiary](beneficiariesColl, request.BeneficiaryId)
		if err != nil {
			return err // HTTP 500
		}
		if beneficiary == nil {
			L.Error("Beneficiary not found", zap.String("beneficiaryId", request.BeneficiaryId))
			return web.Error(c, common.ErrorCodeBeneficiaryNotFound, "Beneficiary not found")
		}

		// Verify that the Wallet is in good state
		response, err := bus2.CallMethodMU[treezor.GetWalletByUserIdResponse](busrpc, treezor.Method_GetWalletByUserId, &treezor.GetWalletByUserIdRequest{
			TreezorUserId: account.TreezorUserId,
			WalletType:    treezor.WalletTypePaymentAccountWallet,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}
		if response.TreezorWallet.Status != "VALIDATED" {
			L.Error("Wallet not valid", zap.String("treezorWalletId", response.TreezorWallet.ID))
			return web.Error(c, common.ErrorCodeInvalidWalletState, "Invalid Wallet state")
		}

		// Verify that the Wallet has enough balance
		L.Info("Wallet received", zap.Any("wallet", response.TreezorWallet))
		if response.TreezorWallet.AuthorizedBalance < request.Amount {
			L.Error("No enough balance: attempt to make a Payout of €{amount}", zap.String("treezorWalletId", response.TreezorWallet.ID), zap.Float64("amount", request.Amount))
			return web.Error(c, common.ErrorCodeNoEnoughWalletBalance, "No enough balance")
		}

		//  Verify that the amount is not too high
		if request.Amount > MaxAllowedPayoutAmount {
			// TODO: Send an alert to the Yochbee team about this attempt!
			L.Error("ALERT: 🚨 Payout amount too high 🚨", zap.Float64("amount", request.Amount))
			return web.Error(c, common.ErrorCodePayoutAmountTooHigh, "Not allowed: Payout amount too high")
		}

		// Ask the `treezor` microservice to create a new SCTE Payout
		resp, err := bus2.CallMethodMU[treezor.PayoutWallet2BeneficiaryResponse](busrpc, treezor.Method_PayoutWallet2Beneficiary, treezor.PayoutWallet2BeneficiaryRequest{
			Account:       account,
			TreezorUserId: account.TreezorUserId,
			Amount:        request.Amount,
			Beneficiary:   beneficiary,
			Label:         request.Label,
		}, 10*time.Second)
		if err != nil {
			str := err.Error()
			if strings.Contains(str, "Amount too high for the wallet current balance") {
				L.Error("No enough balance", zap.String("treezorWalletId", response.TreezorWallet.ID))
				return web.Error(c, common.ErrorCodeNoEnoughWalletBalance, "No enough balance")
			}
			return err // HTTP 500
		}

		return web.SingleEntityForMobile(c, resp.TreezorPayout, "result")
	}, accounts.MiddlewareRequireAccountId, accounts.MiddlewareRetrieveAccount, kyc.MiddlewareRetrieveProgressByAccountId, treezor.MiddlewareRequireAndCacheSCAProof)
}
