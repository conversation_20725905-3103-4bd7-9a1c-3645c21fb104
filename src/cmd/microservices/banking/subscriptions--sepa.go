package main

import (
	"encoding/json"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/common/notifications"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

const (
	YochbeeSupportEmail = "<EMAIL>"
)

func subscribeToSepa(signaling bus2.Client, busrpc bus2.RpcClient) error {
	sepasQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-sepas", true, false,
		[]string{
			"treezor.sepaSctrInst.reject_sctr_inst",
		})
	if err != nil {
		return err
	}

	// ----------------
	if err = signaling.SubscribeToEventForever(sepasQueue, "treezor.sepa.reject_sctr_inst", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.sepaSctrInst.reject_sctr_inst")
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		sepaSctrEntries, err := treezor.UnwrapSepaSctrInstEntriesFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping SepaSctrInstEntries from webhook event", zap.Error(err))
			return
		}

		// Build a list of JSON bytes that we'll send by email, each on in its own <code> tag in the HTML email
		var jsonBytes [][]byte
		for _, sepaSctrEntry := range sepaSctrEntries {
			// Transfer the WebhookTimestamp to the Treezor SepaSctrInstEntry
			sepaSctrEntry.WebhookTimestamp = event.WebhookTimestamp

			j, err := json.Marshal(sepaSctrEntry)
			if err != nil {
				L.Error("Error while marshalling SepaSctrInstEntry to JSON", zap.Error(err))
				continue
			}
			jsonBytes = append(jsonBytes, j)
		}

		// Compose the email body (in French), in HTML and inform the Yochbee support of this rejected SCTR INST
		body := "Bonjour,<br><br>Un ou plusieurs SCTR INST ont été rejetés par Treezor :<br><br>"
		for _, j := range jsonBytes {
			body += "<code>"
			body += string(j)
			body += "</code><br><br>"
		}

		// Compose the email title
		title := "YOCHBEE BACKEND: SCTR INST rejeté par Treezor"

		// Send the email, by using the `notifications` service
		_, err = bus2.CallMethodMU[notifications.SendEmailNoReplyResponse](busrpc, notifications.Method_SendEmailNoReply, &notifications.SendEmailNoReplyRequest{
			ToEmailAddresses: []string{YochbeeSupportEmail},
			Subject:          title,
			ContentType:      "text/html",
			Body:             body,
		}, 30*time.Second)
		if err != nil {
			L.Error("Error while sending email", zap.Error(err))
			return
		}
		L.Info("Email sent to {to}", zap.String("to", YochbeeSupportEmail), zap.String("title", title), zap.String("body", body))

		// Acknowledge the event
		if err = ack(); err != nil {
			L.Error("Error while acknowledging event", zap.Error(err))
		}
	}); err != nil {
		return err
	}

	return nil
}
