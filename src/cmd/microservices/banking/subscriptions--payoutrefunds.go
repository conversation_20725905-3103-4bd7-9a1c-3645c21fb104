package main

import (
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/common/banking"
	"yochbee/common/treezor"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func subscribeToPayoutRefundsTreezorEvents(signaling bus2.Client, payoutsColl dbnosql.DataCollection) error {
	refundsQueue, err := signaling.DeclareSubscriptionQueue("Banking.Subscriptions-refunds", true, false,
		[]string{
			"treezor.payoutrefunds.create",
			"treezor.payoutrefunds.update",
		})
	if err != nil {
		return err
	}

	// --------------------
	if err := signaling.SubscribeToEventForever(refundsQueue, "banking/subscriptions--payoutrefunds.go", false, func(event *bus2.Event, ack func() error) {
		L := logging.L.Named("Consumer(treezor.payoutrefunds.{create,update})").With(zap.String("event", event.Topic))
		L.Info("Started", zap.String("msg", event.Data.(string)))
		defer L.Info("Finished")

		payoutRefunds, err := treezor.UnwrapPayoutRefundsFromTreezorResponse([]byte(event.Data.(string)))
		if err != nil {
			L.Error("Error while unwrapping Payouts from webhook event", zap.Error(err))
			return
		}

		for _, trefund := range payoutRefunds {
			L := L.With(zap.String("treezorRefundID", trefund.ID))

			// Get the related Payout so we can generate the proper event
			payout, err := dbnosql.GetOneByField[*banking.Payout](payoutsColl, "treezorPayoutId", trefund.TreezorPayoutID)
			if err != nil {
				L.Error("Failed to get related Payout", zap.Error(err))
				continue
			}
			if payout == nil {
				L.Error("Related Payout not found")
				continue
			}

			// Deduce the current state and compare the before and after state to generate the proper event
			prePayout := *payout
			switch event.Topic {
			case "treezor.payoutrefunds.create":
				payout.IsRefunded = true
				payout.IsRefundCompleted = false

				if prePayout.IsRefunded != true {
					if err = signaling.PublishEvent(&bus2.Event{
						Topic: "banking.payout.RefundStarted",
						Data:  payout,
						Time:  time.Now(),
					}); err != nil {
						L.Error("Failed to publish banking.payout.RefundStarted event", zap.Error(err))
					} else {
						L.Info("Published banking.payout.RefundStarted event")
					}
				}
			case "treezor.payoutrefunds.update":
				payout.IsRefunded = true
				payout.IsRefundCompleted = trefund.InformationStatus == "VALIDATED"

				if payout.IsRefundCompleted && prePayout.IsRefundCompleted != true {
					if err = signaling.PublishEvent(&bus2.Event{
						Topic: "banking.payout.RefundCompleted",
						Data:  payout,
						Time:  time.Now(),
					}); err != nil {
						L.Error("Failed to publish banking.payout.RefundCompleted event", zap.Error(err))
					} else {
						L.Info("Published banking.payout.RefundCompleted event")
					}
				}
			}

			// Persist the state on the DB
			if err = payoutsColl.UpdateById(payout.ID, payout); err != nil {
				L.Error("Failed to update Payout on the DB", zap.Error(err))
			}
			L.Info("Success handling Payout Refund event")
		}

		// Don't forget to ...
		_ = ack()
	}); err != nil {
		return err
	}
	return nil
}
