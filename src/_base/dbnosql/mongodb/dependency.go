package mongodb

import (
	"errors"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

func CreateDBAccess(config config.Provider) (dbnosql.Database, error) {
	csb, err := config.Get("config.database.mongodb.connection_string")
	if err != nil {
		logging.L.Error("Failed to get config.database.mongodb.connection_string from etcd", zap.Error(err))
		return nil, err
	}
	if len(csb) == 0 {
		logging.L.Error("Please add configuration on etcd with the following key: config.database.mongodb.connection_string")
		return nil, errors.New("please add configuration on etcd with the following key: config.database.mongodb.connection_string")
	}

	const databaseName = "yochbee"
	return NewAccess(string(csb), databaseName)
}
