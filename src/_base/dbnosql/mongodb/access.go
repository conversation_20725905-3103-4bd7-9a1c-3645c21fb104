package mongodb

import (
	"context"
	"errors"
	"reflect"
	"time"

	"yochbee/_base/dbnosql"

	"github.com/eliezedeck/gobase/logging"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var (
	ErrFailedCreatingDBClient = errors.New("failed creating a DB client")
	ErrFailedConnecting       = errors.New("failed connecting to the DB")
)

type Access struct {
	dbClient *mongo.Client
	dbName   string
}

// NewAccess creates a new Access which connects to the database and access the given database
func NewAccess(uri, database string) (*Access, error) {
	builder := bson.NewRegistryBuilder()

	// Decoder for C# field value {"_csharpnull": true} to map to Go's *time.Time = nil
	builder.RegisterTypeDecoder(reflect.TypeOf(&time.Time{}), CSharpNullTimeDecoder{})

	clientOpts := options.Client().ApplyURI(uri).SetRegistry(builder.Build())

	client, err := mongo.NewClient(clientOpts)
	if err != nil {
		logging.L.Error("Failed creating a DB client", zap.Error(err))
		return nil, ErrFailedCreatingDBClient
	}

	ctx, ctxCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer ctxCancel()

	err = client.Connect(ctx)
	if err != nil {
		logging.L.Error("Failed connecting to the DB", zap.Error(err))
		return nil, ErrFailedConnecting
	}

	access := &Access{
		dbClient: client,
		dbName:   database,
	}
	return access, nil
}

func (a *Access) DeclareCollection(name string, toStorage func(doc interface{}) interface{}, destinationProvider func() (destination interface{})) dbnosql.DataCollection {
	c := a.dbClient.Database(a.dbName).Collection(name)
	return &Collection{
		mc:                  c,
		toStorage:           toStorage,
		destinationProvider: destinationProvider,
	}
}
