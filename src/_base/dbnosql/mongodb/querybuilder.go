package mongodb

import (
	"errors"
	"yochbee/_base/dbnosql"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	ErrModeAlreadySet = errors.New("mode has already been set for QueryBuilder")
)

type QueryBuilder struct {
	mode int // 0 not yet set, 1 for direct condition, 2 for AND, 3 for OR

	m1Condition *dbnosql.Condition

	m2AndConditions []*dbnosql.Condition

	m3OrConditions []*dbnosql.Condition

	orderByField *string
	orderByOrder dbnosql.IndexFieldOrdering

	limit *int64

	offset *int64
}

func (q *QueryBuilder) ByID(id string) error {
	return q.Set(&dbnosql.Condition{
		Field:      DefaultIDFieldKey,
		Comparator: "==",
		Value:      id,
	})
}

func (q *QueryBuilder) Set(condition *dbnosql.Condition) error {
	if q.mode != 0 {
		return ErrModeAlreadySet
	}
	q.mode = 1
	q.m1Condition = condition
	return nil
}

func (q *QueryBuilder) SetAnd(conditions ...*dbnosql.Condition) error {
	if q.mode != 0 {
		return ErrModeAlreadySet
	}

	q.mode = 2
	q.m2AndConditions = conditions
	return nil
}

func (q *QueryBuilder) SetOr(conditions ...*dbnosql.Condition) error {
	if q.mode != 0 {
		return ErrModeAlreadySet
	}

	q.mode = 3
	q.m3OrConditions = conditions
	return nil
}

func (q *QueryBuilder) Render() (nativeQuery interface{}, err error) {
	if q == nil {
		return bson.D{}, nil
	}

	switch q.mode {
	case 1:
		return Render(q.m1Condition)
	case 2:
		container := bson.A{}
		for _, c := range q.m2AndConditions {
			if c != nil {
				native, err := Render(c)
				if err != nil {
					return nil, err
				}
				container = append(container, native)
			}
		}
		nativeQuery = bson.D{{"$and", container}}
		return nativeQuery, nil
	case 3:
		container := bson.A{}
		for _, c := range q.m3OrConditions {
			if c != nil {
				native, err := Render(c)
				if err != nil {
					return nil, err
				}
				container = append(container, native)
			}
		}
		nativeQuery = bson.D{{"$or", container}}
		return nativeQuery, nil
	default:
		return nil, errors.New("invalid mode for QueryBuilder; use Set(), SetAnd() or SetOr() before calling Render()")
	}
}

func (q *QueryBuilder) SetOrderByField(name string, order dbnosql.IndexFieldOrdering) {
	q.orderByField = &name
	q.orderByOrder = order
}

func (q *QueryBuilder) SetLimit(n int64) {
	q.limit = &n
}

func (q *QueryBuilder) SetOffset(offset int64) {
	q.offset = &offset
}

func (q *QueryBuilder) RenderFindOptions() (nativeOptions interface{}, err error) {
	opts := options.Find()
	if q == nil {
		return opts, nil
	}

	if q.orderByField != nil {
		opts.SetSort(bson.D{{*q.orderByField, q.orderByOrder}})
	}
	if q.limit != nil {
		opts.SetLimit(*q.limit)
	}
	if q.offset != nil {
		opts.SetSkip(*q.offset)
	}
	return opts, nil
}
