package mongodb

import (
	"fmt"
	"yochbee/_base/dbnosql"

	"go.mongodb.org/mongo-driver/bson"
)

type QueryCondition struct {
	Field      string
	Comparator string // possible string values are ==, <=, <, >=, >
	Value      interface{}
}

func <PERSON>(condition *dbnosql.Condition) (nativeQuery interface{}, err error) {
	switch condition.Comparator {
	case "==":
		return bson.D{{
			condition.Field, condition.Value,
		}}, nil
	case "!=":
		return bson.D{{
			condition.Field, bson.M{
				"$ne": condition.Value,
			},
		}}, nil
	case "<=":
		return bson.D{{
			condition.Field, bson.M{
				"$lte": condition.Value,
			},
		}}, nil
	case "<":
		return bson.D{{
			condition.Field, bson.M{
				"$lt": condition.Value,
			},
		}}, nil
	case ">=":
		return bson.D{{
			condition.Field, bson.M{
				"$gte": condition.Value,
			},
		}}, nil
	case ">":
		return bson.D{{
			condition.Field, bson.M{
				"$gt": condition.Value,
			},
		}}, nil
	case "inArray":
		return bson.D{{
			condition.Field, bson.M{
				"$in": condition.Value,
			},
		}}, nil
	case "regex":
		return bson.D{{
			condition.Field, bson.M{
				"$regex": condition.Value,
			},
		}}, nil
	default:
		return nil, fmt.Errorf("unsupported Comparator %s in QueryCondition", condition.Comparator)
	}
}
