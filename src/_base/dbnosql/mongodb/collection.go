package mongodb

import (
	"context"
	"errors"
	"fmt"
	"yochbee/_base/dbnosql"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	DefaultIDFieldKey = "_id"
)

type Collection struct {
	mc *mongo.Collection

	toStorage           func(doc interface{}) interface{}
	destinationProvider func() (destination interface{})
}

func (c *Collection) SetUniqueIndexOn(definition dbnosql.IndexDefinition) (err error) {
	if len(definition.Fields) == 0 {
		return errors.New("no fields provided for SetUniqueIndexOn()")
	}

	// Build the index model
	keys := bson.D{}
	for _, f := range definition.Fields {
		keys = append(keys, bson.E{Key: f.Name, Value: int(f.Order)})
	}
	mo := mongo.IndexModel{
		Keys:    keys,
		Options: options.Index().SetName(fmt.Sprintf("%sUniqueIdx", definition.Name)).SetUnique(true),
	}

	if _, err := c.mc.Indexes().CreateOne(context.Background(), mo); err != nil {
		return err
	}
	return nil
}

func (c *Collection) SetIndexOn(definition dbnosql.IndexDefinition) (err error) {
	if len(definition.Fields) == 0 {
		return errors.New("no fields provided for SetIndexOn()")
	}

	// Build the index model
	keys := bson.D{}
	for _, f := range definition.Fields {
		keys = append(keys, bson.E{Key: f.Name, Value: int(f.Order)})
	}
	mo := mongo.IndexModel{
		Keys:    keys,
		Options: options.Index().SetName(fmt.Sprintf("%sIdx", definition.Name)).SetUnique(false),
	}

	if _, err := c.mc.Indexes().CreateOne(context.Background(), mo); err != nil {
		return err
	}
	return nil
}

func (c *Collection) SetMultipleIndexesOn(fields ...string) (err error) {
	if len(fields) == 0 {
		return errors.New("no fields provided for SetMultipleIndexesOn()")
	}

	for _, field := range fields {
		order := dbnosql.OrderASC
		if field == "createdAt" {
			order = dbnosql.OrderDESC
		}

		if err = c.SetIndexOn(dbnosql.IndexDefinition{
			Name:   field,
			Fields: []dbnosql.IndexField{{Name: field, Order: order}},
		}); err != nil {
			return err
		}
	}
	return nil
}

func (c *Collection) GenerateUniqueId() string {
	id := primitive.NewObjectID()
	return id.Hex()
}

func (c *Collection) Insert(doc interface{}) (id string, err error) {
	// convert the data to BSON
	converted := c.toStorage(doc)

	res, err := c.mc.InsertOne(context.Background(), converted)
	if err != nil {
		return "", err
	}
	if p, ok := res.InsertedID.(primitive.ObjectID); ok {
		return p.Hex(), nil
	}
	return res.InsertedID.(string), nil
}

func (c *Collection) UpdateById(id string, doc interface{}) (err error) {
	// convert the data to BSON
	converted := c.toStorage(doc)

	_, err = c.mc.UpdateOne(context.Background(), bson.M{"_id": id}, bson.M{"$set": converted})
	if err != nil {
		return err
	}
	return nil
}

func (c *Collection) UpdateOneByField(field string, value interface{}, doc interface{}) (err error) {
	// convert the data to BSON
	converted := c.toStorage(doc)

	_, err = c.mc.UpdateOne(context.Background(), bson.M{field: value}, bson.M{"$set": converted})
	if err != nil {
		return err
	}
	return nil
}

func (c *Collection) Find(query dbnosql.QueryBuilder) (docs []interface{}, err error) {
	qr, err := query.Render()
	if err != nil {
		return nil, err
	}
	qo, err := query.RenderFindOptions()
	if err != nil {
		return nil, err
	}
	u, err := c.mc.Find(context.Background(), qr, qo.(*options.FindOptions))
	if err != nil {
		return nil, err
	}

	// Allocate the place for the results
	docs = make([]interface{}, 0, u.RemainingBatchLength())

	for u.Next(context.Background()) {
		dest := c.destinationProvider()
		if err = u.Decode(dest); err != nil {
			return nil, err
		}
		docs = append(docs, dest)
	}
	return docs, nil
}

func (c *Collection) Count(query dbnosql.QueryBuilder) (count int64, err error) {
	qr, err := query.Render()
	if err != nil {
		return -1, err
	}
	return c.mc.CountDocuments(context.Background(), qr)
}

func (c *Collection) Delete(query dbnosql.QueryBuilder) (count int64, err error) {
	qr, err := query.Render()
	if err != nil {
		return 0, err
	}
	r, err := c.mc.DeleteMany(context.Background(), qr)
	if err != nil {
		return 0, err
	}
	return r.DeletedCount, nil
}

func (c *Collection) DeleteById(id string) (err error) {
	_, err = c.mc.DeleteOne(context.Background(), bson.M{"_id": id})
	if err != nil {
		return err
	}
	return nil
}

func (c *Collection) GetById(id string) (doc interface{}, err error) {
	dest := c.destinationProvider()
	if err = c.mc.FindOne(context.Background(), bson.M{"_id": id}).Decode(dest); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return dest, nil
}

func (c *Collection) GetOneByField(field string, value interface{}) (doc interface{}, err error) {
	dest := c.destinationProvider()
	if err = c.mc.FindOne(context.Background(), bson.M{field: value}).Decode(dest); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}
	return dest, nil
}

func (c *Collection) GetQueryBuilder() dbnosql.QueryBuilder {
	return &QueryBuilder{}
}

func (c *Collection) GetRawCollection() interface{} {
	return c.mc
}

func (c *Collection) GetDocAllocator() func() interface{} {
	return c.destinationProvider
}

func (c *Collection) GetName() string {
	return c.mc.Name()
}
