package dbnosql

import (
	"errors"
	"time"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

var (
	ErrGetOneNonUniqueResult = errors.New("non-unique result for a GetOne*() method")
	ErrMissingID             = errors.New("no ID provided for insert/update operation")
)

func GetOneByField[Op any](coll DataCollection, field string, equalValue interface{}) (Op, error) {
	var zero Op
	q := coll.GetQueryBuilder()
	_ = q.Set(&Condition{field, "==", equalValue})
	docs, err := coll.Find(q)
	if err != nil {
		return zero, err
	}
	if len(docs) > 1 {
		logging.L.Error("GetOneByField() returned more than one result", zap.String("field", field), zap.Any("equalValue", equalValue), zap.String("collection", coll.GetName()))
		return zero, ErrGetOneNonUniqueResult
	}
	if len(docs) == 0 {
		return zero, nil
	}
	return docs[0].(Op), nil
}

func GetMultiByField[Op any](coll DataCollection, field string, arryValue []any) ([]Op, error) {
	q := coll.GetQueryBuilder()
	_ = q.Set(&Condition{field, "inArray", arryValue})
	docs, err := coll.Find(q)
	if err != nil {
		return nil, err
	}
	return ConvertDocsList[Op](docs), nil
}

func GetOneByID[T any](coll DataCollection, id string) (T, error) {
	return GetOneByField[T](coll, "_id", id)
}

func ConvertDocsList[P any](list []interface{}) []P {
	result := make([]P, len(list))
	for i, v := range list {
		result[i] = v.(P)
	}
	return result
}

func PNow() *time.Time {
	now := time.Now()
	return &now
}

func PDate(year int, month time.Month, day, hour, min, sec, nsec int, loc *time.Location) *time.Time {
	t := time.Date(year, month, day, hour, min, sec, nsec, loc)
	return &t
}
