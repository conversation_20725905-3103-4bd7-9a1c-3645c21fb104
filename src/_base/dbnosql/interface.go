package dbnosql

type Database interface {
	DeclareCollection(name string, toStorage func(doc interface{}) interface{}, destinationProvider func() (destination interface{})) DataCollection
}

type DataCollection interface {
	SetUniqueIndexOn(definition IndexDefinition) (err error)
	SetIndexOn(definition IndexDefinition) (err error)
	SetMultipleIndexesOn(fields ...string) (err error)

	GenerateUniqueId() string

	Insert(doc interface{}) (id string, err error)
	UpdateById(id string, doc interface{}) (err error)
	UpdateOneByField(field string, value interface{}, doc interface{}) (err error)

	// Find interrogates the database with the given query and return the results. For each result, the
	// destinationProvider function will be called to provide a struct in which the data will be decoded into; those are
	// then placed into a slice of interface.
	Find(query QueryBuilder) (docs []interface{}, err error)

	// Count returns the number of documents matching the given query. If the query is nil, it will count all documents.
	Count(query QueryBuilder) (count int64, err error)

	// Delete is similar to Find, but instead of returning the matching documents, it will delte them
	Delete(query QueryBuilder) (count int64, err error)
	DeleteById(id string) (err error)

	GetById(id string) (doc interface{}, err error)
	GetOneByField(field string, value interface{}) (doc interface{}, err error)

	GetQueryBuilder() QueryBuilder

	// GetRawCollection returns the native collection for the underlying DB; currently, it will be a *mongo.Collection
	GetRawCollection() interface{}
	GetDocAllocator() func() interface{}
	GetName() string
}

type QueryBuilder interface {
	ByID(id string) error
	Set(condition *Condition) error
	SetAnd(conditions ...*Condition) error
	SetOr(conditions ...*Condition) error

	SetOrderByField(name string, order IndexFieldOrdering)
	SetLimit(n int64)
	SetOffset(offset int64)

	Render() (nativeQuery interface{}, err error)
	RenderFindOptions() (nativeOptions interface{}, err error)
}

type IndexFieldOrdering int

const (
	OrderASC  IndexFieldOrdering = 1
	OrderDESC IndexFieldOrdering = -1
)

type IndexField struct {
	Name  string
	Order IndexFieldOrdering
}

type IndexDefinition struct {
	Fields []IndexField
	// Name of the index, it is automatically suffixed with "Idx" so no need to add that
	Name string
}

type Condition struct {
	Field      string
	Comparator string
	Value      interface{}
}
