package security

import "testing"

func TestEncryptDecrypt1(t *testing.T) {
	// Test that we can encrypt and decrypt a string using a 256-bit key
	plaintext := "This is a test"
	password := "password"
	passwordSalt := "salt"

	key, err := Der<PERSON><PERSON><PERSON>(password, passwordSalt)
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}

	ciphertext, err := Encrypt(key, plaintext)
	if err != nil {
		t.<PERSON>rror(err)
	}

	key, err = Der<PERSON><PERSON><PERSON>(password, passwordSalt)
	if err != nil {
		t.<PERSON>rror(err)
	}
	decrypted, err := Decrypt(key, ciphertext)
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}

	if decrypted != plaintext {
		t.<PERSON><PERSON>("Expected %s, got %s", plaintext, decrypted)
	}
}
