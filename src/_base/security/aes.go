package security

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"errors"
)

var (
	// ErrInvalidKeySize is returned when the key is not 32 bytes long
	ErrInvalidKeySize    = errors.New("invalid key size")
	ErrPlainTextTooLong  = errors.New("password too long")
	ErrInvalidChiperText = errors.New("invalid chiper text")
)

// Encrypt a plaintext string using AES-256 encryption with a given key. The key must be 32 bytes long.
func Encrypt(key []byte, plaintext string) ([]byte, error) {
	l := len(plaintext)
	if l > 200 {
		return nil, ErrPlainTextTooLong
	}

	// We'll add a frame to ensure that decoded data is always be without null bytes
	framed := make([]byte, l+1)
	framed[0] = byte(l)
	copy(framed[1:], plaintext)

	// Ensure the key is 32 bytes long
	if len(key) != 32 {
		return nil, ErrInvalidKeySize
	}

	// Generate a new random IV (initialization vector)
	iv := make([]byte, aes.BlockSize)
	if _, err := rand.Read(iv); err != nil {
		return nil, err
	}

	// Create a new AES cipher block from the key
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// Pad the plaintext with null bytes to ensure that it's a multiple of the block size
	padding := aes.BlockSize - (len(framed) % aes.BlockSize)
	padded := append(framed, bytes.Repeat([]byte{0}, padding)...)

	// Create a new CBC (cipher-block chaining) mode encrypter with the AES cipher and IV
	encrypter := cipher.NewCBCEncrypter(block, iv)

	// Encrypt the plaintext using CBC mode
	ciphertext := make([]byte, aes.BlockSize+len(padded))
	copy(ciphertext[:aes.BlockSize], iv)
	encrypter.CryptBlocks(ciphertext[aes.BlockSize:], padded)

	return ciphertext, nil
}

// Decrypt a ciphertext string using AES-256 encryption with a given key. The key must be 32 bytes long.
func Decrypt(key []byte, ciphertext []byte) (string, error) {
	// Ensure the key is 32 bytes long
	if len(key) != 32 {
		return "", ErrInvalidKeySize
	}

	// Extract the IV from the ciphertext
	iv := ciphertext[:aes.BlockSize]

	// Create a new AES cipher block from the key
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// Create a new CBC (cipher-block chaining) mode decrypter with the AES cipher and IV
	decrypter := cipher.NewCBCDecrypter(block, iv)

	// Decrypt the ciphertext using CBC mode
	decrypted := make([]byte, len(ciphertext)-aes.BlockSize)
	decrypter.CryptBlocks(decrypted, ciphertext[aes.BlockSize:])

	// Unframe the plaintext
	if len(decrypted) < 1 {
		return "", ErrInvalidChiperText
	}
	l := int(decrypted[0])
	if l > len(decrypted)-1 {
		return "", ErrInvalidChiperText
	}
	unpadded := decrypted[1 : l+1]

	return string(unpadded), nil
}
