package bus

import (
	"encoding/json"
	"time"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type BoolResult struct {
	OK bool `json:"ok"`
}

// CallMethodMU marshals the payload in, and unmarshalls response as a concrete set by the user. The returned value type
// should not be a pointer because it's already converted to a pointer on output.
func CallMethodMU[Oo any](busrpc RpcClient, method string, payload interface{}, timeout time.Duration) (*Oo, error) {
	L := logging.L.Named("CallMethodMU").With(zap.String("method", method))

	marshalled, err := json.Marshal(payload)
	if err != nil {
		<PERSON>.<PERSON>rror("Failed to marshal payload", zap.Error(err))
		return nil, err
	}
	b, err := busrpc.Call(method, marshalled, timeout)
	if err != nil {
		L.<PERSON>rror("Failed to call method", zap.Error(err))
		return nil, err
	}
	var result Oo
	if err = json.Unmarshal(b, &result); err != nil {
		<PERSON>.<PERSON>r("Failed to unmarshal response", zap.Error(err))
		return nil, err
	}
	return &result, nil
}

// RegisterUM is the same as Register, but it automatically unmarshalls the payload and marshals the response for a more
// strongly typed usage and convenience.
func RegisterUM[I any, O any](method string, busrpc RpcClient, handler func(*I) (*O, error), parallelProcessing bool) error {
	return busrpc.Register(method, func(payload []byte) ([]byte, error) {
		// Unmarshal the incoming payload
		var incoming I
		if err := json.Unmarshal(payload, &incoming); err != nil {
			return nil, err
		}

		// Call the user's handler function
		outgoing, err := handler(&incoming)
		if err != nil {
			return nil, err
		}
		return json.Marshal(outgoing)
	}, parallelProcessing)
}
