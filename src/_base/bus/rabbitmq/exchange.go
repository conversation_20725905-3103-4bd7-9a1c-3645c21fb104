package rabbitmq

import "encoding/json"

type rpcResponseExchange struct {
	Success bool   `json:"success"`
	Method  string `json:"method"`
	Error   string `json:"error"`
	Body    []byte `json:"body"`
}

func newRpcResponseExchangeFromBytes(data []byte) (*rpcResponseExchange, error) {
	r := &rpcResponseExchange{}
	if err := json.Unmarshal(data, &r); err != nil {
		return nil, err
	}
	return r, nil
}

func (x *rpcResponseExchange) ToJSON() []byte {
	b, err := json.Marshal(x)
	if err != nil {
		panic(err)
	}
	return b
}
