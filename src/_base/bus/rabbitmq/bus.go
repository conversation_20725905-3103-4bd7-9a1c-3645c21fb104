package rabbitmq

import (
	"encoding/json"
	"os"
	"time"
	"yochbee/_base/bus"

	"github.com/eliezedeck/gobase/logging"
	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// BusClient implements both the bus.Client and the bus.RpcClient over RabbitMQ. Thus, it implements simple bus
// messaging and RPC calls over the bus.
//
// An important aspect of this bus is that it does NOT implement auto-reconnect if the bus is down. It is assumed that
// the bus is the central foundation of the whole system, nothing should move if the bus is down. Continuing while the
// bus is down could be dangerous. With that said, it is thus preferable to shutdown (or panic) if the bus is down.
//
// By design, registered methods must generally also be idempotent. If the same parameter is given to the method, the
// system should not be moved to a different state.
type BusClient struct {
	conn            *amqp.Connection
	exchange        string
	exchangeChannel *amqp.Channel // used only to declare the exchange on RabbitMQ
}

// NewBusClient creates a new instance of BusClient.
//
// It is advised to panic if there is a bus error (see the documentation about BusClient). You can use the onBusError
// callback for that. If it is not possible to establish a bus connection, it is also advisable to shutdown, the
// infrastructure (Kubernetes in the time of writing) will automatically relaunch the microservice, and it will keep
// crashing until the bus is ready. This is safe operation thru FX lifecycle hook.
//
// It is up to the dependencies writer to set each type of the buses, such as "direct" or "fanout" for different
// purposes of the whole system. The fx.In and fx.Out can be used for that purpose.
func NewBusClient(connstr, exchange, exchangeType string, durable, autoDelete bool, onBusError func(*amqp.Error)) (*BusClient, error) {
	config := amqp.Config{}
	if os.Getenv("KUBERNETES_SERVICE_HOST") == "" {
		config.Heartbeat = 5 * time.Minute
	}

	conn, err := amqp.DialConfig(connstr, config)
	if err != nil {
		return nil, err
	}
	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}

	amqpErrChan := conn.NotifyClose(make(chan *amqp.Error))
	go func() {
		if amqperr := <-amqpErrChan; amqperr != nil {
			onBusError(amqperr)
		}
	}()

	if err = ch.ExchangeDeclare(exchange, exchangeType, durable, autoDelete, false, false, nil); err != nil {
		return nil, err
	}

	return &BusClient{
		exchange:        exchange,
		conn:            conn,
		exchangeChannel: ch,
	}, nil
}

func (b *BusClient) CanAutoReconnect() bool {
	return false
}

func (b *BusClient) DeclareQueue(name string, durable, exclusive bool) (string, error) {
	q, err := b.exchangeChannel.QueueDeclare(name, durable, false, exclusive, false, nil)
	if err != nil {
		return "", err
	}

	if err = b.exchangeChannel.QueueBind(name, name, b.exchange, false, nil); err != nil {
		return "", err
	}

	return q.Name, nil
}

func (b *BusClient) DeclareSubscriptionQueue(name string, durable, exclusive bool, topics []string) (string, error) {
	q, err := b.exchangeChannel.QueueDeclare(name, durable, false, exclusive, false, nil)
	if err != nil {
		return "", err
	}

	for _, topic := range topics {
		if err = b.exchangeChannel.QueueBind(q.Name, topic, b.exchange, false, nil); err != nil {
			return "", err
		}
	}

	return q.Name, nil
}

func (b *BusClient) Publish(msg []byte, contentType, topic string) error {
	if err := b.exchangeChannel.Publish(b.exchange, topic, false, false, amqp.Publishing{
		DeliveryMode: amqp.Persistent,
		ContentType:  contentType,
		Timestamp:    time.Now(),
		Body:         msg,
	}); err != nil {
		return err
	}
	return nil
}

func (b *BusClient) PublishM(msg interface{}, topic string) error {
	mb, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	if err := b.exchangeChannel.Publish(b.exchange, topic, false, false, amqp.Publishing{
		DeliveryMode: amqp.Persistent,
		ContentType:  "application/json",
		Timestamp:    time.Now(),
		Body:         mb,
	}); err != nil {
		return err
	}
	return nil
}

func (b *BusClient) PublishEvent(event *bus.Event) error {
	// If the Data is a byte array, convert it to a string to avoid creating base64 encoded strings.
	// Anything else will be marshalled as JSON, using the default JSON marshaller.
	if data, ok := event.Data.([]byte); ok {
		event.Data = string(data)
	}

	eb, err := json.Marshal(event)
	if err != nil {
		return err
	}

	if err := b.exchangeChannel.Publish(b.exchange, event.Topic, true /* mandatory */, false, amqp.Publishing{
		DeliveryMode: amqp.Persistent,
		ContentType:  "application/json",
		Timestamp:    event.Time,
		Body:         eb,
	}); err != nil {
		return err
	}
	return nil
}

func (b *BusClient) SubscribeForever(queue, tag string, exclusive bool, msgHandler func(msg []byte, contentType string, ack func() error)) error {
	// Use a separate channel for consuming
	ch, err := b.conn.Channel()
	if err != nil {
		return err
	}
	// No need to close this channel, it is meant to be open for the whole lifetime of the microservice

	// Set the Qos to 1, so that the bus will not send more than one message to the consumer at a time.
	// This is to avoid the situation when the consumer is slow, and the bus is sending messages faster than
	// the consumer can process them. This will cause the bus to buffer messages in memory, which is not good.
	if err = ch.Qos(1, 0, false); err != nil {
		return err
	}

	d, err := ch.Consume(queue, tag, false, exclusive, false, false, nil)
	if err != nil {
		return err
	}
	go func() {
		createDoAckFunc := func(livery amqp.Delivery) func() error {
			return func() error {
				if err := livery.Ack(false); err != nil {
					return err
				}
				return nil
			}
		}

		for livery := range d {
			doAck := createDoAckFunc(livery)
			msgHandler(livery.Body, livery.ContentType, doAck)
		}
	}()
	return nil
}

func (b *BusClient) SubscribeToEventForever(queue, tag string, exclusive bool, eventHandler func(event *bus.Event, ack func() error)) error {
	return b.SubscribeForever(queue, tag, exclusive, func(msg []byte, contentType string, ack func() error) {
		// Convert the message to an event
		event := bus.Event{}
		if err := json.Unmarshal(msg, &event); err != nil {
			logging.L.Error("Failed unmarshalling event during SubscribeToEventForever", zap.Error(err))
			return
		}
		eventHandler(&event, ack)
	})
}
