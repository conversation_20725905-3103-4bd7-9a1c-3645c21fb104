package rabbitmq

import (
	"errors"
	"strings"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"

	"github.com/streadway/amqp"
	"github.com/tidwall/gjson"
)

// CreateBusClient returns a new instance of bus2.Client and bus2.RpcClient, it is the same instance that is capable of
// fulfilling both roles.
func CreateBusClient(config config.Provider) (bus2.Client, bus2.RpcClient, error) {
	mobile, err := config.Get("config.bus.yochbee.mobile")
	if err != nil {
		return nil, nil, err
	}
	if strings.TrimSpace(string(mobile)) == "" {
		return nil, nil, errors.New("mobile bus configuration is missing")
	}
	rc := gjson.ParseBytes(mobile)

	mbus, err := NewBusClient(
		rc.Get("connectionString").String(),
		rc.Get("exchange").String(),
		rc.Get("exchangeType").String(),
		rc.Get("durable").<PERSON><PERSON>(),
		rc.Get("autoDelete").Bool(),
		func(aerr *amqp.Error) {
			// Bus is assumed to never get down. If it does, it is a dangerous and there is more serious issue at hand.
			// With that said, it is preferrable to just stop everything and let the process/container/pod get restarted
			// and we'll try again, until the bus is back up.
			panic(aerr.Error())
		})
	if err != nil {
		panic(err)
	}

	// Signaling bus
	signals, err := config.Get("config.bus.yochbee.signals")
	if err != nil {
		return nil, nil, err
	}
	if strings.TrimSpace(string(signals)) == "" {
		return nil, nil, errors.New("signals bus configuration is missing")
	}
	sc := gjson.ParseBytes(signals)
	sbus, err := NewBusClient(
		sc.Get("connectionString").String(),
		sc.Get("exchange").String(),
		sc.Get("exchangeType").String(),
		sc.Get("durable").Bool(),
		sc.Get("autoDelete").Bool(),
		func(aerr *amqp.Error) {
			panic(aerr.Error())
		})
	if err != nil {
		panic(err)
	}
	return sbus, mbus, nil
}
