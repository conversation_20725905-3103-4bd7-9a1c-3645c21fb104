package rabbitmq

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"yochbee/_base/config"

	"github.com/eliezedeck/gobase/logging"
	"github.com/google/uuid"
	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

func (b *BusClient) Register(method string, handler func(payload []byte) ([]byte, error), parallelProcessing bool) error {
	// We'll use a different channel for each method to separate errors from affecting other methods
	ch, err := b.conn.Channel()
	if err != nil {
		return err
	}
	// Channel will live until the death of this microservice

	// If we're doing parallel processing, we'll allow up to 10 messages to be processed at once
	prefetchCount := 1
	if parallelProcessing {
		prefetchCount = 10
	}
	if err = ch.Qos(prefetchCount, 0, false); err != nil {
		return err
	}

	q, err := ch.QueueDeclare(method, false, false, false, false, nil)
	if err != nil {
		return err
	}
	if b.exchange != "" {
		if err = ch.QueueBind(method, method, b.exchange, false, nil); err != nil {
			return err
		}
	}

	// Start consuming
	d, err := ch.Consume(q.Name, fmt.Sprintf("Golang.Method.Impl:%s-%s", method, uuid.New().String()), false, false, false, false, nil)
	if err != nil {
		return err
	}

	go func() {
		processor :=
			func(livery amqp.Delivery) {
				// If there is an error, this is how we'll return the error message
				respondWithError := func(err error) {
					_ = livery.Ack(false)

					// Respond with the marshalled error message
					errorMsg := rpcResponseExchange{
						Success: false,
						Method:  method,
						Error:   err.Error(),
						Body:    nil,
					}
					jbytes := errorMsg.ToJSON()
					if err := ch.Publish(b.exchange, livery.ReplyTo, false, false, amqp.Publishing{
						CorrelationId: livery.CorrelationId,
						ContentType:   "application/json",
						Timestamp:     time.Now(),
						Body:          jbytes,
					}); err != nil {
						// Normally, this will cause a bus error (OnBusError called)
						// NOT ACKED
						return
					}
				}

				// Recover if there is a panic within the registered function
				defer func() {
					if r := recover(); r != nil {
						err := fmt.Errorf("recovered from method %s: %v", method, r)
						logging.L.Error("Recovered from a Bus method crash", zap.String("method", method), zap.String("errstr", fmt.Sprintf("%v", r)))
						respondWithError(err)
					}
				}()

				response, err := handler(livery.Body) // this could panic
				if err != nil {
					respondWithError(err)
					return
				}

				// Response message marshalling
				msg := rpcResponseExchange{
					Success: true,
					Method:  method,
					Error:   "",
					Body:    response,
				}
				jbytes := msg.ToJSON()
				if err := ch.Publish(b.exchange, livery.ReplyTo, false, false, amqp.Publishing{
					CorrelationId: livery.CorrelationId,
					ContentType:   "application/json",
					Timestamp:     time.Now(),
					Body:          jbytes,
				}); err != nil {
					// Normally, this will cause a bus error (OnBusError called)
					// NOT ACKED
					// FIXME: should the caller retry and we'll deliberately crash the microservice?
					logging.L.Error("Failed publishing back RPC response to its caller", zap.String("method", method), zap.Error(err))
					return
				}

				_ = livery.Ack(false)
			}
		for livery := range d {
			if parallelProcessing {
				go processor(livery)
			} else {
				processor(livery)
			}
		}
	}()
	return nil
}

func (b *BusClient) CallM(method string, payload interface{}, timeout time.Duration) ([]byte, error) {
	marshalled, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	return b.Call(method, marshalled, timeout)
}

func (b *BusClient) Call(method string, payload []byte, timeout time.Duration) ([]byte, error) {
	if method == "" {
		panic("cannot use empty string as RPC method name")
	}
	L := logging.L.Named("rabbitmq-rpc.Call").Named(fmt.Sprintf("(%s)", method)).With(zap.String("method", method))

	if !config.IsRunningInsideKubernetes() {
		debugTimeout := 900 * time.Second
		if timeout < debugTimeout {
			timeout = debugTimeout
		}
	}

	// Create a new channel for this RPC call
	ch, err := b.conn.Channel()
	if err != nil {
		L.Error("Failed creating a new channel for RPC", zap.Error(err))
		return nil, err
	}
	defer ch.Close()

	// Declare a temporary queue with a random unique name
	q, err := ch.QueueDeclare("" /* this is correct! */, false, false, true, false, nil)
	if err != nil {
		L.Error("Failed declaring a temporary queue for RPC", zap.Error(err))
		return nil, err
	}
	L = L.With(zap.String("queue", q.Name))

	// Ensure this queue gets deleted when done
	defer func() {
		if _, err := ch.QueueDelete(q.Name, false, false, true); err != nil {
			L.Error("Failed deleting temporary queue", zap.Error(err))
		}
	}()

	if b.exchange != "" {
		if err = ch.QueueBind(q.Name, q.Name, b.exchange, false, nil); err != nil {
			L.Error("Failed binding temporary queue to exchange", zap.Error(err))
			return nil, err
		}
	}

	// Prepare to get the response back
	consumer := fmt.Sprintf("Golang.Method.Caller:%s(%s)-%s", method, q.Name, uuid.New().String())
	msgs, err := ch.Consume(q.Name, consumer, true /* no need to ACK */, false, false, false, nil)
	if err != nil {
		L.Error("Failed starting consumption from temporary queue", zap.Error(err), zap.String("consumer", consumer))
		return nil, err
	}
	defer func() {
		if err := ch.Cancel(consumer, true); err != nil {
			L.Warn("Failed cancelling consumer", zap.Error(err), zap.String("consumer", consumer))
		}
	}()

	corrId := uuid.New().String()

	// Send the request
	if err := ch.Publish(b.exchange, method, true, false, amqp.Publishing{
		ReplyTo:       q.Name,
		CorrelationId: corrId,
		ContentType:   "application/octet-stream",
		Timestamp:     time.Now(),
		Body:          payload,
		Expiration:    fmt.Sprintf("%d", int64(timeout/time.Millisecond)),
	}); err != nil {
		// Normally, this will cause a bus error (OnBusError called)
		L.Error("Failed publishing RPC request", zap.Error(err))
		return nil, err
	}

	// Start a timer based on the timeout parameter
	timer := time.NewTimer(timeout)
loop:
	for {
		select {
		case <-timer.C:
			L.Error("RPC call has timed-out")
			return nil, errors.New("the RPC call has timed-out")

		case d, ok := <-msgs:
			if !ok {
				// Will drop down below
				break loop
			}

			if corrId == d.CorrelationId {
				// We don't need to do any ACK because this is an auto-ack consumption

				// Got the response back
				response, err := newRpcResponseExchangeFromBytes(d.Body)
				if err != nil {
					L.Error("Failed unmarshalling RPC response", zap.Error(err))
					return nil, err
				}

				if response.Success {
					return response.Body, nil
				}

				// Formulate an error
				L.Error("RPC call produced an error", zap.String("error", response.Error))
				return nil, errors.New(response.Error)
			}
		}
	}

	L.Error("End of RPC call without proper response from RabbitMQ", zap.String("method", method))
	return nil, errors.New("end of RPC call without proper response from RabbitMQ")
}
