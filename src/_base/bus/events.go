package bus

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// Event is something that is published on the signaling bus. All other system should also adhere to this format.
type Event struct {
	Topic            string      `json:"topic"`
	Data             interface{} `json:"data"`
	Time             time.Time   `json:"time"`
	Retrigger        bool        `json:"retrigger"`
	WebhookID        string      `json:"webhookId"`
	WebhookTimestamp int64       `json:"webhookTimestamp"`
}

func ParseEvent(msg []byte) (*Event, error) {
	var event Event
	if err := json.Unmarshal(msg, &event); err != nil {
		return nil, err
	}
	return &event, nil
}

func RegisterBusMethodCallingFromREST(rpc RpcClient) func(echo.Context) error {
	return func(c echo.Context) error {
		if c.Request().Header.Get("Content-Type") != "application/json" {
			return c.String(http.StatusBadRequest, "Send me a JSON body")
		}

		content, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return c.String(http.StatusInternalServerError, err.Error())
		}
		if len(content) == 0 {
			return c.String(http.StatusBadRequest, "Send me a JSON body")
		}

		method := strings.TrimSpace(c.Param("methodName"))
		if method == "" {
			method = "pkg.MethodName"
		}

		timeoutStr := strings.TrimSpace(c.QueryParam("timeoutSeconds"))
		if timeoutStr == "" {
			timeoutStr = "900" // by default, timeout will be a long 900 seconds, enough for debugging
		}
		timeout, err := strconv.ParseInt(timeoutStr, 10, 32)
		if err != nil {
			return c.String(http.StatusInternalServerError, err.Error())
		}

		logging.L.Info("Calling RPC method (manual)", zap.String("method", method), zap.ByteString("content", content), zap.Int64("timeout", timeout))
		br, err := rpc.Call(method, content, time.Duration(timeout)*time.Second)
		if err != nil {
			return c.String(http.StatusInternalServerError, err.Error())
		}

		return c.Blob(http.StatusOK, "application/json", br)
	}
}
