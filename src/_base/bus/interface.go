package bus

import "time"

// Client implementation will allow messages to be published on the bus and to be received to subscribers (those who are
// interested in those messages) by topic. Client is thus like a handle to access resources that go thru the bus, as the
// name implies.
//
// Note that bus implementations can vary wildly, but it is up to the implementation to provide named instances via the
// FX library. See https://pkg.go.dev/go.uber.org/fx#hdr-Named_Values for more information on how to implement this.
//
// In general, there are basically 2 interesting types of buses, "direct" and "fanout". In a "direct" mode,
// implementations will deliver the message to only one subscribers, even though there are multiple subscribers.
type Client interface {
	// DeclareQueue is used to declare that a certain queue with the given name exists on the bus. It is possible and
	// valid that multiple microservices using the particular queue will all call this method to declare their need of
	// that particular queue.
	DeclareQueue(name string, durable, exclusive bool) (string, error)

	// DeclareSubscriptionQueue is used for topic subscription on a "topic" exchange. It automatically binds the queue
	// with the given topics.
	DeclareSubscriptionQueue(name string, durable, exclusive bool, topics []string) (string, error)

	// Publish is used to publish a message on the bus to interested subscribers. Depending on the implementation, it
	// could be a fanout (multiple subscribers will receive the same message), or a direct message delivery (only one
	// subscriber will receive the message). See the documentation for Client for how named instances are right for this
	// kind of use case.
	Publish(msg []byte, contentType, topic string) error

	// PublishM is the same as Publish but will automatically use JSON as the message type and will marshal the given
	// msg data automatically (hence the M suffix).
	PublishM(msg interface{}, topic string) error

	// PublishEvent is similar to PublishM but publishes the given Even with its topic.
	PublishEvent(event *Event) error

	// SubscribeForever is called if you want to consume messages that is available on the queue. The tag has no effect on the
	// consumption, but it is used to identify the consumer; you can specify an empty tag and it will be generated by the
	// implementation.
	SubscribeForever(queue, tag string, exclusive bool, msgHandler func(msg []byte, contentType string, ack func() error)) error

	// SubscribeToEventForever is just like Consume, but it conveniently converts the raw bytes to an *Event.
	SubscribeToEventForever(queue, tag string, exclusive bool, eventHandler func(event *Event, ack func() error)) error

	// CanAutoReconnect will tell if the implementation of the bus client will automatically reconnect or not. The bus
	// should be a solid piece of the architecture, an important foundation that if it crumbles, everything else should
	// not stand. It is thus preferrable that in the event of a bus disfunction, the microservice should shutdown and
	// be restarted automatically until the bus is back up.
	CanAutoReconnect() bool
}

// RpcClient is similar to Client but adds capabilities for making RPC calls over the bus.
//
// Normally, the bus should provide durability to ensure that messages/calls are not lost in the event of application
// crash, or even bus crash (which is extremely rare), but that is still up to the implementation. These clients can
// register arbitrary methods and can return also, arbitrary data.
//
// RPC calls are like HTTP: there is one request, and there is one corresponding response, then it's done. If more
// advanced pattern is required (like request, and n responses), consider the Client instead.
type RpcClient interface {
	// Register registers the method to be callable by any microservice that is connected on the same bus. Method must
	// generally be idempotent, meaning that is should do the right thing even if the same message is given twice,
	// without different result for multiple calls.
	Register(method string, handler func(payload []byte) ([]byte, error), parallelProcessing bool) error

	// Call is used to call any registered method that have been registered with Register, whether it was registered in
	// the same process, or in a different microserver, and even in an implementation from a completely different
	// language.
	Call(method string, payload []byte, timeout time.Duration) ([]byte, error)

	// CallM is similar to normal Call(), but it takes an interface as payload and automatically marshals it to JSON
	// before calling Call()
	CallM(method string, payload interface{}, timeout time.Duration) ([]byte, error)
}
