package random

// Take from https://github.com/eliezedeck/gobase/blob/main/random/strings.go

import (
	crand "crypto/rand"
	"math/big"
	"math/rand"
	"time"
	"unsafe"
)

var (
	src = rand.NewSource(time.Now().UnixNano())
	rng = rand.New(src)
)

const letterBytes = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
const easilyReadableLetterBytes = "*********************************"
const (
	letterIdxBits = 6                    // 6 bits to represent a letter index
	letterIdxMask = 1<<letterIdxBits - 1 // All 1-bits, as many as letterIdxBits
	letterIdxMax  = 63 / letterIdxBits   // # of letter indices fitting in 63 bits
)

// Strings is an extremely fast random string generator.
//
// Copied and adapted from https://stackoverflow.com/questions/22892120/how-to-generate-a-random-string-of-a-fixed-length-in-go
func Strings(n int) string {
	b := make([]byte, n)
	// A src.Int63() generates 63 random bits, enough for letterIdxMax characters!
	for i, cache, remain := n-1, src.Int63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = src.Int63(), letterIdxMax
		}
		if idx := int(cache & letterIdxMask); idx < len(letterBytes) {
			b[i] = letterBytes[idx]
			i--
		}
		cache >>= letterIdxBits
		remain--
	}

	return *(*string)(unsafe.Pointer(&b))
}

func EasilyReadableStrings(n int) string {
	b := make([]byte, n)
	for i, cache, remain := n-1, src.Int63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = src.Int63(), letterIdxMax
		}
		if idx := int(cache & letterIdxMask); idx < len(easilyReadableLetterBytes) {
			b[i] = easilyReadableLetterBytes[idx]
			i--
		}
		cache >>= letterIdxBits
		remain--
	}

	return *(*string)(unsafe.Pointer(&b))
}

func Numbers(low, hi int) int {
	return low + rng.Intn(hi-low)
}

func GenerateSecureNumberSequence(length int) (string, error) {
	secureSequence := ""
	for i := 0; i < length; i++ {
		randomDigit, err := crand.Int(crand.Reader, big.NewInt(10))
		if err != nil {
			return "", err
		}
		secureSequence += randomDigit.String()
	}
	return secureSequence, nil
}
