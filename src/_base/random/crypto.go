package random

import (
	"crypto/rand"
	"math/big"
)

// CryptoStrings returns a random string of the specified length, can be used for crypto
func CryptoStrings(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	charsetLen := big.NewInt(int64(len(charset)))

	// Generate a random string by selecting a random character from the charset
	// for each position in the output string
	output := make([]byte, length)
	for i := range output {
		randomIndex, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", err
		}
		output[i] = charset[randomIndex.Int64()]
	}

	return string(output), nil
}
