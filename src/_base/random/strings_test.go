package random

import "testing"

func TestEasilyReadableStrings(t *testing.T) {
	t.Log(EasilyReadableStrings(5))
	t.Log(EasilyReadableStrings(6))
	t.Log(EasilyReadableStrings(7))
	t.Log(EasilyReadableStrings(8))
	t.Log(EasilyReadableStrings(9))
	t.Log(EasilyReadableStrings(10))
}

func TestGenerateSecureNumberSequence(t *testing.T) {
	t.Log(GenerateSecureNumberSequence(5))
	t.Log(GenerateSecureNumberSequence(6))
	t.Log(GenerateSecureNumberSequence(7))
	t.Log(GenerateSecureNumberSequence(8))
	t.Log(GenerateSecureNumberSequence(9))
	t.Log(GenerateSecureNumberSequence(10))
}
