package lock

import (
	"errors"
	"fmt"
	"yochbee/_base/config"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	goredislib "github.com/redis/go-redis/v9"
	"github.com/tidwall/gjson"
)

type DistributedLocker struct {
	rs *redsync.Redsync
}

func CreateDistributedLocker(config config.Provider) (*DistributedLocker, error) {
	// Get the configuration to the Redis server. It looks like this:
	// {"host":"redis-14482.c3.eu-west-1-2.ec2.cloud.redislabs.com","port":14482,"password":"...","db":0}
	conf, err := config.Get("config.database.redis.locks")
	if err != nil {
		return nil, err
	}
	if conf == nil {
		return nil, errors.New("config.database.redis.locks is missing from ETCD")
	}
	rc := gjson.ParseBytes(conf)
	if !rc.Get("host").Exists() {
		return nil, errors.New("config.database.redis.locks: host not found in configuration")
	}
	if !rc.Get("port").Exists() {
		return nil, errors.New("config.database.redis.locks: port not found in configuration")
	}
	if !rc.Get("password").Exists() {
		return nil, errors.New("config.database.redis.locks: password not found in configuration")
	}
	if !rc.Get("db").Exists() {
		return nil, errors.New("config.database.redis.locks: db not found in configuration")
	}

	// Acquire the configuration values
	host := rc.Get("host").String()
	port := rc.Get("port").Int()
	password := rc.Get("password").String()
	db := rc.Get("db").Int()

	// Create a new pool of connections to Redis
	client := goredislib.NewClient(&goredislib.Options{
		Addr:     fmt.Sprintf("%s:%d", host, port),
		Password: password,
		DB:       int(db),
	})
	pool := goredis.NewPool(client)

	// Create a new Redsync instance with the pool
	rs := redsync.New(pool)

	return &DistributedLocker{rs}, nil
}

func (ds *DistributedLocker) GetMutex(name string, options ...redsync.Option) *redsync.Mutex {
	return ds.rs.NewMutex("wiboost:"+name, options...)
}
