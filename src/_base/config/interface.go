package config

import "context"

// Provider allows the app to get configurations from some place. It also allows the ability to get the value and then
// get updates to new configuration values in the future, if and when they get updated.
type Provider interface {
	// Get returns the configuration associated with the key.
	Get(key string) ([]byte, error)

	// GetLive returns the current configuration associated with the key (via the given onConfigUpdated callback
	// parameter) and will also do so if and when the configuration gets updated in the future.
	//
	// This allows dynamic service without the need to recompile, reconfigure/restart microservices.
	GetLive(ctx context.Context, key string, onConfigUpdated func([]byte)) error
}
