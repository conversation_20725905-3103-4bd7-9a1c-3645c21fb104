package etcd

import (
	"errors"
	"os"
	"strings"
	"time"
	"yochbee/_base/config"
)

// CreateLiveConfig will return a new instance of the config.Provider. The environment variable `ETCD_ENDPOINTS` needs
// to be set before the app start since that is a hard dependency.
//
// Once registered, this implementation can be used to query all other configurations, like the bus, database, logging,
// etc... And that is exactly its purpose.
func CreateLiveConfig() (config.Provider, error) {
	// Take the list of endpoints from the env
	endpointsCommaSeparated := os.Getenv("ETCD_ENDPOINTS")
	if endpointsCommaSeparated == "" {
		return nil, errors.New("you must specify the ETCD_ENDPOINTS env")
	}
	endpoints := strings.Split(endpointsCommaSeparated, ",")
	finalEndpoints := make([]string, 0, len(endpoints))
	for _, e := range endpoints {
		if e != "" {
			finalEndpoints = append(finalEndpoints, strings.TrimSpace(e))
		}
	}

	liveconf, err := NewLiveConfig(finalEndpoints, 3*time.Second)
	if err != nil {
		return nil, err
	}
	return liveconf, nil
}
