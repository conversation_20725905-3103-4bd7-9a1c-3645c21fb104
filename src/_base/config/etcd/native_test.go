package etcd

import (
	"context"
	"testing"
	"time"
)

func TestManualLocal(t *testing.T) {
	liveconf, err := NewLiveConfig([]string{"127.0.0.1:12379"}, 3*time.Second)
	if err != nil {
		panic(err)
	}

	v1, err := liveconf.Get("config.bus.rabbitMqEndpoint")
	if err != nil {
		panic(err)
	}
	t.Logf(".Get result: %s", v1)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	if err = liveconf.GetLive(ctx, "test.nested", func(v2 []byte) {
		t.Logf(".GetLive update: %s", v2)
	}); err != nil {
		panic(err)
	}

	time.Sleep(30 * time.Second)
}
