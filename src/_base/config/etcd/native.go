package etcd

import (
	"bytes"
	"context"
	"sync"
	"sync/atomic"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
)

// LiveConfig is an implementation of the config.Provider interface over etcd. etcd is a well established, well known
// key-value store that is capable of scaling to meet high demands and is capable of reliable replication. See the
// official website https://etcd.io/ if you want more information.
//
// It is capable of live configuration updates (as the name already implies) for really powerful live configuration and
// tuning, without the need to recomplie or restart any microservices. Any configuration can be updated via etcd and
// it is quite possible for multiple microservices to be updated with new configuration on the fly and in realtime,
// without restarting.
type LiveConfig struct {
	client  *clientv3.Client
	timeout time.Duration
}

func NewLiveConfig(endpoints []string, timeout time.Duration) (*LiveConfig, error) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   endpoints,
		DialTimeout: timeout,
	})
	if err != nil {
		return nil, err
	}
	return &LiveConfig{
		client:  cli,
		timeout: timeout,
	}, nil
}

func (l *LiveConfig) Get(key string) ([]byte, error) {
	if key == "" {
		panic("empty key")
	}

	ctx, cancel := context.WithTimeout(context.Background(), l.timeout)
	defer cancel()

	resp, err := l.client.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	// Retrieve values
	if len(resp.Kvs) > 0 {
		return resp.Kvs[0].Value, nil
	}
	return nil, nil
}

func (l *LiveConfig) GetLive(ctx context.Context, key string, onConfigUpdated func([]byte)) error {
	if key == "" {
		panic("empty key")
	}

	current, err := l.Get(key)
	if err != nil {
		return err
	}
	defer onConfigUpdated(current)

	go func() {
		rch := l.client.Watch(ctx, key)

		// For correctness, we'll do a second Get briefly after the watch to ensure that we do have the current value,
		// because there is a possible race condition during the time the watch is started and the initial Get.
		ulock := &sync.Mutex{}
		shield := uint32(0)
		go func() {
			time.Sleep(1 * time.Second)

			if sv := atomic.LoadUint32(&shield); sv == 0 {
				ulock.Lock()
				defer ulock.Unlock()

				// There was no update from Watch received yet
				v, err := l.Get(key)
				if err != nil {
					return
				}
				if bytes.Compare(v, current) != 0 {
					onConfigUpdated(current)
				}
			}
		}()

		for wresp := range rch {
			for _, ev := range wresp.Events {
				atomic.AddUint32(&shield, uint32(1))

				ulock.Lock()
				onConfigUpdated(ev.Kv.Value)
				ulock.Unlock()
			}
		}
	}()

	return nil
}
