package config

// Microservice should be set to the name of the microservice, for example "accounts" or "banking".
//
// Why is this required? It allows different common packages to know which microservice they are running in. This then allows
// them to use the correct configuration values. For example, this allows the "accounts" middlware to automatically get
// an account via the bus, or directly from the database, depending on the configuration.
var Microservice = "microservices"
