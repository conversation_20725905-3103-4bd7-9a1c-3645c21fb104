package _base

import (
	"yochbee/_base/bus/rabbitmq"
	"yochbee/_base/cache"
	"yochbee/_base/config/etcd"
	"yochbee/_base/dbnosql/mongodb"
	"yochbee/_base/lock"
	"yochbee/_base/storage"
	"yochbee/_base/utils"
	"yochbee/_base/web"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/kyc"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/fx"
	"go.uber.org/fx/fxevent"
	"go.uber.org/fx/fxtest"
)

// SetupApp prepares all the dependencies for a microservice (like configuration, bus, database access, etc...) and then
// starts the user-provided userNonBlockingFunctions for preparations.
//
// NOTE: userNonBlockingFunctions must not block, or it will interfere with the startup and app hooks. See
// https://pkg.go.dev/go.uber.org/fx for the implementation of the Dependency Injection system that is used, and on the
// lifecycle of the app. See the web.CreateEchoWebServer for an example of how a service hook is laid out.
//
// The app.Run() method should then be called after the call to this.
func SetupApp(appName string, userNonBlockingFunctions ...interface{}) *fx.App {
	logging.Init()
	utils.SetupMapper()

	// Setup logging for this app
	logging.L = logging.L.Named(appName)

	app := fx.New(
		fx.Provide(
			etcd.CreateLiveConfig,
			rabbitmq.CreateBusClient,
			mongodb.CreateDBAccess,
			web.CreateEchoWebServer,
			storage.CreateDefaultService,
			cache.CreateRedisCacheClient,
			lock.CreateDistributedLocker,

			kyc.SetupProgressCollection,
			banking.SetupAcquiringTokenCollection,
			accounts.SetupAccountsCollection,
			banking.SetupTransactionsCollection,
			accounts.SetupTwoFactorAuthCollection,
			accounts.SetupSCAWalletCollection,
		),
		fx.Invoke(userNonBlockingFunctions...),
		fx.WithLogger(func() fxevent.Logger {
			return &fxevent.ZapLogger{Logger: logging.L}
		}),
	)
	return app
}

func SetupAppWithCustomProvidersAndInvokes(appName string, providers []interface{}, invokes []interface{}) *fx.App {
	logging.Init()
	utils.SetupMapper()

	// Setup logging for this app
	logging.L = logging.L.Named(appName)

	appOptions := []fx.Option{
		fx.Provide(
			etcd.CreateLiveConfig,
			rabbitmq.CreateBusClient,
			mongodb.CreateDBAccess,
			web.CreateEchoWebServer,
			storage.CreateDefaultService,
			cache.CreateRedisCacheClient,
			lock.CreateDistributedLocker,

			kyc.SetupProgressCollection,
			banking.SetupAcquiringTokenCollection,
			accounts.SetupAccountsCollection,
			banking.SetupTransactionsCollection,
			accounts.SetupTwoFactorAuthCollection,
			accounts.SetupSCAWalletCollection,
		),
	}

	// Add provided functions as providers
	if len(providers) > 0 {
		appOptions = append(appOptions, fx.Provide(providers...))
	}

	// Add provided functions to be invoked
	if len(invokes) > 0 {
		appOptions = append(appOptions, fx.Invoke(invokes...))
	}

	appOptions = append(appOptions,
		fx.WithLogger(func() fxevent.Logger {
			return &fxevent.ZapLogger{Logger: logging.L}
		}),
	)

	app := fx.New(appOptions...)
	return app
}

// SetupTestApp is similar to SetupApp but is just for testing purposes
func SetupTestApp(t fxtest.TB, userNonBlockingFunctions ...interface{}) *fxtest.App {
	app := fxtest.New(t,
		fx.Provide(
			etcd.CreateLiveConfig,
			rabbitmq.CreateBusClient,
			mongodb.CreateDBAccess,
			storage.CreateDefaultService,
			cache.CreateRedisCacheClient,
		),
		fx.Invoke(userNonBlockingFunctions...),
	)
	return app
}
