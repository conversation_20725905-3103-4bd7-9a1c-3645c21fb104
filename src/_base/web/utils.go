package web

import (
	"encoding/json"
	"net/url"
	"strings"
	"time"
	"yochbee/_base/bus"
	"yochbee/_base/utils"
	"yochbee/common"
	"yochbee/common/notifications"

	"github.com/labstack/echo/v4"
)

func GetInternationalPhoneNumber(c echo.Context, busRpc bus.RpcClient, phoneNumber string) (string, error) {
	cc := GetCloudflareCountryCode(c)
	raw, err := busRpc.CallM(notifications.Method_ValidateSMSNumber, notifications.ValidateSMSNumberRequest{
		Iso3166CountryCode:              cc,
		LocalOrInternationalPhoneNumber: phoneNumber,
	}, 10*time.Second)
	if err != nil {
		return "", err
	}
	response := notifications.ValidateSMSNumberResponse{}
	if err = json.Unmarshal(raw, &response); err != nil {
		return "", err
	}

	return response.ValidatedStandardPhoneNumber, nil
}

func GetStartEndDates(c echo.Context) (start, end *time.Time, err error, aborted bool) {
	startDateStr := strings.TrimSpace(c.QueryParam("startDate"))
	endDateStr := strings.TrimSpace(c.QueryParam("endDate"))
	if startDateStr != "" {
		sd, err := utils.ParseUnixTimestampInSecondsString(startDateStr)
		if err != nil {
			return nil, nil, Error(c, common.ErrorCodeInvalidTime, "Invalid start date"), true
		}
		start = &sd
	}
	if endDateStr != "" {
		ed, err := utils.ParseUnixTimestampInSecondsString(endDateStr)
		if err != nil {
			return nil, nil, Error(c, common.ErrorCodeInvalidTime, "Invalid end date"), true
		}
		end = &ed
	}
	if start != nil && end != nil && start.After(*end) {
		return nil, nil, Error(c, common.ErrorCodeInvalidTime, "Start date cannot be after end date"), true
	}

	return start, end, nil, false
}

func AddQueryParamToUrl(urll, key, value string) (string, error) {
	u, err := url.Parse(urll)
	if err != nil {
		return "", err
	}

	q := u.Query()
	q.Add(key, value)

	u.RawQuery = q.Encode()
	return u.String(), nil
}

func AddQueryParamsToUrl(urll string, params map[string]string) (string, error) {
	u, err := url.Parse(urll)
	if err != nil {
		return "", err
	}

	q := u.Query()
	for key, value := range params {
		q.Add(key, value)
	}

	u.RawQuery = q.Encode()
	return u.String(), nil
}
