package web

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/_base/utils"
	"yochbee/common"

	"github.com/labstack/echo/v4"
	"github.com/tidwall/sjson"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func OK(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]interface{}{
		"ok": true,
	})
}

func Error(c echo.Context, errCode int, message string) error {
	return c.JSON(http.StatusBadRequest, map[string]interface{}{
		"error": message,
		"code":  errCode,
	})
}

func ErrorWithStatusAndCode(c echo.Context, status int, errCode int, message string) error {
	return c.<PERSON>(status, map[string]interface{}{
		"error": message,
		"code":  errCode,
	})
}

// -----------------------

type MobileJSONer interface {
	ToMobileJSON() ([]byte, error)
}

func SingleEntityForMobile(c echo.Context, entity MobileJSONer, key string) error {
	b, err := entity.ToMobileJSON()
	if err != nil {
		return err // HTTP 500
	}
	return c.JSON(http.StatusOK, map[string]interface{}{
		key: json.RawMessage(b),
	})
}

// -----------------------

// FetchPaginatedEntitiesBasedOnContext fetches paginated entities based on the provided criteria from the HTTP context.
func FetchPaginatedEntitiesBasedOnContext(
	c echo.Context,
	coll dbnosql.DataCollection,
	queryModifier func(builder dbnosql.QueryBuilder),
	preJsonEntriesModifier func(entries []interface{}) []interface{},
	key string,
) ([]interface{}, int64, int64, error) {
	// Acquire the pagination parameters
	page := strings.TrimSpace(c.QueryParam("page"))
	pageSize := strings.TrimSpace(c.QueryParam("pageSize"))
	iPage, _ := strconv.ParseInt(page, 10, 64)
	if iPage < 0 {
		iPage = 0
	}
	iPageSize, _ := strconv.ParseInt(pageSize, 10, 64)
	if iPageSize <= 1 {
		iPageSize = 1
	}
	if iPageSize > 100 {
		iPageSize = 100
	}

	// Acquire the dates parameters (if any)
	startDateStr := strings.TrimSpace(c.QueryParam("startDate"))
	endDateStr := strings.TrimSpace(c.QueryParam("endDate"))
	var startDate *time.Time
	var endDate *time.Time
	if startDateStr != "" {
		sd, err := utils.ParseUnixTimestampInSecondsString(startDateStr)
		if err != nil {
			return nil, 0, 0, Error(c, common.ErrorCodeInvalidTime, "Invalid start date")
		}
		startDate = &sd
	}
	if endDateStr != "" {
		ed, err := utils.ParseUnixTimestampInSecondsString(endDateStr)
		if err != nil {
			return nil, 0, 0, Error(c, common.ErrorCodeInvalidTime, "Invalid end date")
		}
		endDate = &ed
	}
	if startDate != nil && endDate != nil && startDate.After(*endDate) {
		return nil, 0, 0, Error(c, common.ErrorCodeInvalidTime, "Start date cannot be after end date")
	}

	// Prepare the query, step 1: without pagination/limits, applying the user's modifier first
	q := coll.GetQueryBuilder()
	if queryModifier != nil {
		queryModifier(q)
	}
	nativeQuery, err := q.Render() // we only need this, no options needed
	if err != nil {
		return nil, 0, 0, err // HTTP 500
	}
	queryDoc := nativeQuery.(bson.D)

	raw := coll.GetRawCollection().(*mongo.Collection)
	if startDate != nil || endDate != nil {
		filter := bson.M{}
		if startDate != nil {
			filter["$gte"] = startDate
		}
		if endDate != nil {
			filter["$lt"] = endDate
		}
		queryDoc = append(queryDoc, bson.E{Key: "createdAt", Value: filter})
	}

	// Do the counting
	totalCount, err := raw.CountDocuments(context.Background(), queryDoc)
	if err != nil {
		return nil, 0, 0, err // HTTP 500
	}

	// Prepare the query, step 2: with all pagination/limits
	q.SetOffset(iPage * iPageSize)
	q.SetLimit(iPageSize)
	q.SetOrderByField("createdAt", dbnosql.OrderDESC)
	nativeOptions, err := q.RenderFindOptions()
	if err != nil {
		return nil, 0, 0, err // HTTP 500
	}

	// Get and return the matching entries
	cursor, err := raw.Find(context.Background(), queryDoc, nativeOptions.(*options.FindOptions))
	if err != nil {
		return nil, 0, 0, err // HTTP 500
	}
	remainingCount := totalCount - ((iPage + 1) * iPageSize)
	if remainingCount < 0 {
		remainingCount = 0
	}

	// Convert to concrete type
	allocator := coll.GetDocAllocator()
	entries := make([]interface{}, 0)
	for cursor.Next(context.Background()) {
		dest := allocator()
		if err = cursor.Decode(dest); err != nil {
			return nil, 0, 0, err // HTTP 500
		}
		entries = append(entries, dest)
	}

	if preJsonEntriesModifier != nil {
		entries = preJsonEntriesModifier(entries)
	}

	return entries, totalCount, remainingCount, nil
}

func PaginatedEntitiesForMobileJSONFromResult(
	c echo.Context,
	entries []interface{},
	totalCount int64,
	remainingCount int64,
	responseOverrider func(c echo.Context, entries []interface{}, total int64) (overridden bool, err error),
	key string,
) error {
	// If possible, use the .ToMobileJSON() method to convert the entries to JSON
	if len(entries) > 0 {
		if _, ok := entries[0].(MobileJSONer); ok {
			list := []byte("[]")
			for i, entry := range entries {
				b, err := entry.(MobileJSONer).ToMobileJSON()
				if err != nil {
					return err // HTTP 500
				}
				list, err = sjson.SetRawBytes(list, fmt.Sprintf("%d", i), b) // mutate the final list as we iterate
				if err != nil {
					return err // HTTP 500
				}
			}

			// Process the response override, if any
			if responseOverrider != nil {
				if overridden, err := responseOverrider(c, entries, totalCount); overridden || err != nil {
					return err
				}
			}

			return c.JSON(http.StatusOK, map[string]interface{}{
				key:              json.RawMessage(list),
				"totalCount":     totalCount,
				"remainingCount": remainingCount,
			})
		}
	}

	// Process the response override, if any
	if responseOverrider != nil {
		if overridden, err := responseOverrider(c, entries, totalCount); overridden || err != nil {
			return err
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		key:              entries,
		"totalCount":     totalCount,
		"remainingCount": remainingCount,
	})
}

// PaginatedEntitiesForMobile returns paginated entities as JSON.
func PaginatedEntitiesForMobile(
	c echo.Context,
	coll dbnosql.DataCollection,
	queryModifier func(builder dbnosql.QueryBuilder),
	preJsonEntriesModifier func(entries []interface{}) []interface{},
	responseOverrider func(c echo.Context, entries []interface{}, total int64) (overridden bool, err error),
	key string,
) error {
	entries, totalCount, remainingCount, err := FetchPaginatedEntitiesBasedOnContext(c, coll, queryModifier, preJsonEntriesModifier, key)
	if err != nil {
		return err
	}

	// If possible, use the .ToMobileJSON() method to convert the entries to JSON
	if len(entries) > 0 {
		if _, ok := entries[0].(MobileJSONer); ok {
			list := []byte("[]")
			for i, entry := range entries {
				b, err := entry.(MobileJSONer).ToMobileJSON()
				if err != nil {
					return err // HTTP 500
				}
				list, err = sjson.SetRawBytes(list, fmt.Sprintf("%d", i), b) // mutate the final list as we iterate
				if err != nil {
					return err // HTTP 500
				}
			}

			// Process the response override, if any
			if responseOverrider != nil {
				if overridden, err := responseOverrider(c, entries, totalCount); overridden || err != nil {
					return err
				}
			}

			return c.JSON(http.StatusOK, map[string]interface{}{
				key:              json.RawMessage(list),
				"totalCount":     totalCount,
				"remainingCount": remainingCount,
			})
		}
	}

	// Process the response override, if any
	if responseOverrider != nil {
		if overridden, err := responseOverrider(c, entries, totalCount); overridden || err != nil {
			return err
		}
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		key:              entries,
		"totalCount":     totalCount,
		"remainingCount": remainingCount,
	})
}

func MapOfMobileJsoner(c echo.Context, data map[string]interface{}) error {
	newMap := make(map[string]interface{})
	for k, v := range data {
		if v == nil {
			newMap[k] = nil
			continue
		}
		if j, ok := v.(MobileJSONer); ok {
			b, err := j.ToMobileJSON()
			if err != nil {
				return err // HTTP 500
			}
			newMap[k] = json.RawMessage(b)
		} else {
			// Not an AppJsoner, just use the original value
			newMap[k] = v
		}
	}
	return c.JSON(http.StatusOK, newMap)
}

func ListOfMobileJsoner(c echo.Context, key string, list []any) error {
	newList := make([]any, len(list))
	for i, v := range list {
		if v == nil {
			newList[i] = nil
			continue
		}
		if j, ok := v.(MobileJSONer); ok {
			b, err := j.ToMobileJSON()
			if err != nil {
				return err // HTTP 500
			}
			newList[i] = json.RawMessage(b)
		} else {
			// Not an AppJsoner, just use the original value
			newList[i] = v
		}
	}
	return c.JSON(http.StatusOK, map[string]interface{}{
		key: newList,
	})
}

func ListOfMobileJsonerWithoutKey(c echo.Context, list []any) error {
	newList := make([]any, len(list))
	for i, v := range list {
		if v == nil {
			newList[i] = nil
			continue
		}
		if j, ok := v.(MobileJSONer); ok {
			b, err := j.ToMobileJSON()
			if err != nil {
				return err // HTTP 500
			}
			newList[i] = json.RawMessage(b)
		} else {
			// Not an AppJsoner, just use the original value
			newList[i] = v
		}
	}
	return c.JSON(http.StatusOK, newList)
}
