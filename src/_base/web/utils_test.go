package web

import "testing"

func TestAddSCAQueryParam(t *testing.T) {
	urll := "http://example.com/?test=1"
	scaJws := "test_sca_jw=s"

	updatedUrl, err := AddQueryParamToUrl(urll, "sca", scaJws)
	if err != nil {
		t.Fatalf("Error adding query param: %v", err)
	}

	expectedURL := "http://example.com/?sca=test_sca_jw%3Ds&test=1"
	if updatedUrl != expectedURL {
		t.<PERSON>rrorf("Expected URL to be %s, got %s", expectedURL, updatedUrl)
	}
}
