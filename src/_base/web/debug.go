package web

import (
	"bytes"
	"io"
	"yochbee/_base/config"

	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

func DebugPayload(L *zap.Logger, c echo.Context) (io.Reader, error) {
	if config.IsDebugMode() {
		// Gather all the headers that have been sent
		headers := make(map[string]string)
		for k, v := range c.Request().Header {
			headers[k] = v[0]
		}

		L<PERSON>Debug("DEBUG: Request info", zap.String("method", c.Request().Method), zap.String("url", c.Request().URL.String()), zap.Any("headers", headers))
		rawPayload, err := io.ReadAll(c.Request().Body)
		if err != nil {
			return nil, err
		}
		L.Debug("DEBUG: Payload", zap.ByteString("payload", rawPayload), zap.Int("len", len(rawPayload)))
		return bytes.NewReader(rawPayload), nil
	}

	return c.Request().Body, nil
}
