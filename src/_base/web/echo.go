package web

import (
	"context"
	"net/http"
	"time"
	"yochbee/_base/config"
	"yochbee/common"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

// ServerEndpoint can be overridden to a different endpoint if needed. It has to be updated before setting up the app.
var ServerEndpoint = ":8080"

var error500 = map[string]interface{}{
	"error": "An Internal Server Error has occured",
	"code":  common.ErrorInternalError,
}

var (
	EnableRequestsResponsesLogging = true
)

// CreateEchoWebServer will create an instance of echo.Echo which will be used to register routes. The user must not
// start the returned server, but will let FX automatically call the hooks necessary to start the web server.
//
// ServerEndpoint can be used to override where the webserver will listen at.
//
// This is not an interface, simply because it will need to be called against specialized callbacks for route handlers.
// And since every web frameworks have their own signature, implementations and requirements, a change in the web server
// implementation will require refactoring of all route handlers (which cannot be generalized).
//
// No matter how hard that dependency is, a change can always be made: microservices are not limited to just one web
// server, migrations can always be done progressively, if/when needed.
func CreateEchoWebServer(lc fx.Lifecycle) *echo.Echo {
	e := echo.New()
	e.HideBanner = true
	e.HidePort = true
	e.DisableHTTP2 = true

	// Enable request and response logging
	if EnableRequestsResponsesLogging {
		e.Use(middleware.BodyDump(func(c echo.Context, reqBody, resBody []byte) {
			logging.L.Info("Request payload",
				zap.Any("request", string(reqBody)),
				zap.Any("response", string(resBody)),
				// Also log the request and response headers
				zap.Any("request_headers", c.Request().Header),
				zap.Any("response_headers", c.Response().Header()),
			)
		}))
	}

	// Configure middlewares
	e.Use(logging.EchoInjectActivityId)
	e.Use(logging.ZapLoggerForEcho(logging.L))
	e.Use(logging.RecoverWithZapLogging)

	// Enable CORS in case it's used in Web browsers
	e.Use(middleware.CORS())

	// Standardize the returned error message
	e.HTTPErrorHandler = func(err error, c echo.Context) {
		if c.Response().Committed {
			return
		}
		if c.Request().Method == http.MethodHead {
			_ = c.NoContent(http.StatusInternalServerError)
			return
		}
		erro := c.JSON(http.StatusInternalServerError, error500)
		if erro != nil {
			e.Logger.Error(erro)
		}
	}

	// Repelling message ;)
	e.GET("/", func(c echo.Context) error {
		return c.String(http.StatusOK, "hello world")
	})

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			logging.L.Info("Starting ECHO Web server ...", zap.String("endpoint", ServerEndpoint))
			errc := make(chan error, 1)
			go func() {
				if err := e.Start(ServerEndpoint); err != nil {
					errc <- err
				}
			}()
			select {
			case <-time.After(500 * time.Millisecond):
				// After some time, no error ... assume successful Webserver startup
				return nil
			case err := <-errc:
				return err
			}
		},
		OnStop: func(ctx context.Context) error {
			logging.L.Info("Shutting-down ECHO Web server")
			return e.Server.Shutdown(ctx)
		},
	})

	return e
}

func GetCloudflareCountryCode(c echo.Context) string {
	cc := c.Request().Header.Get("CF-IPCountry")
	if cc == "" {
		if config.IsRunningInsideKubernetes() {
			panic("no CF-IPCountry header from Cloudflare")
		}
		cc = "MG" // simulate a Malagasy country code if not running via Cloudflare and Kubernetes
	}
	return cc
}
