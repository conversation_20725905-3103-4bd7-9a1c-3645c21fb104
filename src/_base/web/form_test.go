package web

import (
	"bytes"
	"testing"
	"time"
)

func TestForm2Entity(t *testing.T) {
	type ADto struct {
		Name string `json:"name" validate:"required"`
	}

	type AnEntity struct {
		Name string    `json:"name"`
		Date time.Time `json:"date"`
	}

	formBody := bytes.NewBuffer([]byte(`{"name":"<PERSON><PERSON>"}`))

	e, err := Form2Entity[ADto, AnEntity](formBody, func() (*AnEntity, error) {
		return &AnEntity{
			Date: time.Now(),
		}, nil
	})
	if err != nil {
		t.Error(err)
	}

	if e.Name != "<PERSON><PERSON>ck" {
		t.<PERSON>rf("Expected %s, got %s", "<PERSON><PERSON> Zedeck", e.Name)
	}
}
