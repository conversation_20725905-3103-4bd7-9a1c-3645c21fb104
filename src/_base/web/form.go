package web

import (
	"encoding/json"
	"io"
	json2 "yochbee/_base/json"
)

func Form2Entity[D, E any](formBody io.Reader, authoritativeEntityProvider func() (*E, error)) (*E, error) {
	var mold D

	// Verify that the form matches the mold and all its required fields are satisfied
	// The `validate:...` field tags are used automatically if present
	bytes, err := json2.ValidateBodyStrictInto(formBody, &mold, true)
	if err != nil {
		return nil, err
	}

	// Get the authoritative entity
	entity, err := authoritativeEntityProvider()
	if err != nil {
		return nil, err
	}

	// Apply the clean data to the entity
	if err := json.Unmarshal(bytes, &entity); err != nil {
		return nil, err
	}
	return entity, nil
}
