package json

import (
	"bytes"
	"testing"
)

func TestValidation(t *testing.T) {
	req := struct {
		FirstName        string `json:"firstName"           validate:"required,min=2" mod:"trim"`
		ExtraAddressInfo string `json:"extraAddressInfo"                              mod:"trim"`
		Gender           string `json:"gender"              validate:"required,min=1" mod:"trim"`

		Email string `json:"email" validate:"omitempty,email" mod:"lcase"` // optional
	}{}

	rawJson := []byte(`{
		"firstName": "John ",
		"extraAddressInfo": "  ",
		"gender": " M ",
		"email": "<EMAIL>"
	}`)

	if _, err := ValidateBodyInto(bytes.NewReader(rawJson), &req); err != nil {
		t.Error(err)
	}

	if req.FirstName != "John" {
		t.Error("firstName should not have been trimmed", req.FirstName)
	}
	if req.ExtraAddressInfo != "" {
		t.Error("extraAddressInfo should have been trimmed", req.ExtraAddressInfo)
	}
	if req.Gender != "M" {
		t.Error("Gender should have been trimmed", req.Gender)
	}
	if req.Email != "<EMAIL>" {
		t.Error("Email should have been lowercased", req.Email)
	}
}
