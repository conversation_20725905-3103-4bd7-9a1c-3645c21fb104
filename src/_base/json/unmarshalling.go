package json

import (
	"encoding/json"
	"strings"
	"time"
)

type BirthDate time.Time

const BirthDateLayout = "2006-01-02"

func (j *BirthDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	t, err := time.ParseInLocation(BirthDateLayout, s, time.UTC)
	if err != nil {
		return err
	}
	*j = BirthDate(t)
	return nil
}

func (j BirthDate) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Time(j))
}
