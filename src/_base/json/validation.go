package json

import (
	"context"
	"io"

	"github.com/go-playground/mold/v4/modifiers"
	"github.com/go-playground/validator/v10"
)

var (
	M = modifiers.New()
	V = validator.New()
)

// ValidateBodyStrictInto parses the given JSON from the request, unmarshalls it into the given `dest`. If keep == true, the
// JSON is recreated based on the struct `dest` and returned as `validated`, useful for cleaning-up.
func ValidateBodyStrictInto(jbody io.Reader, dest interface{}, keep ...bool) (validated []byte, err error) {
	// Decode the JSON, not allowing extra fields
	if err := DecodeJSONStrict(jbody, dest); err != nil {
		return nil, err // HTTP 500
	}

	// Mold the data (dependent on `mod` tags)
	if err := M.Struct(context.TODO(), dest); err != nil {
		return nil, err
	}

	// Validate based on the dest struct
	if err := V.Struct(dest); err != nil {
		return nil, err
	}

	if len(keep) == 0 || !keep[0] {
		// user doesn't want the repacked JSON to be returned
		return nil, nil
	}

	// Convert back to a safe JSON
	asjson, err := StructToJSON(dest)
	if err != nil {
		return nil, err // HTTP 500
	}

	return asjson, nil
}

func ValidateBodyRelaxedInto(jbody io.Reader, dest interface{}, keep ...bool) (validated []byte, err error) {
	// Decode the JSON, not allowing extra fields
	if err := DecodeJSONRelaxed(jbody, dest); err != nil {
		return nil, err // HTTP 500
	}

	// Mold the data (dependent on `mod` tags)
	if err := M.Struct(context.Background(), dest); err != nil {
		return nil, err
	}

	// Validate based on the dest struct
	if err := V.Struct(dest); err != nil {
		return nil, err
	}

	if len(keep) == 0 || !keep[0] {
		// user doesn't want the repacked JSON to be returned
		return nil, nil
	}

	// Convert back to a safe JSON
	asjson, err := StructToJSON(dest)
	if err != nil {
		return nil, err // HTTP 500
	}

	return asjson, nil
}
