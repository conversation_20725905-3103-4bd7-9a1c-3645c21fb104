package json

import (
	"bytes"
	"encoding/json"
	"io"

	"github.com/tidwall/sjson"
)

// DecodeJSONStrict decodes into the given `i` while making sure not to allow extra fields that are not present in the
// struct for a strict decoding.
func DecodeJSONStrict(readable io.Reader, i interface{}) error {
	decoder := json.NewDecoder(readable)
	decoder.DisallowUnknownFields()
	return decoder.Decode(i)
}

func DecodeJSONRelaxed(readable io.Reader, i interface{}) error {
	decoder := json.NewDecoder(readable)
	return decoder.Decode(i)
}

func StructToJSON(i interface{}) ([]byte, error) {
	buff := &bytes.Buffer{}
	encoder := json.NewEncoder(buff)
	if err := encoder.Encode(i); err != nil {
		return nil, err
	}
	return buff.Bytes(), nil
}

func <PERSON><PERSON><PERSON>ields(o interface{}, fieldPaths ...string) ([]byte, error) {
	m, err := json.Marshal(o)
	if err != nil {
		return nil, err
	}

	for _, f := range fieldPaths {
		m, err = sjson.DeleteBytes(m, f)
		if err != nil {
			return nil, err
		}
	}
	return m, nil
}
