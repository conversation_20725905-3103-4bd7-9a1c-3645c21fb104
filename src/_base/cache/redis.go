package cache

import (
	"errors"
	"fmt"
	"yochbee/_base/config"

	goredis "github.com/redis/go-redis/v9"
	"github.com/tidwall/gjson"
)

func CreateRedisCacheClient(config config.Provider) (*goredis.Client, error) {
	conf, err := config.Get("config.database.redis")
	if err != nil {
		return nil, err
	}
	if conf == nil {
		return nil, errors.New("config.database.redis is missing from ETCD")
	}
	rc := gjson.ParseBytes(conf)
	if !rc.Get("host").Exists() {
		return nil, errors.New("config.database.redis: host not found in configuration")
	}
	if !rc.Get("port").Exists() {
		return nil, errors.New("config.database.redis: port not found in configuration")
	}
	if !rc.Get("password").Exists() {
		return nil, errors.New("config.database.redis: password not found in configuration")
	}
	if !rc.Get("db").Exists() {
		return nil, errors.New("config.database.redis: db not found in configuration")
	}

	// Acquire the configuration values
	host := rc.Get("host").String()
	port := rc.Get("port").Int()
	password := rc.Get("password").String()
	db := rc.Get("db").Int()

	// Create a new pool of connections to Redis
	client := goredis.NewClient(&goredis.Options{
		Addr:     fmt.Sprintf("%s:%d", host, port),
		Password: password,
		DB:       int(db),
	})

	return client, nil
}
