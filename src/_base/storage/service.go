package storage

import (
	"yochbee/_base/config"

	"github.com/eliezedeck/gobase/azure"
	"github.com/tidwall/gjson"
)

func CreateDefaultService(config config.Provider) (*azure.BlobService, error) {
	c, err := config.Get("storage.default")
	if err != nil {
		return nil, err
	}
	configs := gjson.GetManyBytes(c, "accountName", "accountKey")
	if !configs[0].Exists() || !configs[1].Exists() {
		panic("incomplete configuration for `storage.default`")
	}

	return azure.NewBlobService(configs[0].String(), configs[1].String())
}
