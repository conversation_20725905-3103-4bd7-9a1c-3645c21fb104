package utils

import "encoding/base64"

// DecodeTreezorBase64 decodes a string that may be encoded in either standard or URL-safe base64.
func DecodeTreezorBase64(s string) ([]byte, error) {
	result, err := base64.URLEncoding.DecodeString(s)
	if err != nil {
		result, err = base64.StdEncoding.DecodeString(s)
		if err != nil {
			result, err = base64.RawURLEncoding.DecodeString(s)
			if err != nil {
				result, err = base64.RawStdEncoding.DecodeString(s)
				if err != nil {
					return nil, err
				}
			}
		}
	}
	return result, nil
}
