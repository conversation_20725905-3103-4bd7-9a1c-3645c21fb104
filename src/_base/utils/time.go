package utils

import (
	"strconv"
	"strings"
	"time"
)

func ParseUnixTimestampInSecondsString(ts string) (time.Time, error) {
	i, err := strconv.ParseInt(ts, 10, 64)
	if err != nil {
		return time.Time{}, err
	}
	return time.Unix(i, 0), nil
}

type DynamicFormatTime struct {
	time.Time
}

const DateTimeFormat = "2006-01-02 15:04:05"
const DateTimeFormat2 = "2006-01-02T15:04:05Z07:00"

var htLayouts = []string{
	"2006-01-02 15:04:05",
	"2006-01-02T15:04:05Z07:00",
}

func (ct *DynamicFormatTime) UnmarshalJSON(b []byte) (err error) {
	s := strings.Trim(string(b), "\"")
	s = strings.TrimSpace(s)
	if s == "null" {
		ct.Time = time.Time{}
		return
	}
	for _, layout := range htLayouts {
		ct.Time, err = time.Parse(layout, s)
		if err == nil {
			return
		}
	}
	return
}
