package notifications

import (
	"time"
)

type Notification struct {
	ID            string `json:"id" bson:"_id"`
	UserID        string `json:"userId" bson:"userId"`
	CardID        string `json:"cardId" bson:"cardId"`
	CardTxAuthID  string `json:"cardTxAuthId" bson:"cardTxAuthId"`
	BeneficiaryID string `json:"beneficiaryId" bson:"beneficiaryId"`
	Title         string `json:"title" bson:"title"`
	Message       string `json:"message" bson:"message"`
	IsRead        bool   `json:"isRead" bson:"isRead"`

	// SDDR-specific fields
	SepaSDDRID         string `json:"sepaSDDRId,omitempty" bson:"sepaSDDRId,omitempty"`
	SepaTransactionID  string `json:"sepaTransactionId,omitempty" bson:"sepaTransactionId,omitempty"`
	SepaMandateID      string `json:"sepaMandateId,omitempty" bson:"sepaMandateId,omitempty"`
	SepaAmount         string `json:"sepaAmount,omitempty" bson:"sepaAmount,omitempty"`
	SepaReasonCode     string `json:"sepaReasonCode,omitempty" bson:"sepaReasonCode,omitempty"`
	SepaReasonText     string `json:"sepaReasonText,omitempty" bson:"sepaReasonText,omitempty"`
	SepaCreditorName   string `json:"sepaCreditorName,omitempty" bson:"sepaCreditorName,omitempty"`
	SepaCollectionDate string `json:"sepaCollectionDate,omitempty" bson:"sepaCollectionDate,omitempty"`
	NotificationType   string `json:"notificationType,omitempty" bson:"notificationType,omitempty"`

	CreatedAt time.Time  `json:"createdAt" bson:"createdAt"`
	UpdatedAt *time.Time `json:"updatedAt" bson:"updatedAt"`
}
