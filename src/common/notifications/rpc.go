package notifications

// =============

const Method_SendSMS = "notifications.SendSMS"

type SendSMSRequest struct {
	ToPhoneNumber string `json:"toPhoneNumber"`
	Body          string `json:"body"`
}

// =============

const Method_ValidateSMSNumber = "notifications.ValidateSMSNumber"

type ValidateSMSNumberRequest struct {
	// Iso3166CountryCode is like FR, MG, GB, etc... see https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
	Iso3166CountryCode              string `json:"countryCode"`
	LocalOrInternationalPhoneNumber string `json:"localOrInternationalPhoneNumber"`
}

type ValidateSMSNumberResponse struct {
	ValidatedStandardPhoneNumber string `json:"validatedStandardPhoneNumber"`
	CountryIso3166               string `json:"countryIso3166"`
}

// =============

const Method_SendEmailNoReply = "notifications.SendEmailNoReply"

type SendEmailNoReplyRequest struct {
	ToEmailAddresses []string `json:"toEmailAddresses"`
	Subject          string   `json:"subject"`
	ContentType      string   `json:"contentType"`
	Body             string   `json:"body"`
}

type SendEmailNoReplyResponse struct {
	Ok bool `json:"ok"`
}

// =============

const Method_SendEmailFrom = "notifications.SendEmailFrom"

type SendEmailFromRequest struct {
	ToEmailAddresses []string `json:"toEmailAddresses"`
	FromEmailAddress string   `json:"fromEmailAddress"`
	Subject          string   `json:"subject"`
	ContentType      string   `json:"contentType"`
	Body             string   `json:"body"`
}

type SendEmailFromResponse struct {
	Ok bool `json:"ok"`
}
