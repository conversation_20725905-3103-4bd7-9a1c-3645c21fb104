package notifications

// GetFrenchReasonText returns a user-friendly French explanation for SEPA reason codes
func GetFrenchReasonText(reasonCode string) string {
	reasons := map[string]string{
		// Most common reason codes
		"AM04": "Provisions insuffisantes",
		"AG01": "Transaction interdite sur ce type de compte",
		"AC04": "Compte fermé",
		"AC06": "Compte bloqué",
		"MD07": "Titulaire du compte décédé",
		"MS03": "Raison non spécifiée",
		"MS02": "Prélèvement bloqué par le payeur",

		// Additional common codes
		"AC01": "Format de compte incorrect",
		"AC05": "Compte fermé",
		"AC13": "Type de compte invalide pour le débiteur",
		"AG02": "Transaction ou format de fichier invalide",
		"AM05": "Prélèvement en double",
		"BE01": "L'identifiant du client ne correspond pas au numéro de compte",
		"FF01": "Format du fichier invalide",
		"MD01": "Aucun mandat",
		"MD02": "Informations du mandat manquantes ou incorrectes",
		"MD06": "Prélèvement contesté par le débiteur",
		"RC01": "Code BIC invalide",
		"RR01": "Spécification réglementaire manquante",
		"RR02": "Spécification réglementaire manquante dans le type d'information structuré",
		"RR03": "Spécification réglementaire manquante dans le nom du créancier",
		"RR04": "Raison réglementaire",
		"SL01": "Service spécifique offert par la banque du débiteur",
		"TM01": "L'heure limite a été dépassée",

		// Privacy protection code
		"FOCR": "Instructions de suivi annulées",
		"LEGL": "Raison légale",
		"NARR": "Raison fournie dans les informations supplémentaires",
	}

	if text, exists := reasons[reasonCode]; exists {
		return text
	}

	// Default fallback for unknown codes
	return "Raison technique: " + reasonCode
}
