package accounts

import "time"

type Account2FAEntry struct {
	ID             string     `bson:"_id"             json:"id"`
	AccountId      string     `bson:"accountId"       json:"accountId"`
	OTPSalt        []byte     `bson:"otpSalt"         json:"otpSalt"`
	OTPEncrypted   []byte     `bson:"otpEncrypted"    json:"otpEncrypted"`
	ExpiresOn      time.Time  `bson:"expiresOn"       json:"expiresOn"`
	FailedAttempts int        `bson:"failedAttempts"  json:"failedAttempts"`
	LastAttempt    *time.Time `bson:"lastAttempt"     json:"lastAttempt"`
	InvalidatedAt  *time.Time `bson:"invalidatedAt"   json:"invalidatedAt"`
	SuccessfulAt   *time.Time `bson:"successfulAt"    json:"successfulAt"`
	CreatedAt      time.Time  `bson:"createdAt"       json:"createdAt"`
}
