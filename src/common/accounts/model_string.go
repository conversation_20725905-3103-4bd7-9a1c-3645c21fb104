// Code generated by "stringer -type=SCAWalletStatus -output=model_string.go"; DO NOT EDIT.

package accounts

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[SCAWalletStatus_Unknown-0]
	_ = x[SCAWalletStatus_Created-1]
	_ = x[SCAWalletStatus_Activated-2]
}

const _SCAWalletStatus_name = "SCAWalletStatus_UnknownSCAWalletStatus_CreatedSCAWalletStatus_Activated"

var _SCAWalletStatus_index = [...]uint8{0, 23, 46, 71}

func (i SCAWalletStatus) String() string {
	if i < 0 || i >= SCAWalletStatus(len(_SCAWalletStatus_index)-1) {
		return "SCAWalletStatus(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _SCAWalletStatus_name[_SCAWalletStatus_index[i]:_SCAWalletStatus_index[i+1]]
}
