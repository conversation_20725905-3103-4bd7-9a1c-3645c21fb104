package accounts

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"strings"
	"time"
	"yochbee/_base/config"
	"yochbee/_base/web"
	"yochbee/common"

	"github.com/golang-jwt/jwt"
	"github.com/labstack/echo/v4"
	goredis "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

func (a *Account) GenerateJWTToken(accountsColl *AccountsCollection, duration time.Duration) (string, error) {
	now := time.Now()
	token := jwt.New(jwt.GetSigningMethod("HS256"))
	claims := token.Claims.(jwt.MapClaims)
	claims["yochbee"] = map[string]interface{}{
		"app": true,
	}

	if config.IsDebugMode() {
		// Expires in 7 days
		claims["exp"] = now.Add(time.Hour * 24 * 7).Unix()
	} else {
		// Expires at ...
		claims["exp"] = now.Add(duration).Unix()
	}
	// Issued on ...
	claims["iap"] = now.Unix() - 1
	claims["sub"] = a.ID

	// Sign
	strToken, err := token.SignedString(JwtSecret)
	if err != nil {
		return "", err
	}

	// Record the latest SHA 256 sum of the JWT in the account (on the DB)
	if accountsColl != nil {
		hasher := sha256.New()
		hasher.Write([]byte(strToken))
		latestSHASUM256 := hex.EncodeToString(hasher.Sum(nil))
		if err = accountsColl.UpdateLastJWTSignature(a.ID, latestSHASUM256); err != nil {
			return "", err
		}
	}

	return token.SignedString(JwtSecret)
}

func (a *Account) Generate2FAJWTToken(cache *goredis.Client, tags []string) (string, error) {
	now := time.Now()
	token := jwt.New(jwt.GetSigningMethod("HS256"))
	claims := token.Claims.(jwt.MapClaims)
	claims["yochbee"] = map[string]interface{}{
		"2fa": true,
	}

	// Check the Redis cache to see if the user has any tags that we want to be put in the 2FA JWT token
	key := "2fa:neededTags:" + a.ID
	neededTagsJson, err := cache.Get(context.Background(), key).Result()
	if err != nil && err != goredis.Nil {
		return "", errors.New("error retrieving needed tags from cache: " + err.Error())
	}
	if neededTagsJson != "" {
		var neededTags []string
		err = json.Unmarshal([]byte(neededTagsJson), &neededTags)
		if err != nil {
			return "", errors.New("error unmarshalling needed tags from cache: " + err.Error())
		}
		neededTags = append(neededTags, tags...)
		tags = neededTags

		// Delete the key from the cache
		if err = cache.Del(context.Background(), key).Err(); err != nil {
			return "", errors.New("error deleting needed tags from cache: " + err.Error())
		}
	}

	if len(tags) > 0 {
		tagsmap := make(map[string]interface{}, len(tags))
		for _, tag := range tags {
			tagsmap[strings.TrimSpace(tag)] = true
		}
		claims["yochbee"].(map[string]interface{})["tags"] = tagsmap
	}

	if config.IsDebugMode() {
		// Expires in 24 hours
		claims["exp"] = now.Add(time.Hour * 24).Unix()
	} else {
		// Expires in 30 minutes
		claims["exp"] = now.Add(time.Minute * 30).Unix()
	}
	// Issued on ...
	claims["iap"] = now.Unix() - 1
	claims["sub"] = a.ID

	// The secret that will be used will be JwtSecret suffixed with the text "2fa"
	twoFASecret := append(JwtSecret, []byte("2fa")...)
	return token.SignedString(twoFASecret)
}

func (a *Account) NeedTagsOnNext2FA(cache *goredis.Client, tags []string) error {
	// This key will contain the desired tags that will be put in the 2FA JWT token when the user passes the 2FA challenge.
	// The key will be in the form of "2fa:neededTags:<accountid>". If this tag already exists, more tags will be added to it.
	// The value will be a JSON array of strings.
	// The key will expire in 30 minutes.
	// The key will be deleted when the user passes the 2FA challenge.
	key := "2fa:neededTags:" + a.ID

	// Get the value of the key if it exists
	var existingTags []string
	existingTagsJSON, err := cache.Get(context.Background(), key).Result()
	if err != nil && err != goredis.Nil {
		return err
	}
	if existingTagsJSON != "" {
		if err := json.Unmarshal([]byte(existingTagsJSON), &existingTags); err != nil {
			return err
		}
	}

	// Add the new tags to the existing tags
	for _, tag := range tags {
		tag = strings.TrimSpace(tag)
		if tag != "" {
			existingTags = append(existingTags, tag)
		}
	}
	// Remove duplicates
	for i := 0; i < len(existingTags); i++ {
		for j := i + 1; j < len(existingTags); j++ {
			if existingTags[i] == existingTags[j] {
				existingTags = append(existingTags[:j], existingTags[j+1:]...)
				j--
			}
		}
	}

	// Set the key in Redis after converting the tags to JSON
	existingTagsJSONbytes, err := json.Marshal(existingTags)
	if err != nil {
		return err
	}
	if err = cache.Set(context.Background(), key, existingTagsJSONbytes, time.Minute*30).Err(); err != nil {
		return err
	}

	return nil
}

func (a *Account) Verify2FAJWTToken(tokenString string) (bool, []string, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// The secret that will be used will be JwtSecret suffixed with the text "2fa"
		twoFASecret := append(JwtSecret, []byte("2fa")...)
		return twoFASecret, nil
	})
	if err != nil {
		return false, nil, err
	}
	if !token.Valid {
		return false, nil, err
	}
	claims := token.Claims.(jwt.MapClaims)

	// Verify that it is truly owned by the account
	sub, ok := claims["sub"].(string)
	if !ok {
		return false, nil, err
	}
	if sub != a.ID {
		return false, nil, err
	}

	// Verify that it has the {"yochbee": {"2fa": true}} claim
	yochbee, ok := claims["yochbee"].(map[string]interface{})
	if !ok {
		return false, nil, err
	}
	twoFA, ok := yochbee["2fa"].(bool)
	if !ok {
		return false, nil, err
	}
	if !twoFA {
		return false, nil, err
	}

	// Extract all the tags from the yochbee claim and convert the map to a []string
	var tags []string
	if tagsmap, ok := yochbee["tags"].(map[string]interface{}); ok {
		tags = make([]string, 0, len(tagsmap))
		for tag := range tagsmap {
			tags = append(tags, tag)
		}
	}

	return true, tags, nil
}

func (a *Account) HasVerified2FATokenFromRequest(L *zap.Logger, c echo.Context) (responded bool, tags []string, err error) {
	// Get the 2FA JWT from the `X-Yochbee-2FA-Token` header.
	// NOTE: This is directly a token, and does not begin with `Bearer `.
	twoFaToken := strings.TrimSpace(c.Request().Header.Get("X-Yochbee-2FA-Token"))
	if twoFaToken == "" {
		L.Warn("2FA token is empty")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}

	token, err := jwt.Parse(twoFaToken, func(token *jwt.Token) (interface{}, error) {
		// The secret that will be used will be JwtSecret suffixed with the text "2fa"
		twoFASecret := append(JwtSecret, []byte("2fa")...)
		return twoFASecret, nil
	})
	if err != nil {
		L.Error("2FA token is invalid", zap.Error(err))
		// Do not report this as an error (don't want to cause 500 error)
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}
	if !token.Valid {
		L.Warn("2FA token is invalid")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}
	claims := token.Claims.(jwt.MapClaims)

	// Verify that it is truly owned by the account
	sub, ok := claims["sub"].(string)
	if !ok {
		L.Warn("2FA token is not owned by the account")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}
	if sub != a.ID {
		L.Warn("2FA token is not owned by the account")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}

	// Verify that it has the {"yochbee": {"2fa": true}} claim
	yochbee, ok := claims["yochbee"].(map[string]interface{})
	if !ok {
		L.Warn("2FA token is not a valid token (no yochbee claim)")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}
	twoFA, ok := yochbee["2fa"].(bool)
	if !ok {
		L.Warn("2FA token is not a valid token (no 2fa claim)")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}
	if !twoFA {
		L.Warn("2FA token is not a valid token (2fa claim is not true)")
		return true, nil, web.Error(c, common.ErrorCode2FATokenRequired, "2FA token is required")
	}

	// Extract all the tags from the yochbee claim and convert the map to a []string
	if tagsmap, ok := yochbee["tags"].(map[string]interface{}); ok {
		tags = make([]string, 0, len(tagsmap))
		for tag := range tagsmap {
			tags = append(tags, tag)
		}
	}

	return false, tags, nil
}
