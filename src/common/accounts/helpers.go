package accounts

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

var (
	randomSource = rand.NewSource(time.Now().UnixNano())
	rng = rand.New(randomSource)
)

func GenerateReferralCode(firstName, lastName string) string {
	firstName = strings.ToUpper(firstName)
	lastName = strings.ToUpper(lastName)
	firstNameLen := len(firstName)
	lastNameLen := len(lastName)
	randStr := fmt.Sprintf("%d", ********+rng.Int63())

	if firstNameLen > 2 {
		firstName = firstName[:2]
	} else {
		firstName = firstName + randStr[3:3+(2-lastNameLen)]
	}
	if lastNameLen > 2 {
		lastName = lastName[:2]
	} else {
		lastName = lastName + randStr[6:6+(2-lastNameLen)]
	}

	return firstName + randStr[:4] + lastName
}
