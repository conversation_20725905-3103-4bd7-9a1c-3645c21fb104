package accounts

import (
	"errors"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/config"
	"yochbee/_base/dbnosql"
	"yochbee/_base/web"
	"yochbee/common"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// MiddlewareRequireAccountId verifies that the current accountId matches the given JWT token.
func MiddlewareRequireAccountId(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		accountId := strings.TrimSpace(c.Param("accountId"))
		if !IsRequestReallyFromUser(c, accountId) {
			return common.ReturnInvalidJWTTokenResponse(c)
		}

		// Set the accountId in the context
		c.Set("accountId", accountId)

		return next(c)
	}
}

// MiddlewareRetrieveAccount is the next step after the MiddlewareRequireAccountId, meaning that you need to use that
// middleware before this one. It will automatically get the Account from the DB or from the bus, depending on the
// microservice.
func MiddlewareRetrieveAccount(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		L := logging.L.Named("MiddlewareRetrieveAccount")

		// Retrieve the existing account
		accountId := strings.TrimSpace(c.Param("accountId"))
		if accountId == "" {
			L.Error("accountId is empty")
			return common.ReturnInvalidAPICallResponse(c)
		}

		var (
			err     error
			account *Account
		)

		if config.Microservice == "accounts" {
			// Get the accountsColl from the context (this was set by a middleware before the accounts routes setup)
			accountsColl := c.Get("accountsColl").(dbnosql.DataCollection)
			account, err = dbnosql.GetOneByID[*Account](accountsColl, accountId)
			if err != nil {
				L.Error("Error retrieving account directly from the DB", zap.Error(err))
				return err // HTTP 500
			}
		} else {
			busrpc, ok := c.Get("busrpc").(bus2.RpcClient)
			if !ok {
				L.Error("busrpc not found in context")
				return errors.New("busrpc not found in context")
			}
			result, err := bus2.CallMethodMU[GetResponse](busrpc, Method_Get, &GetRequest{
				AccountId: accountId,
			}, 5*time.Second)
			if err != nil {
				L.Error("Error retrieving account from the bus", zap.Error(err))
				return err // HTTP 500
			}
			account = result.Account
		}

		if account == nil {
			L.Error("Account not found", zap.String("accountId", accountId))
			return web.Error(c, common.ErrorCodeAccountNotFound, "Account not found")
		}

		c.Set("account", account)
		c.Set("accountId", accountId)
		c.Set("treezorUserId", account.TreezorUserId)
		return next(c)
	}
}
