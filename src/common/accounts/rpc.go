package accounts

import (
	"encoding/json"
	"time"
	bus2 "yochbee/_base/bus"
)

// =============

const Method_Get = "accounts.Get"

type GetRequest struct {
	AccountId string `json:"accountId"`
}

type GetResponse struct {
	Account *Account `json:"account"`
}

func Get(busrpc bus2.RpcClient, accountId string) (*Account, error) {
	b, err := busrpc.CallM(Method_Get, &GetRequest{AccountId: accountId}, 5*time.Second)
	if err != nil {
		return nil, err
	}
	r := GetResponse{}
	if err := json.Unmarshal(b, &r); err != nil {
		return nil, err
	}
	return r.Account, nil
}

// =============

const Method_GetByEmail = "accounts.GetInfo"

type GetByEmailResponse struct {
	Account *Account `json:"account"`
}

type GetByEmailRequest struct {
	Email string `json:"email"`
}

// =============

const Method_GetByTreezorUserId = "accounts.GetByTreezorUserId"

type GetByTreezorUserIdRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
}

type GetByTreezorUserIdResponse struct {
	Account *Account `json:"account"`
}

func GetByTreezorUser(busrpc bus2.RpcClient, treezorUserId int64) (*Account, error) {
	result, err := bus2.CallMethodMU[GetByTreezorUserIdResponse](busrpc, Method_GetByTreezorUserId, &GetByTreezorUserIdRequest{TreezorUserId: treezorUserId}, 5*time.Second)
	if err != nil {
		return nil, err
	}
	return result.Account, nil
}

// =============

const Method_GetByTreezorPrimaryWalletId = "accounts.GetByTreezorPrimaryWalletId"

type GetByTreezorPrimaryWalletIdRequest struct {
	TreezorPrimaryWalletId int64 `json:"primaryWalletId"`
}

type GetByTreezorPrimaryWalletIdResponse struct {
	Account *Account `json:"account"`
}

func GetByTreezorPrimaryWalletId(busrpc bus2.RpcClient, primaryWalletId int64) (*Account, error) {
	result, err := bus2.CallMethodMU[GetByTreezorPrimaryWalletIdResponse](busrpc, Method_GetByTreezorPrimaryWalletId, &GetByTreezorPrimaryWalletIdRequest{TreezorPrimaryWalletId: primaryWalletId}, 5*time.Second)
	if err != nil {
		return nil, err
	}
	return result.Account, nil
}
