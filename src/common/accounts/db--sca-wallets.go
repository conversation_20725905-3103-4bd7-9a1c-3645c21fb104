package accounts

import "yochbee/_base/dbnosql"

type SCAWalletCollection struct {
	dbnosql.DataCollection
}

func SetupSCAWalletCollection(db dbnosql.Database) (*SCAWalletCollection, error) {
	coll := db.DeclareCollection("accounts-sca-wallets", func(account interface{}) interface{} {
		return account
	}, func() (destination interface{}) {
		return &SCAWalletEntry{}
	})

	if err := coll.SetMultipleIndexesOn("accountId", "treezorUserId"); err != nil {
		return nil, err
	}

	return &SCAWalletCollection{coll}, nil
}

func (w *SCAWalletCollection) GetOneForAccountId(accountId string) (doc *SCAWalletEntry, err error) {
	query := w.GetQueryBuilder()
	if err = query.Set(&dbnosql.Condition{"accountId", "==", accountId}); err != nil {
		return nil, err
	}

	docs, err := w.Find(query)
	if err != nil {
		return nil, err
	}

	if len(docs) == 0 {
		return nil, nil
	}

	return docs[0].(*SCAWalletEntry), nil
}
