package accounts

import (
	"context"
	"errors"
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/_base/random"
	"yochbee/_base/security"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// These errors correspond to the errors returned by the 2FA service (see errorcodes.go)
var (
	Err2FATemporaryBlocked = errors.New("2FA temporary blocked")
	Err2FARequired         = errors.New("2FA required")
	Err2FAIncorrect        = errors.New("2FA incorrect")
	ErrSCARequired         = errors.New("SCA required")
)

type TwoFactorAuthCollection struct {
	dbnosql.DataCollection
}

func SetupTwoFactorAuthCollection(db dbnosql.Database) (*TwoFactorAuthCollection, error) {
	coll := db.DeclareCollection("accounts-2fa", func(account interface{}) interface{} {
		return account
	}, func() (destination interface{}) {
		return &Account2FAEntry{}
	})

	if err := coll.SetMultipleIndexesOn("accountId", "invalidatedAt", "createdAt"); err != nil {
		return nil, err
	}

	return &TwoFactorAuthCollection{coll}, nil
}

func (c *TwoFactorAuthCollection) Set2FAForAccount(accountId, password, otpPlain string) (*Account2FAEntry, error) {
	// Invalidate any existing 2FA for this account
	if err := c.Invalidate2FAEntriesForAccount(accountId); err != nil {
		return nil, err
	}

	// Find any 2FA entries that have been created in the last hour, newest first
	r := c.GetRawCollection().(*mongo.Collection)
	result, err := r.Find(context.Background(), bson.M{
		"accountId": accountId,
		"createdAt": bson.M{"$gte": time.Now().Add(-1 * time.Hour)},
	}, options.Find().SetSort(bson.M{
		"createdAt": -1,
	}))
	if err != nil {
		return nil, err
	}

	// Detect if we should block the user from creating a new 2FA entry or not
	count := 0
	consecutiveFailed := 0
	for result.Next(context.Background()) {
		var entry Account2FAEntry
		if err := result.Decode(&entry); err != nil {
			return nil, err
		}

		// If the latest entry was successful, immediately allow another 2FA
		if count == 0 && entry.SuccessfulAt != nil {
			break
		}

		if entry.SuccessfulAt == nil && entry.InvalidatedAt != nil {
			consecutiveFailed++

			// If there are any 3 consecutive entries that have failed in the last hour, block the star until time passes
			if consecutiveFailed >= 3 {
				return nil, Err2FATemporaryBlocked
			}
		} else {
			consecutiveFailed = 0
		}

		count++
	}

	// Encrypt the OTP
	otpSalt, otpEncrypted, err := c.EncryptOTP(password, otpPlain)
	if err != nil {
		return nil, err
	}

	// Create a new 2FA entry
	now := time.Now()
	entry := &Account2FAEntry{
		ID:           primitive.NewObjectID().Hex(),
		AccountId:    accountId,
		OTPSalt:      otpSalt,
		OTPEncrypted: otpEncrypted,
		ExpiresOn:    now.Add(30 * time.Minute),
		CreatedAt:    now,
	}
	if _, err = c.Insert(entry); err != nil {
		return nil, err
	}

	return entry, nil
}

func (c *TwoFactorAuthCollection) Invalidate2FAEntriesForAccount(accountId string) error {
	r := c.GetRawCollection().(*mongo.Collection)

	// Suppress all `otpEncrypted` values
	now := time.Now()
	_, err := r.UpdateMany(context.Background(), bson.M{
		"accountId":     accountId,
		"invalidatedAt": nil,
	}, bson.M{
		"$set": bson.M{
			"otpSalt":      "",
			"otpEncrypted": "",
			"invalidateAt": now,
			"expiresOn":    now,
		},
	})
	return err
}

func (c *TwoFactorAuthCollection) EncryptOTP(password, otpPlain string) (salt, encryptedOtp []byte, err error) {
	// Generate a random salt
	ssalt, err := random.CryptoStrings(16)
	if err != nil {
		return nil, nil, err
	}

	// Derive the key with the salt
	key, err := security.DeriveKey(password, ssalt)
	if err != nil {
		return nil, nil, err
	}

	// Encrypt the OTP
	encrypted, err := security.Encrypt(key, otpPlain)
	if err != nil {
		return nil, nil, err
	}

	return []byte(ssalt), encrypted, nil
}

func (c *TwoFactorAuthCollection) Challenge2FAForAccount(accountId, password string, otp string) (string, error) {
	// Get the latest active 2FA entry for this account
	r := c.GetRawCollection().(*mongo.Collection)
	entry := &Account2FAEntry{}
	if err := r.FindOne(context.Background(), bson.M{
		"accountId":     accountId,
		"invalidatedAt": nil,
	}, &options.FindOneOptions{
		Sort: bson.M{"createdAt": -1},
	}).Decode(entry); err != nil {
		// If the entry doesn't exist
		if errors.Is(err, mongo.ErrNoDocuments) {
			return "", Err2FARequired
		}
		return "", err // will be rendered as a 500
	}

	// If the entry has expired, or maximum of 3 attempts have been reached, invalidate it
	now := time.Now()
	if entry.ExpiresOn.Before(now) || entry.FailedAttempts+1 >= 3 {
		if _, err := r.UpdateOne(context.Background(), bson.M{
			"_id": entry.ID,
		}, bson.M{
			"$set": bson.M{
				"lastAttempt":   now,
				"otpSalt":       "",
				"otpEncrypted":  "",
				"invalidatedAt": now,
			},
			"$inc": bson.M{
				"failedAttempts": 1,
			},
		}); err != nil {
			return "", err // will be rendered as a 500
		}

		return "", Err2FARequired
	}

	// Derive the key with the salt
	key, err := security.DeriveKey(password, string(entry.OTPSalt))
	if err != nil {
		return "", err // will be rendered as a 500
	}

	// Decrypt the OTP
	otpPlain, err := security.Decrypt(key, entry.OTPEncrypted)
	if err != nil {
		return "", err // will be rendered as a 500
	}

	// Check if the OTP is correct
	if otp != otpPlain {
		if _, err = r.UpdateOne(context.Background(), bson.M{
			"_id": entry.ID,
		}, bson.M{
			"$set": bson.M{
				"lastAttempt": now,
			},
			"$inc": bson.M{
				"failedAttempts": 1,
			},
		}); err != nil {
			return "", err // will be rendered as a 500
		}

		return "", Err2FAIncorrect
	}

	// If the OTP is correct, invalidate the entry, and return without error
	if _, err = r.UpdateOne(context.Background(), bson.M{
		"_id": entry.ID,
	}, bson.M{
		"$set": bson.M{
			"lastAttempt":  now,
			"otpSalt":      "",
			"otpEncrypted": "",
			"invalidateAt": now,
			"successfulAt": now,
		},
	}); err != nil {
		return "", err // will be rendered as a 500
	}

	return entry.ID, nil
}
