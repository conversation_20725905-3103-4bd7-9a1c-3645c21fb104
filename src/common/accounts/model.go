// Requires:
//  $ go install golang.org/x/tools/cmd/stringer@latest
//
//go:generate stringer -type=SCAWalletStatus -output=model_string.go

package accounts

import (
	"time"

	"yochbee/_base/json"

	"golang.org/x/crypto/bcrypt"
)

type SCAWalletStatus int8

const (
	SCAWalletStatus_Unknown SCAWalletStatus = iota
	SCAWalletStatus_Created
	SCAWalletStatus_Activated
)

type Account struct {
	ID                     string `bson:"_id" json:"id"`
	TreezorUserId          int64  `bson:"treezorUserId" json:"treezorUserId"`                   // XXXXXX
	TreezorPrimaryWalletId int64  `bson:"treezorPrimaryWalletId" json:"treezorPrimaryWalletId"` // XXXXXX

	TreezorSCAWalletId string `bson:"treezorSCAWalletId"     json:"treezorSCAWalletId"`

	ReferralCode           string     `bson:"referralCode,omitempty" json:"referralCode"` // Unique
	RefererParentAccountId string     `bson:"refererParentAccountId,omitempty" json:"refererParentAccountId"`
	FirstName              string     `bson:"firstName"              json:"firstName" validate:"required"`             // Prénom
	LastName               string     `bson:"lastName"               json:"lastName"`                                  // Nom de famille
	Email                  string     `bson:"email"                  json:"email"    validate:"required,email,max=50"` // Unique; see also https://docs.treezor.com/guide/cards/creation.html#creation-process
	HashedPassword         string     `bson:"hashedPassword"         json:"-"`                                         // [not exposed to any JSON marshalling, such as methods]
	PhoneNumber            string     `bson:"phoneNumber"            json:"phoneNumber"`                               // Unique; Starts with + (country code) (phone number)
	IsPhoneNumberVerified  bool       `bson:"isPhoneNumberVerified"  json:"isPhoneNumberVerified"`
	Last2FATime            *time.Time `bson:"last2FATime"            json:"last2FATime"` // XXXXXX
	LastSCATime            *time.Time `bson:"lastSCATime"            json:"lastSCATime"` // XXXXXX

	PhoneVerificationCode        string     `bson:"phoneVerificationCode" json:"phoneVerificationCode"`               // XXXXXX
	LastPhoneVerificationCodeGen *time.Time `bson:"lastPhoneVerificationCodeGen" json:"lastPhoneVerificationCodeGen"` // XXXXXX
	PasswordResetOTPCode         string     `bson:"passwordResetOTPCode" json:"passwordResetOTPCode"`                 // XXXXXX
	LastPasswordResetRequested   *time.Time `bson:"lastPasswordResetRequested" json:"lastPasswordResetRequested"`     // XXXXXX
	BlockedReason                string     `bson:"blockedReason" json:"blockedReason"`
	LastJWTSignature             string     `bson:"lastJWTSignature"` // XXXXXX

	PhysicalAddress         string `bson:"physicalAddress" json:"physicalAddress" validate:"max=36"`
	ExtraAddressInfo        string `bson:"extraAddressInfo" json:"extraAddressInfo" validate:"max=36"`
	PostalCode              string `bson:"postalCode" json:"postalCode"`
	City                    string `bson:"city" json:"city"`
	State                   string `bson:"state" json:"state"`
	Country                 string `bson:"country" json:"country"`
	CountryCode             string `bson:"countryCode" json:"countryCode"`
	IpAddressCountryIso3166 string `bson:"ipAddressCountryIso3166" json:"ipAddressCountryIso3166"` // XXXXXX

	Gender   string     `bson:"gender" json:"gender" validate:"required"`
	Birthday *time.Time `bson:"birthday" json:"birthday"`

	Title             string `bson:"title"             json:"title"             validate:"oneof=M MELLE MME"`
	PlaceOfBirth      string `bson:"placeOfBirth"      json:"placeOfBirth"      validate:""`
	BirthCountry      string `bson:"birthCountry"      json:"birthCountry"      validate:"iso3166_1_alpha2"`
	Nationality       string `bson:"nationality"       json:"nationality"       validate:"iso3166_1_alpha2"`
	SpecifiedUSPerson int    `bson:"specifiedUSPerson" json:"specifiedUSPerson" validate:"oneof=0 1"`
	FrenchTaxResident int    `bson:"frenchTaxResident" json:"frenchTaxResident" validate:"oneof=0 1"`
	IncomeRange       string `bson:"incomeRange"       json:"incomeRange"       validate:"oneof=0-18 19-23 24-27 28-35 36-56 57-*"`
	PersonalAssets    string `bson:"personalAssets"    json:"personalAssets"    validate:"oneof=0-2 3-22 23-*********** ***********-"`
	Occupation        string `bson:"occupation"        json:"occupation"`

	ProfilePhotoUrl string `bson:"profilePhotoUrl" json:"profilePhotoUrl"`

	Preferences *Preferences `bson:"preferences" json:"preferences"`

	CurrentOfferType   string `bson:"currentOfferType" json:"currentOfferType"`
	CurrentOfferPeriod string `bson:"currentOfferPeriod" json:"currentOfferPeriod"`

	PersonalTopic string `bson:"personalTopic" json:"personalTopic"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`
}

type Preferences struct {
	ReceiveYochbeeNewsByEmail          bool `bson:"receiveYochbeeNewsByEmail"          json:"receiveYochbeeNewsByEmail"`
	ReceiveYochbeeSpecialOffersByEmail bool `bson:"receiveYochbeeSpecialOffersByEmail" json:"receiveYochbeeSpecialOffersByEmail"`
	ReceivePushNotifications           bool `bson:"receivePushNotifications"           json:"receivePushNotifications"`
	IsBiometricAuthEnabled             bool `bson:"isBiometricAuthEnabled"             json:"isBiometricAuthEnabled"`
}

func (a *Account) ToMobileJSON() ([]byte, error) {
	if a == nil {
		return []byte("null"), nil
	}
	return json.MarshalRemoveFields(a,
		"hashedPassword",
		"treezorUserId",
		"treezorPrimaryWalletId",
		"ipAddressCountryIso3166",
		"phoneVerificationCode",
		"lastPhoneVerificationCodeGen",
		"lastPasswordResetRequested",
		"passwordResetOTPCode",
		"last2FATime",
		"lastSCATime",
		"lastJWTSignature",
	)
}

// SetNewPassword updates the account's hashed password. It is up to the user to persist to the DB.
func (a *Account) SetNewPassword(newPassword string) error {
	bp := []byte(newPassword)
	hash, err := bcrypt.GenerateFromPassword(bp, bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	a.HashedPassword = string(hash)
	return nil
}

// Is2FARequired returns true if the account needs to do a 2FA reverification after 90 days from the last 2FA time. The
// account is considered to have done the 2FA 90 days within the account creation date.
func (a *Account) Is2FARequired() bool {
	if EnableSCA {
		return false
	}

	now := time.Now()
	if a.CreatedAt.Add(90 * 24 * time.Hour).After(now) {
		return false
	}
	if a.Last2FATime == nil {
		return true
	}
	return a.Last2FATime.Add(90 * 24 * time.Hour).Before(now)
}

const EnableSCA = true

// IsSCARequired returns true if the account needs to do a SCA reverification after 180 days from the last SCA time.
// Account creation is not considered as a SCA time.
func (a *Account) IsSCARequired() bool {
	if !EnableSCA {
		return false
	}

	now := time.Now()
	if a.LastSCATime == nil {
		return true
	}
	return a.LastSCATime.Add(180 * 24 * time.Hour).Before(now)
}

func (a *Account) IsBlocked() bool {
	return a.BlockedReason != ""
}
