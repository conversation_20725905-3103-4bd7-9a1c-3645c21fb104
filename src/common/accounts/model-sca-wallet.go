package accounts

import (
	"time"
	"yochbee/_base/json"
)

type SCAWalletEntry struct {
	ID                       string    `bson:"_id"                      json:"id"`
	AccountId                string    `bson:"accountId"                json:"accountId"`
	TreezorUserId            int64     `bson:"treezorUserId"            json:"treezorUserId"`
	TreezorSCAWalletId       int64     `bson:"treezorSCAWalletId"       json:"treezorSCAWalletId"`
	CreationDate             string    `bson:"creationDate"             json:"creationDate"` // // using YYYY-MM-DD HH-MM-SS format (#doc https://docs.treezor.com/guide/users/events.html#structure-of-a-sca-wallet-create)
	ActivationCode           string    `bson:"activationCode"           json:"activationCode"`
	ActivationCodeExpiryDate time.Time `bson:"activationCodeExpiryDate" json:"activationCodeExpiryDate"` // parsed from RFC3336, like 2020-12-31T23:59:59Z
	IsConsumed               bool      `bson:"isConsumed"              json:"isConsumed"`

	LastUsedForTreezor *time.Time `bson:"lastUsedForTreezor"       json:"lastUsedForTreezor"`
}

func (w *SCAWalletEntry) ToMobileJSON() ([]byte, error) {
	if w == nil {
		return []byte("null"), nil
	}
	return json.MarshalRemoveFields(w,
		"treezorUserId",
		"treezorSCAWalletId",
		"lastUsedForTreezor",
	)
}
