package accounts

import (
	"context"
	"time"
	"yochbee/_base/dbnosql"
	"yochbee/_base/random"

	"github.com/eliezedeck/gobase/logging"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

type AccountsCollection struct {
	dbnosql.DataCollection
}

func SetupAccountsCollection(db dbnosql.Database) (*AccountsCollection, error) {
	coll := db.DeclareCollection("accounts", func(account interface{}) interface{} {
		return account
	}, func() (destination interface{}) {
		return &Account{}
	})
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "email", Order: dbnosql.OrderASC},
		},
		Name: "email",
	}); err != nil {
		return nil, err
	}
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "phoneNumber", Order: dbnosql.OrderASC},
		},
		Name: "phoneNumber",
	}); err != nil {
		return nil, err
	}

	// Set a unique index on referralCode
	// This is used to prevent duplicate referral codes at the DB level (atomic)
	if err := coll.SetUniqueIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "referralCode", Order: dbnosql.OrderASC},
		},
		Name: "referralCode",
	}); err != nil {
		return nil, err
	}

	// Set a search index on refererParentAccountId
	// This is to allow searching for accounts that were referred by a specific account
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "refererParentAccountId", Order: dbnosql.OrderASC},
		},
		Name: "refererParentAccountId",
	}); err != nil {
		return nil, err
	}

	// Set a search index on personalTopic
	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "personalTopic", Order: dbnosql.OrderASC},
		},
		Name: "personalTopic",
	}); err != nil {
		return nil, err
	}

	// Find all accounts without a personalTopic
	rawColl := coll.GetRawCollection().(*mongo.Collection)
	filter := bson.M{"personalTopic": bson.M{"$exists": false}}
	cursor, err := rawColl.Find(context.Background(), filter)
	if err != nil {
		return nil, err
	}

	// Iterate over the cursor and update each account
	for cursor.Next(context.Background()) {
		var account Account
		if err := cursor.Decode(&account); err != nil {
			return nil, err
		}

		// Generate a random string for the personalTopic
		randomString, err := random.CryptoStrings(13)
		if err != nil {
			return nil, err
		}
		account.PersonalTopic = "account-" + randomString

		// Update the account in the database
		_, err = rawColl.UpdateOne(context.Background(), bson.M{"_id": account.ID}, bson.M{"$set": bson.M{"personalTopic": account.PersonalTopic}})
		if err != nil {
			return nil, err
		}

		logging.L.Info("Updated account with personalTopic", zap.String("accountId", account.ID), zap.String("personalTopic", account.PersonalTopic))
	}

	// Check if there was any error during iteration
	if err := cursor.Err(); err != nil {
		return nil, err
	}

	// Close the cursor
	cursor.Close(context.Background())

	// Return the updated AccountsCollection
	return &AccountsCollection{coll}, nil
}

func (c *AccountsCollection) GetByPhoneNumber(phoneNumber string) (*Account, error) {
	return dbnosql.GetOneByField[*Account](c, "phoneNumber", phoneNumber)
}

func (c *AccountsCollection) GetByEmail(email string) (*Account, error) {
	return dbnosql.GetOneByField[*Account](c, "email", email)
}

func (c *AccountsCollection) GetByReferralCode(referralCode string) (*Account, error) {
	return dbnosql.GetOneByField[*Account](c, "referralCode", referralCode)
}

func (c *AccountsCollection) GetAccountsWithoutSCAWallet() ([]*Account, error) {
	raw := c.GetRawCollection().(*mongo.Collection)
	filter := bson.M{"$or": []bson.M{
		{"treezorSCAWalletId": bson.M{"$exists": false}},
		{"treezorSCAWalletId": ""},
	}}
	opts := options.Find().SetSort(bson.D{{"createdAt", 1}})
	cursor, err := raw.Find(context.Background(), filter, opts)
	if err != nil {
		return nil, err
	}

	var accounts []*Account
	if err := cursor.All(context.Background(), &accounts); err != nil {
		return nil, err
	}
	return accounts, nil
}

func (c *AccountsCollection) TouchLast2FATime(accountId string, time *time.Time) error {
	r := c.GetRawCollection().(*mongo.Collection)
	_, err := r.UpdateOne(context.Background(), bson.M{"_id": accountId}, bson.M{"$set": bson.M{"last2FATime": time}})
	return err
}

func (c *AccountsCollection) TouchLastSCATime(accountId string, time *time.Time) error {
	r := c.GetRawCollection().(*mongo.Collection)
	_, err := r.UpdateOne(context.Background(), bson.M{"_id": accountId}, bson.M{"$set": bson.M{"lastSCATime": time}})
	return err
}

func (c *AccountsCollection) UpdatePreferences(accountId string, preferences *Preferences) error {
	r := c.GetRawCollection().(*mongo.Collection)
	_, err := r.UpdateOne(context.Background(), bson.M{"_id": accountId}, bson.M{"$set": bson.M{"preferences": preferences}})
	return err
}

func (c *AccountsCollection) UpdateLastJWTSignature(id string, shasum256 string) error {
	r := c.GetRawCollection().(*mongo.Collection)
	_, err := r.UpdateOne(context.Background(), bson.M{"_id": id}, bson.M{"$set": bson.M{"lastJWTSignature": shasum256}})
	return err
}
