package accounts

import (
	"context"
	"strings"
	"yochbee/_base/config"

	"github.com/golang-jwt/jwt"
	"github.com/labstack/echo/v4"
)

var (
	// this will be overidden by the etcd configuration; during dev, if there is nothing on etcd, this will be used
	JwtSecret = []byte("xEX%wxfWC-;n2@[vTv{V,dekj/xv\\3oBdxCld1%/N6h")
)

func ConfigureJWT(config config.Provider) error {
	return config.GetLive(context.Background(), "security.jwt.secret", func(bytestr []byte) {
		if len(bytestr) == 0 {
			return
		}
		JwtSecret = bytestr
	})
}

func JwtTokenIsValid(token string) (*jwt.Token, bool) {
	// Verify the token
	ptoken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return JwtSecret, nil
	})
	if err != nil || !ptoken.Valid {
		// Token could be:
		// - Invalid
		// - Expired
		// - Not signed by us
		// - ...
		return nil, false
	}
	return ptoken, true
}

func IsRequestReallyFromUser(c echo.Context, accountId string) bool {
	ra := c.Request().Header.Get("Authorization")
	splits := strings.Split(ra, " ")
	if len(splits) < 2 {
		return false
	}

	token := strings.TrimSpace(splits[len(splits)-1])
	jtoken, valid := JwtTokenIsValid(token)
	if !valid {
		return false
	}

	claims := jtoken.Claims.(jwt.MapClaims)
	realAccountId := claims["sub"].(string)
	return realAccountId == accountId
}
