package treezor

import (
	"encoding/json"
	"time"
	"yochbee/common/banking"

	json2 "yochbee/_base/json"

	"go.uber.org/zap"
)

// Card is a snapshot of the user's card in time. The latest one is the one that is currently active.
type Card struct {
	ID string `bson:"_id" json:"id"`

	TreezorCardID   int64 `bson:"treezorCardId" json:"cardId"`
	TreezorUserID   int64 `bson:"treezorUserId" json:"userId"`
	TreezorWalletID int64 `bson:"treezorWalletId" json:"walletId"`

	WalletCardtransactionID int64   `bson:"walletCardtransactionId" json:"walletCardtransactionId"`
	PublicToken             string  `bson:"publicToken" json:"publicToken"`
	CardTag                 string  `bson:"cardTag" json:"cardTag"`
	StatusCode              string  `bson:"statusCode" json:"statusCode"`
	IsLive                  int     `bson:"isLive" json:"isLive"`
	PinTryExceeds           int     `bson:"pinTryExceeds" json:"pinTryExceeds"`
	MaskedPan               string  `bson:"maskedPan" json:"maskedPan"`
	EmbossedName            string  `bson:"embossedName" json:"embossedName"`
	ExpiryDate              string  `bson:"expiryDate" json:"expiryDate"`
	CVV                     string  `bson:"cvv" json:"cvv"`
	StartDate               string  `bson:"startDate" json:"startDate"`
	EndDate                 string  `bson:"endDate" json:"endDate"`
	CountryCode             string  `bson:"countryCode" json:"countryCode"`
	CurrencyCode            string  `bson:"currencyCode" json:"currencyCode"`
	Lang                    string  `bson:"lang" json:"lang"`
	DeliveryTitle           string  `bson:"deliveryTitle" json:"deliveryTitle"`
	DeliveryLastname        string  `bson:"deliveryLastname" json:"deliveryLastname"`
	DeliveryFirstname       string  `bson:"deliveryFirstname" json:"deliveryFirstname"`
	DeliveryAddress1        string  `bson:"deliveryAddress1" json:"deliveryAddress1"`
	DeliveryAddress2        string  `bson:"deliveryAddress2" json:"deliveryAddress2"`
	DeliveryAddress3        string  `bson:"deliveryAddress3" json:"deliveryAddress3"`
	DeliveryCity            string  `bson:"deliveryCity" json:"deliveryCity"`
	DeliveryPostcode        string  `bson:"deliveryPostcode" json:"deliveryPostcode"`
	DeliveryCountry         string  `bson:"deliveryCountry" json:"deliveryCountry"`
	MobileSent              string  `bson:"mobileSent" json:"mobileSent"`
	CardDesign              string  `bson:"cardDesign" json:"cardDesign"`
	VirtualConverted        int     `bson:"virtualConverted" json:"virtualConverted"`
	OptionAtm               int     `bson:"optionAtm" json:"optionAtm"`
	OptionForeign           int     `bson:"optionForeign" json:"optionForeign"`
	OptionOnline            int     `bson:"optionOnline" json:"optionOnline"`
	OptionNfc               int     `bson:"optionNfc" json:"optionNfc"`
	LimitAtmYear            float64 `bson:"limitAtmYear" json:"limitAtmYear"`
	LimitAtmMonth           float64 `bson:"limitAtmMonth" json:"limitAtmMonth"`
	LimitAtmWeek            float64 `bson:"limitAtmWeek" json:"limitAtmWeek"`
	LimitAtmDay             float64 `bson:"limitAtmDay" json:"limitAtmDay"`
	LimitAtmAll             float64 `bson:"limitAtmAll" json:"limitAtmAll"`
	LimitPaymentYear        float64 `bson:"limitPaymentYear" json:"limitPaymentYear"`
	LimitPaymentMonth       float64 `bson:"limitPaymentMonth" json:"limitPaymentMonth"`
	LimitPaymentWeek        float64 `bson:"limitPaymentWeek" json:"limitPaymentWeek"`
	LimitPaymentDay         float64 `bson:"limitPaymentDay" json:"limitPaymentDay"`
	LimitPaymentAll         float64 `bson:"limitPaymentAll" json:"limitPaymentAll"`
	PaymentDailyLimit       float64 `bson:"paymentDailyLimit" json:"paymentDailyLimit"`
	TotalAtmYear            float64 `bson:"totalAtmYear" json:"totalAtmYear"`
	TotalAtmMonth           float64 `bson:"totalAtmMonth" json:"totalAtmMonth"`
	TotalAtmWeek            float64 `bson:"totalAtmWeek" json:"totalAtmWeek"`
	TotalAtmDay             float64 `bson:"totalAtmDay" json:"totalAtmDay"`
	TotalAtmAll             float64 `bson:"totalAtmAll" json:"totalAtmAll"`
	TotalPaymentYear        float64 `bson:"totalPaymentYear" json:"totalPaymentYear"`
	TotalPaymentMonth       float64 `bson:"totalPaymentMonth" json:"totalPaymentMonth"`
	TotalPaymentWeek        float64 `bson:"totalPaymentWeek" json:"totalPaymentWeek"`
	TotalPaymentDay         float64 `bson:"totalPaymentDay" json:"totalPaymentDay"`
	TotalPaymentAll         float64 `bson:"totalPaymentAll" json:"totalPaymentAll"`
	CreatedBy               int     `bson:"createdBy" json:"createdBy"`
	CreatedDate             string  `bson:"createdDate" json:"createdDate"`
	ModifiedBy              int     `bson:"modifiedBy" json:"modifiedBy"`
	ModifiedDate            string  `bson:"modifiedDate" json:"modifiedDate"`
	IsPhysical              int     `bson:"physical" json:"physical"`

	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (c *Card) ToMobileJSON() ([]byte, error) {
	return json2.MarshalRemoveFields(c, "treezorCardId", "treezorUserId", "treezorWalletId", "cvv", "cardDesign", "createdBy", "createdDate", "modifiedBy", "modifiedDate")
}

func (c *Card) SyncToBankingCard(L *zap.Logger, bcard *banking.Card) bool {
	if bcard.WebhookTimestamp != 0 && c.WebhookTimestamp != 0 && bcard.WebhookTimestamp >= c.WebhookTimestamp {
		L.Warn("Banking Card webhook timestamp is equal/newer than Treezor's Card webhook timestamp")
		return false
	}

	bcard.WebhookTimestamp = c.WebhookTimestamp

	bcard.TreezorCardId = c.TreezorCardID
	bcard.TreezorUserId = c.TreezorUserID
	bcard.TreezorWalletId = c.TreezorWalletID
	bcard.IsPhysical = c.IsPhysical == 1
	bcard.WalletCardTransactionID = c.WalletCardtransactionID
	bcard.PublicToken = c.PublicToken
	bcard.StatusCode = c.StatusCode
	bcard.IsLive = c.IsLive
	bcard.PinTryExceeds = c.PinTryExceeds
	bcard.MaskedPan = c.MaskedPan
	bcard.EmbossedName = c.EmbossedName
	bcard.ExpiryDate = c.ExpiryDate
	bcard.CVV = c.CVV
	bcard.CountryCode = c.CountryCode
	bcard.CurrencyCode = c.CurrencyCode
	bcard.Lang = c.Lang
	bcard.OptionAtm = c.OptionAtm == 1
	bcard.OptionForeign = c.OptionForeign == 1
	bcard.OptionOnline = c.OptionOnline == 1
	bcard.OptionNfc = c.OptionNfc == 1
	bcard.LimitAtmDay = c.LimitAtmDay
	bcard.LimitAtmWeek = c.LimitAtmWeek
	bcard.LimitAtmMonth = c.LimitAtmMonth
	bcard.LimitAtmYear = c.LimitAtmYear
	bcard.LimitAtmAll = c.LimitAtmAll
	bcard.LimitPaymentDay = c.LimitPaymentDay
	bcard.LimitPaymentWeek = c.LimitPaymentWeek
	bcard.LimitPaymentMonth = c.LimitPaymentMonth
	bcard.LimitPaymentYear = c.LimitPaymentYear
	bcard.LimitPaymentAll = c.LimitPaymentAll
	bcard.PaymentDailyLimit = c.PaymentDailyLimit
	bcard.TotalAtmDay = c.TotalAtmDay
	bcard.TotalAtmWeek = c.TotalAtmWeek
	bcard.TotalAtmMonth = c.TotalAtmMonth
	bcard.TotalAtmYear = c.TotalAtmYear
	bcard.TotalAtmAll = c.TotalAtmAll
	bcard.TotalPaymentDay = c.TotalPaymentDay
	bcard.TotalPaymentWeek = c.TotalPaymentWeek
	bcard.TotalPaymentMonth = c.TotalPaymentMonth
	bcard.TotalPaymentYear = c.TotalPaymentYear
	bcard.TotalPaymentAll = c.TotalPaymentAll
	bcard.IsVirtualConverted = c.VirtualConverted == 1

	return true
}

func UnwrapCardsFromTreezorResponse(msg []byte) ([]*Card, error) {
	// Convert all int fields that are strings to numeric values.
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "cards", []string{"cardId",
		"userId",
		"walletId",
		"walletCardtransactionId",
		"isLive",
		"pinTryExceeds",
		"virtualConverted",
		"optionAtm",
		"optionForeign",
		"optionOnline",
		"optionNfc",
		"limitAtmYear",
		"limitAtmMonth",
		"limitAtmWeek",
		"limitAtmDay",
		"limitAtmAll",
		"limitPaymentYear",
		"limitPaymentMonth",
		"limitPaymentWeek",
		"limitPaymentDay",
		"limitPaymentAll",
		"paymentDailyLimit",
		"totalAtmYear",
		"totalAtmMonth",
		"totalAtmWeek",
		"totalAtmDay",
		"totalAtmAll",
		"totalPaymentYear",
		"totalPaymentMonth",
		"totalPaymentWeek",
		"totalPaymentDay",
		"totalPaymentAll",
		"createdBy",
		"modifiedBy",
		"physical"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Cards []*Card `json:"cards"`
	}{}
	if err = json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Cards, nil
}
