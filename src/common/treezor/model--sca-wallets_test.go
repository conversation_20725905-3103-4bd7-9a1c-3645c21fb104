package treezor

import "testing"

func TestManualCreationPayloadParsing(t *testing.T) {
	payload := `{
  "id": "12112f6d36fa46cabc0283b92fd1a0d7",
  "status": "CREATED",
  "subStatus": "CREATED_READY",
  "passcodeStatus": "NOT_SET",
  "locked": false,
  "lockReasons": [],
  "lockMessage": null,
  "settingsProfile": "default",
  "mobileWallet": null,
  "activationCode": "0x48ad93d06449bfef75568e91ab6e73f5",
  "creationDate": "2023-12-27T13:09:04+01:00",
  "deletionDate": null,
  "activationCodeExpiryDate": "2023-12-27T13:29:04+01:00",
  "authenticationMethods": [
    {
      "type": "DEVICE_BIOMETRIC",
      "usages": [
        "STRONG_CUSTOMER_AUTHENTICATION"
      ],
      "parameters": {
        "validityDuration": 60
      }
    },
    {
      "type": "HYBRID_PIN",
      "usages": [
        "WALLET_MANAGEMENT",
        "STRONG_CUSTOMER_AUTHENTICATION"
      ],
      "parameters": {
        "maxAttempts": 3,
        "validityDuration": 60
      }
    },
    {
      "type": "NONE",
      "usages": [
        "STRONG_CUSTOMER_AUTHENTICATION"
      ],
      "parameters": []
    }
  ],
  "invalidActivationAttempts": null,
  "userId": "3940321",
  "scaWalletTag": "iPhone14Pro",
  "clientId": "39625",
  "activationDate": null
}`
	wallet, err := UnwrapSingleSCAWalletFromTreezorResponse([]byte(payload))
	if err != nil {
		t.Fatal(err)
	}

	if wallet.ID != "" {
		t.Fatal("wallet.ID != ")
	}

  if wallet.TreezorId != "12112f6d36fa46cabc0283b92fd1a0d7" {
    t.Fatal("wallet.TreezorId != 12112f6d36fa46cabc0283b92fd1a0d7")
  }

	if wallet.CreationDate == nil {
		t.Fatal("wallet.CreationDate == nil")
	}
}
