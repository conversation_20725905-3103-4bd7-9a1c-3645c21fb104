package treezor

import (
	"encoding/json"
	"time"
)

type Mandate struct {
	ID                                  int64      `bson:"_id" json:"mandateId"`
	Title                               string     `bson:"title" json:"title"`
	LegalInformations                   string     `bson:"legalInformations" json:"legalInformations"`
	UniqueMandateReference              string     `bson:"uniqueMandateReference" json:"uniqueMandateReference"`
	MandateStatus                       string     `bson:"mandateStatus" json:"mandateStatus"`
	UserID                              int64      `bson:"userId" json:"userId"`
	DebtorName                          string     `bson:"debtorName" json:"debtorName"`
	DebtorAddress                       string     `bson:"debtorAddress" json:"debtorAddress"`
	DebtorCity                          string     `bson:"debtorCity" json:"debtorCity"`
	DebtorZipCode                       string     `bson:"debtorZipCode" json:"debtorZipCode"`
	DebtorCountry                       string     `bson:"debtorCountry" json:"debtorCountry"`
	DebtorIban                          string     `bson:"debtorIban" json:"debtorIban"`
	DebtorBic                           string     `bson:"debtorBic" json:"debtorBic"`
	SequenceType                        string     `bson:"sequenceType" json:"sequenceType"`
	CreditorName                        string     `bson:"creditorName" json:"creditorName"`
	SepaCreditorIdentifier              string     `bson:"sepaCreditorIdentifier" json:"sepaCreditorIdentifier"`
	CreditorAddress                     string     `bson:"creditorAddress" json:"creditorAddress"`
	CreditorCity                        string     `bson:"creditorCity" json:"creditorCity"`
	CreditorZipCode                     string     `bson:"creditorZipCode" json:"creditorZipCode"`
	CreditorCountry                     string     `bson:"creditorCountry" json:"creditorCountry"`
	SignatureDate                       string     `bson:"signatureDate" json:"signatureDate"`
	DebtorSignatureIp                   string     `bson:"debtorSignatureIp" json:"debtorSignatureIp"`
	Signed                              int        `bson:"signed" json:"signed"`
	RevocationSignatureDate             *time.Time `bson:"revocationSignatureDate" json:"revocationSignatureDate"`
	DebtorIdentificationCode            string     `bson:"debtorIdentificationCode" json:"debtorIdentificationCode"`
	DebtorReferencePartyName            string     `bson:"debtorReferencePartyName" json:"debtorReferencePartyName"`
	DebtorReferenceIdentificationCode   string     `bson:"debtorReferenceIdentificationCode" json:"debtorReferenceIdentificationCode"`
	CreditorReferencePartyName          string     `bson:"creditorReferencePartyName" json:"creditorReferencePartyName"`
	CreditorReferenceIdentificationCode string     `bson:"creditorReferenceIdentificationCode" json:"creditorReferenceIdentificationCode"`
	ContractIdentificationNumber        string     `bson:"contractIdentificationNumber" json:"contractIdentificationNumber"`
	ContractDescription                 string     `bson:"contractDescription" json:"contractDescription"`
	CreatedDate                         *time.Time `bson:"createdDate" json:"createdDate"`
	CodeStatus                          int        `bson:"codeStatus" json:"codeStatus"`
	InformationStatus                   string     `bson:"informationStatus" json:"informationStatus"`
	IsPaper                             bool       `bson:"isPaper" json:"isPaper"`
	UserIDUltimateCreditor              int64      `bson:"userIdUltimateCreditor" json:"userIdUltimateCreditor"`
}

// UnwrapMandateFromTreezorResponse unwraps the response from Treezor for mandate events
func UnwrapMandateFromTreezorResponse(response []byte) ([]*Mandate, error) {
	// Convert all numeric fields that are strings to numbers
	response, err := PatchBadStringFieldsThatShouldBeNumbers(response, "mandates",
		[]string{
			// All number fields appears to be numbers already
		},
	)
	if err != nil {
		return nil, err
	}

	var payload struct {
		Mandates []*Mandate `json:"mandates"`
	}
	if err = json.Unmarshal(response, &payload); err != nil {
		return nil, err
	}
	return payload.Mandates, nil
}

func (m *Mandate) ToMobileJSON() ([]byte, error) {
	data := map[string]interface{}{
		"id":                                  m.ID, // changed
		"title":                               m.Title,
		"legalInformations":                   m.LegalInformations,
		"uniqueMandateReference":              m.UniqueMandateReference,
		"mandateStatus":                       m.MandateStatus,
		"treezorUserId":                       m.UserID, // changed
		"debtorName":                          m.DebtorName,
		"debtorAddress":                       m.DebtorAddress,
		"debtorCity":                          m.DebtorCity,
		"debtorZipCode":                       m.DebtorZipCode,
		"debtorCountry":                       m.DebtorCountry,
		"debtorIban":                          m.DebtorIban,
		"debtorBic":                           m.DebtorBic,
		"sequenceType":                        m.SequenceType,
		"creditorName":                        m.CreditorName,
		"sepaCreditorIdentifier":              m.SepaCreditorIdentifier,
		"creditorAddress":                     m.CreditorAddress,
		"creditorCity":                        m.CreditorCity,
		"creditorZipCode":                     m.CreditorZipCode,
		"creditorCountry":                     m.CreditorCountry,
		"signatureDate":                       m.SignatureDate,
		"debtorSignatureIp":                   m.DebtorSignatureIp,
		"signed":                              m.Signed,
		"revocationSignatureDate":             m.RevocationSignatureDate,
		"debtorIdentificationCode":            m.DebtorIdentificationCode,
		"debtorReferencePartyName":            m.DebtorReferencePartyName,
		"debtorReferenceIdentificationCode":   m.DebtorReferenceIdentificationCode,
		"creditorReferencePartyName":          m.CreditorReferencePartyName,
		"creditorReferenceIdentificationCode": m.CreditorReferenceIdentificationCode,
		"contractIdentificationNumber":        m.ContractIdentificationNumber,
		"contractDescription":                 m.ContractDescription,
		"createdDate":                         m.CreatedDate,
		"codeStatus":                          m.CodeStatus,
		"informationStatus":                   m.InformationStatus,
		"isPaper":                             m.IsPaper,
		"userIdUltimateCreditor":              m.UserIDUltimateCreditor,
	}
	return json.Marshal(data)
}
