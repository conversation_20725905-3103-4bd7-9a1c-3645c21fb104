package treezor

import (
	"encoding/json"
	"time"
)

type PayoutRefund struct {
	ID string `bson:"_id"                json:"bid"`

	TreezorPayoutRefundID interface{} `bson:"treezorPayoutRefundId"  json:"id"`       // transitioning to a string: October 2023
	TreezorPayoutID       interface{} `bson:"treezorPayoutId"        json:"payoutId"` // transitioning to a string: October 2023

	CodeStatus        int    `bson:"codeStatus"         json:"codeStatus"`
	InformationStatus string `bson:"informationStatus"  json:"informationStatus"`
	RequestAmount     string `bson:"requestAmount"      json:"requestAmount"`
	RequestCurrency   string `bson:"requestCurrency"    json:"requestCurrency"`
	RequestComment    string `bson:"requestComment"     json:"requestComment"`
	ReasonCode        string `bson:"reasonCode"         json:"reasonCode"`
	RefundAmount      string `bson:"refundAmount"       json:"refundAmount"`
	RefundCurrency    string `bson:"refundCurrency"     json:"refundCurrency"`
	RefundDate        string `bson:"refundDate"         json:"refundDate"`
	RefundComment     string `bson:"refundComment"      json:"refundComment"`
	CreatedDate       string `bson:"createdDate"        json:"createdDate"`
	ModifiedDate      string `bson:"modifiedDate"       json:"modifiedDate"`

	CreatedAt time.Time  `bson:"createdAt"           json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt"           json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (p *PayoutRefund) GetTreezorID() (string, interface{}) {
	return "treezorPayoutRefundId", p.TreezorPayoutRefundID
}

func (p *PayoutRefund) TreezorTimestamp() int64 {
	return p.WebhookTimestamp
}

func (p *PayoutRefund) SetTreezorTimestamp(ts int64) {
	p.WebhookTimestamp = ts
}

func (p *PayoutRefund) SetInsertID(id string) {
	p.ID = id
}

func (p *PayoutRefund) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*PayoutRefund)

	now := time.Now()
	p.UpdatedAt = &now
	p.WebhookTimestamp = in.WebhookTimestamp

	// Treezor IDs left intact

	p.CodeStatus = in.CodeStatus
	p.InformationStatus = in.InformationStatus
	p.RequestAmount = in.RequestAmount
	p.RequestCurrency = in.RequestCurrency
	p.RequestComment = in.RequestComment
	p.ReasonCode = in.ReasonCode
	p.RefundAmount = in.RefundAmount
	p.RefundCurrency = in.RefundCurrency
	p.RefundDate = in.RefundDate
	p.RefundComment = in.RefundComment
	p.CreatedDate = in.CreatedDate
	p.ModifiedDate = in.ModifiedDate
}

func UnwrapPayoutRefundsFromTreezorResponse(msg []byte) ([]*PayoutRefund, error) {
	// Convert all int fields that are strings to ints
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "payoutRefunds", []string{"id", "payoutId", "codeStatus"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Refunds []*PayoutRefund `json:"payoutRefunds"`
	}{}
	if err := json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Refunds, nil
}
