package treezor

import (
	"encoding/json"
	"sort"
	"strconv"
	json2 "yochbee/_base/json"
	"yochbee/_base/utils"
	"yochbee/common/banking"

	"github.com/eliezedeck/gobase/logging"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
)

// CardTransaction is a mapping of the Treezor card transaction object.
// See https://docs.treezor.com/guide/cards/transactions.html#structure-of-a-transaction
type CardTransaction struct {
	ID              string `bson:"_id"  json:"id"`
	TreezorId       int64  `json:"cardtransactionId" bson:"treezorId"`
	TreezorCardId   int64  `json:"cardId" bson:"treezorCardId"`
	TreezorWalletId int64  `json:"walletId" bson:"treezorWalletId"`

	WalletCurrency            string                  `json:"walletCurrency" bson:"walletCurrency"`
	MerchantId                string                  `json:"merchantId" bson:"merchantId"`
	MerchantName              string                  `json:"merchantName" bson:"merchantName"`
	MerchantCity              string                  `json:"merchantCity" bson:"merchantCity"`
	MerchantCountry           string                  `json:"merchantCountry" bson:"merchantCountry"`
	PaymentLocalTime          int64                   `json:"paymentLocalTime" bson:"paymentLocalTime"`
	PublicToken               string                  `json:"publicToken" bson:"publicToken"`
	PaymentAmount             float64                 `json:"paymentAmount" bson:"paymentAmount"`
	PaymentCurrency           string                  `json:"paymentCurrency" bson:"paymentCurrency"`
	Fees                      float64                 `json:"fees" bson:"fees"`
	PaymentCountry            string                  `json:"paymentCountry" bson:"paymentCountry"`
	PaymentId                 string                  `json:"paymentId" bson:"paymentId"`
	PaymentStatus             string                  `json:"paymentStatus" bson:"paymentStatus"`
	PaymentLocalAmount        float64                 `json:"paymentLocalAmount" bson:"paymentLocalAmount"`
	PosCardholderPresence     string                  `json:"posCardholderPresence" bson:"posCardholderPresence"`
	PosPostcode               string                  `json:"posPostcode" bson:"posPostcode"`
	PosCountry                string                  `json:"posCountry" bson:"posCountry"`
	PosTerminalId             string                  `json:"posTerminalId" bson:"posTerminalId"`
	PosCardPresence           string                  `json:"posCardPresence" bson:"posCardPresence"`
	PanEntryMethod            int64                   `json:"panEntryMethod" bson:"panEntryMethod"`
	AuthorizationNote         string                  `json:"authorizationNote" bson:"authorizationNote"`
	AuthorizationResponseCode int64                   `json:"authorizationResponseCode" bson:"authorizationResponseCode"`
	AuthorizationIssuerId     string                  `json:"authorizationIssuerId" bson:"authorizationIssuerId"`
	AuthorizationIssuerTime   utils.DynamicFormatTime `json:"authorizationIssuerTime" bson:"authorizationIssuerTime"`
	AuthorizationMti          int64                   `json:"authorizationMti" bson:"authorizationMti"`
	AuthorizedBalance         float64                 `json:"authorizedBalance" bson:"authorizedBalance"`
	LimitAtmYear              float64                 `json:"limitAtmYear" bson:"limitAtmYear"`
	LimitAtmMonth             float64                 `json:"limitAtmMonth" bson:"limitAtmMonth"`
	LimitAtmWeek              float64                 `json:"limitAtmWeek" bson:"limitAtmWeek"`
	LimitAtmAll               float64                 `json:"limitAtmAll" bson:"limitAtmAll"`
	LimitPaymentYear          float64                 `json:"limitPaymentYear" bson:"limitPaymentYear"`
	LimitPaymentMonth         float64                 `json:"limitPaymentMonth" bson:"limitPaymentMonth"`
	LimitPaymentWeek          float64                 `json:"limitPaymentWeek" bson:"limitPayment"`
	LimitPaymentDay           float64                 `json:"limitPaymentDay" bson:"limitPaymentDay"`
	LimitPaymentAll           float64                 `json:"limitPaymentAll" bson:"limitPaymentAll"`
	TotalLimitAtmWeek         float64                 `json:"totalLimitAtmWeek" bson:"totalLimitAtmWeek"`
	TotalLimitAtmDay          float64                 `json:"totalLimitAtmDay" bson:"totalLimitAtmDay"`
	TotalLimitAtmAll          float64                 `json:"totalLimitAtmAll" bson:"totalLimitAtmAll"`
	TotalLimitPaymentYear     float64                 `json:"totalLimitPaymentYear" bson:"totalLimitPaymentYear"`
	TotalLimitPaymentMonth    float64                 `json:"totalLimitPaymentMonth" bson:"totalLimitPaymentMonth"`
	TotalLimitPaymentWeek     float64                 `json:"totalLimitPaymentWeek" bson:"totalLimitPaymentWeek"`
	TotalLimitPaymentDay      float64                 `json:"totalLimitPaymentDay" bson:"totalLimitPaymentDay"`
	TotalLimitPaymentAll      float64                 `json:"totalLimitPaymentAll" bson:"totalLimitPaymentAll"`
	MccCode                   string                  `json:"mccCode" bson:"mccCode"`
	AcquirerId                int64                   `json:"acquirerId" bson:"acquirerId"`
	Is3DS                     *bool                   `json:"is3DS,omitempty" bson:"is3DS,omitempty"`
	MerchantAddress           *string                 `json:"merchantAddress,omitempty" bson:"merchantAddress,omitempty"`
	PaymentLocalDate          *string                 `json:"paymentLocalDate,omitempty" bson:"paymentLocalDate,omitempty"`

	TreezorWebhookTimestamp int64                   `json:"treezorWebhookTimestamp" bson:"treezorWebhookTimestamp"`
	CreatedAt               utils.DynamicFormatTime `json:"createdAt" bson:"createdAt"`
}

func (t *CardTransaction) EnrichBankingTransaction(bt *banking.Transaction) bool {
	if bt.TreezorCardTransactionWebhookTimestamp != 0 && bt.TreezorCardTransactionWebhookTimestamp > t.TreezorWebhookTimestamp {
		logging.L.Info("CardTransaction.EnrichBankingTransaction: the Banking Transaction is already newer (or the same)", zap.String("transactionId", bt.ID))
		return false
	}
	bt.TreezorCardTransactionWebhookTimestamp = t.TreezorWebhookTimestamp

	// Based on the amount, determine if this is a credit or debit
	if t.PaymentAmount < 0 {
		bt.TransactionType = banking.TransactionTypeCardDebit
		bt.TransactionTypeStr = banking.TransactionTypeCardDebit.String()
		bt.Description = "Achat"
	} else {
		bt.TransactionType = banking.TransactionTypeCardCredit
		bt.TransactionTypeStr = banking.TransactionTypeCardCredit.String()
		bt.Description = "Remboursement"
	}

	// Seller information
	bt.Name = t.MerchantName

	// Set the status based on the treezor paymentStatus
	// See also: https://docs.treezor.com/guide/cards/transactions.html#statuses-paymentstatus
	switch t.PaymentStatus {
	case "A":
		bt.TransactionStatus = banking.TransactionStatusAuthorized
		bt.TransactionStatusStr = banking.TransactionStatusAuthorized.String()
	case "R":
		bt.TransactionStatus = banking.TransactionStatusRefunded
		bt.TransactionStatusStr = banking.TransactionStatusRefunded.String()
	case "S":
		bt.TransactionStatus = banking.TransactionStatusSettled
		bt.TransactionStatusStr = banking.TransactionStatusSettled.String()
	case "C":
		// Doc: Settlement is received without a previous matching authorization request
		bt.TransactionStatus = banking.TransactionStatusSettled
		bt.TransactionStatusStr = banking.TransactionStatusSettled.String()
	case "I":
		bt.TransactionStatus = banking.TransactionStatusDeclined
		bt.TransactionStatusStr = banking.TransactionStatusDeclined.String()
	case "V":
		// Doc: V (Reversed) may happen when an automated gas station authorized an amount greater than the actual amount of fuel pumped.
		bt.TransactionStatus = banking.TransactionStatusDeclined
		bt.TransactionStatusStr = banking.TransactionStatusDeclined.String()
	}

	return true
}

func UnwrapCardTransactionsFromTreezorResponse(msg []byte) ([]*CardTransaction, error) {
	// On production, the list key is "cardtransactions", on staging, it is "cardTransactions"
	listKey := "cardTransactions"
	if !gjson.GetBytes(msg, listKey).Exists() {
		listKey = "cardtransactions"
	}

	// Convert all numeric fields that are strings to numbers
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, listKey,
		[]string{
			"cardtransactionId",
			"cardId",
			"walletId",
			"fees",
			"paymentAmount",
			"paymentLocalTime",
			"paymentLocalAmount",
			"panEntryMethod",
			"authorizationResponseCode",
			"authorizationMti",
			"authorizedBalance",
			"limitAtmYear",
			"limitAtmMonth",
			"limitAtmWeek",
			"limitAtmAll",
			"limitPaymentYear",
			"limitPaymentMonth",
			"limitPaymentWeek",
			"limitPaymentDay",
			"limitPaymentAll",
			"totalLimitAtmWeek",
			"totalLimitAtmDay",
			"totalLimitAtmAll",
			"totalLimitPaymentYear",
			"totalLimitPaymentMonth",
			"totalLimitPaymentWeek",
			"totalLimitPaymentDay",
			"totalLimitPaymentAll",
			"acquirerId",
		},
	)
	if err != nil {
		return nil, err
	}

	// Patch all boolean fields that are strings to booleans
	msg, _ = PatchBadStringFieldsThatShouldBeBoolean(msg, listKey,
		[]string{
			"is3DS",
		},
	)

	transactions := []*CardTransaction{}
	if err = json.Unmarshal([]byte(gjson.GetBytes(msg, listKey).Raw), &transactions); err != nil {
		return nil, err
	}

	// Populate the CreatedAt field
	for _, t := range transactions {
		if t.CreatedAt.IsZero() {
			t.CreatedAt = utils.DynamicFormatTime{Time: t.AuthorizationIssuerTime.Time}
		}
	}

	return transactions, nil
}

func MapCardTransactionToBankingTransaction(cardTransaction *CardTransaction) (banking.Transaction, error) {
	// Map
	transaction := banking.Transaction{
		TreezorId:                cardTransaction.TreezorId,
		TreezorCardTransactionId: cardTransaction.TreezorId,

		TreezorCardTransactionWebhookTimestamp: cardTransaction.TreezorWebhookTimestamp,

		Name:        cardTransaction.MerchantName,
		Description: cardTransaction.MerchantName,
	}

	transaction.TransactionType = banking.TransactionTypeCardTranscation
	transaction.TransactionTypeStr = banking.TransactionTypeCardTranscation.String()

	// Set the status based on the treezor paymentStatus
	switch cardTransaction.PaymentStatus {
	case "A":
		transaction.TransactionStatus = banking.TransactionStatusAuthorized
		transaction.TransactionStatusStr = banking.TransactionStatusAuthorized.String()
		transaction.TreezorWalletDebitId = cardTransaction.TreezorWalletId
		transaction.Description = "Achat"
	case "R":
		transaction.TransactionStatus = banking.TransactionStatusRefunded
		transaction.TransactionStatusStr = banking.TransactionStatusRefunded.String()
		transaction.TreezorWalletCreditId = cardTransaction.TreezorWalletId
		transaction.Description = "Remboursement"
	case "S", "M":
		transaction.TransactionStatus = banking.TransactionStatusSettled
		transaction.TransactionStatusStr = banking.TransactionStatusSettled.String()
		transaction.TreezorWalletDebitId = cardTransaction.TreezorWalletId
	case "C":
		// Doc: Settlement is received without a previous matching authorization request
		transaction.TransactionStatus = banking.TransactionStatusSettled
		transaction.TransactionStatusStr = banking.TransactionStatusSettled.String()
		transaction.TreezorWalletDebitId = cardTransaction.TreezorWalletId
	case "I":
		transaction.TransactionStatus = banking.TransactionStatusDeclined
		transaction.TransactionStatusStr = banking.TransactionStatusDeclined.String()
	case "V":
		// Doc: V (Reversed) may happen when an automated gas station authorized an amount greater than the actual amount of fuel pumped.
		transaction.TransactionStatus = banking.TransactionStatusDeclined
		transaction.TransactionStatusStr = banking.TransactionStatusDeclined.String()
		transaction.TreezorWalletCreditId = cardTransaction.TreezorWalletId
		transaction.Description = "Reverse"
	}

	// Set PostBalance
	transaction.PostBalance = strconv.FormatFloat(cardTransaction.AuthorizedBalance, 'f', -1, 64)

	// Convert the CreatedDate (with the date format of '2023-02-04 15:39:13') using Paris location
	transaction.CreatedAt = cardTransaction.CreatedAt.Time

	// Set the amount
	transaction.Amount = cardTransaction.PaymentAmount

	// Set the currency
	transaction.Currency = cardTransaction.PaymentCurrency

	// Set the createdAt
	transaction.CreatedAt = cardTransaction.AuthorizationIssuerTime.Time

	return transaction, nil
}

func SortCardTransactionsByCreatedAt(transactions []*CardTransaction) []*CardTransaction {
	// Sort by transaction.createdAt
	sort.Slice(transactions, func(i, j int) bool {
		return transactions[i].CreatedAt.Before(transactions[j].CreatedAt.Time)
	})
	return transactions
}

func (t *CardTransaction) ToMobileJSON() ([]byte, error) {
	if t.ID == "" {
		return json2.MarshalRemoveFields(t, "id")
	} else {
		return json2.MarshalRemoveFields(t)
	}
}
