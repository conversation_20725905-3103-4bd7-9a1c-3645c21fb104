package treezor

import (
	"encoding/json"
	"time"
	json2 "yochbee/_base/json"
)

// Balance is a snapshot of a wallet's balance at a given time.
type Balance struct {
	ID              string `bson:"_id"  json:"id"`
	TreezorWalletID int64  `bson:"treezorWalletId" json:"walletId"`

	CurrentBalance    float64 `bson:"currentBalance"     json:"currentBalance"`
	Authorizations    float64 `bson:"authorizations"     json:"authorizations"`
	AuthorizedBalance float64 `bson:"authorizedBalance"  json:"authorizedBalance"`
	Currency          string  `bson:"currency"           json:"currency"`
	CalculationDate   string  `bson:"calculationDate"    json:"calculationDate"`

	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (b *Balance) ToMobileJSON() ([]byte, error) {
	return json2.MarshalRemoveFields(b, "walletId")
}

func UnwrapBalancesFromTreezorResponse(msg []byte) ([]*Balance, error) {
	// Convert all int fields that are strings to numeric values.
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "balances", []string{"walletId", "currentBalance", "authorizations", "authorizedBalance"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Balances []*Balance `json:"balances"`
	}{}
	if err = json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Balances, nil
}
