package treezor

import (
	"encoding/json"
	"time"
	bus2 "yochbee/_base/bus"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type WebhookEvent struct {
	ID string `bson:"_id" json:"-"`

	EventName        string      `bson:"eventName" json:"webhook"`
	TreezorId        string      `bson:"treezorId" json:"webhook_id"`
	TreezorTimestamp int64       `bson:"treezorTimestamp" json:"webhook_created_at"`
	Payload          interface{} `bson:"payload" json:"object_payload"` // yes, this is an interface{} during unmarshaling, but will be turned into a string before storage
	PayloadSignature string      `bson:"payloadSignature" json:"object_payload_signature"`

	Processed bool `bson:"processed" json:"-"`

	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`
}

func MarkWebhookEventAsProcessed(signaling bus2.Client, treezorWebhookID string) error {
	L := logging.L.Named("MarkWebhookEventAsProcessed()").With(zap.String("treezorWebhookID", treezorWebhookID))
	marshalled, err := json.Marshal(map[string]string{"treezorWebhookID": treezorWebhookID})
	if err != nil {
		L.Error("Failed to marshal treezorWebhookID", zap.Error(err))
		return err
	}

	event := &bus2.Event{
		Topic:            "webhook.processed",
		Data:             marshalled,
		Time:             time.Now(),
		Retrigger:        false,
		WebhookID:        treezorWebhookID,
		WebhookTimestamp: time.Now().Unix(),
	}

	if err := signaling.PublishEvent(event); err != nil {
		L.Error("Failed to publish event", zap.Error(err))
		return err
	}
	return nil
}
