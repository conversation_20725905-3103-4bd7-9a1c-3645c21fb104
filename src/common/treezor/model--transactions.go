package treezor

import (
	"encoding/json"
	"time"
	_ "time/tzdata"
	"yochbee/common/banking"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type Transaction struct {
	ID string `bson:"_id" json:"id"`

	TreezorId             int64 `bson:"treezorId"              json:"transactionId"`
	TreezorWalletDebitId  int64 `bson:"treezorWalletDebitId"   json:"walletDebitId"`
	TreezorWalletCreditId int64 `bson:"treezorWalletCreditId"  json:"walletCreditId"`
	TreezorForeignId      int64 `bson:"treezorForeignId"       json:"foreignId"`

	TransactionType     string  `bson:"transactionType"        json:"transactionType"`
	Name                string  `bson:"name"                   json:"name"`
	Description         string  `bson:"description"            json:"description"`
	ValueDate           string  `bson:"valueDate"              json:"valueDate"`
	ExecutionDate       string  `bson:"executionDate"          json:"executionDate"`
	Amount              float64 `bson:"amount"                 json:"amount"`
	Currency            string  `bson:"currency"               json:"currency"`
	WalletDebitBalance  string  `bson:"walletDebitBalance"     json:"walletDebitBalance"`
	WalletCreditBalance string  `bson:"walletCreditBalance"    json:"walletCreditBalance"`
	CreatedDate         string  `bson:"createdDate"            json:"createdDate"`
	TotalRows           int     `bson:"totalRows"              json:"totalRows"`

	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func UnwrapTransactionsFromTreezorResponse(msg []byte) ([]*Transaction, error) {
	// Convert all int fields that are strings to numeric
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "transactions", []string{"transactionId", "walletDebitId", "walletCreditId", "foreignId", "amount", "totalRows"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Transactions []*Transaction `json:"transactions"`
	}{}
	if err = json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Transactions, nil
}

func (t *Transaction) MapToNewBankingTransaction() *banking.Transaction {
	logging.L.Info("Mapping from treezor.Transaction to banking.Transaction", zap.Any("treezorTransaction", t))
	bt := &banking.Transaction{
		TreezorId:             t.TreezorId,
		TreezorWalletDebitId:  t.TreezorWalletDebitId,
		TreezorWalletCreditId: t.TreezorWalletCreditId,

		Name:          t.Name,
		Description:   t.Description,
		ValueDate:     t.ValueDate,
		ExecutionDate: t.ExecutionDate,
		Amount:        t.Amount,
		Currency:      t.Currency,

		CreatedAt: time.Now(), // default time for now

		WebhookTimestamp: t.WebhookTimestamp,
	}

	if t.CreatedDate != "" {
		// Convert the CreatedDate (with the date format of '2023-02-04 15:39:13') using Paris location
		loc, err := time.LoadLocation("Europe/Paris")
		if err != nil {
			logging.L.Warn("Failed to load Paris location", zap.Error(err))
			bt.CreatedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", t.CreatedDate, time.FixedZone("Europe/Paris", 3600))
		} else {
			bt.CreatedAt, _ = time.ParseInLocation("2006-01-02 15:04:05", t.CreatedDate, loc)
		}
	}

	// Convert the Treezor transaction type to the banking.TransactionType
	//
	// Here are some sample data:
	//     {
	//      "transactionId": 4184364,
	//      "walletDebitId": 9,
	//      "walletCreditId": 2145901,
	//      "transactionType": "Payout Refund",
	//      "foreignId": "46",
	//      "name": "PayoutRefund: 46",
	//      "description": "Remboursement du payout 135028",
	//      "valueDate": "2023-07-11",
	//      "executionDate": "2023-07-11",
	//      "amount": "41.25",
	//      "walletDebitBalance": "0.00",
	//      "walletCreditBalance": "155.00",
	//      "currency": "EUR",
	//      "createdDate": "2023-07-11 14:11:53",
	//      "totalRows": "10"
	//    },
	//    {
	//      "transactionId": 3897189,
	//      "walletDebitId": 2145901,
	//      "walletCreditId": 9,
	//      "transactionType": "Payout",
	//      "foreignId": "135534",
	//      "name": "135534",
	//      "description": "Virement 135534",
	//      "valueDate": "2023-04-28",
	//      "executionDate": "2023-04-28",
	//      "amount": "1.25",
	//      "walletDebitBalance": "113.75",
	//      "walletCreditBalance": "0.00",
	//      "currency": "EUR",
	//      "createdDate": "2023-04-28 15:07:05",
	//      "totalRows": ""
	//    }
	switch t.TransactionType {
	case "Payin", "SCTR Inst":
		bt.TransactionType = banking.TransactionTypePayin
		bt.TransactionTypeStr = banking.TransactionTypePayin.String()
		if bt.TransactionStatus == 0 {
			bt.TransactionStatus = banking.TransactionStatusSuccess
			bt.TransactionStatusStr = banking.TransactionStatusSuccess.String()
		}
		bt.TreezorPayinId = t.TreezorForeignId
		if f, ok := bt.TreezorPayinId.(float64); ok {
			bt.TreezorPayinId = int64(f)
		}
		bt.PostBalance = t.WalletCreditBalance
	case "Payout":
		bt.TransactionType = banking.TransactionTypePayout
		bt.TransactionTypeStr = banking.TransactionTypePayout.String()
		if bt.TransactionStatus == 0 {
			bt.TransactionStatus = banking.TransactionStatusSuccess
			bt.TransactionStatusStr = banking.TransactionStatusSuccess.String()
		}
		bt.TreezorPayoutId = t.TreezorForeignId
		if f, ok := bt.TreezorPayoutId.(float64); ok {
			bt.TreezorPayoutId = int64(f)
		}
		bt.Amount = -bt.Amount
		bt.PostBalance = t.WalletDebitBalance
	case "Payout Refund":
		bt.TransactionType = banking.TransactionTypePayoutRefund
		bt.TransactionTypeStr = banking.TransactionTypePayoutRefund.String()
		if bt.TransactionStatus == 0 {
			bt.TransactionStatus = banking.TransactionStatusRefunded
			bt.TransactionStatusStr = banking.TransactionStatusRefunded.String()
		}
		bt.TreezorPayoutId = t.TreezorForeignId
		if f, ok := bt.TreezorPayoutId.(float64); ok {
			bt.TreezorPayoutId = int64(f)
		}
		bt.PostBalance = t.WalletCreditBalance
	case "Card transaction":
		bt.PostBalance = "0"
		if bt.Amount < 0 {
			bt.TransactionType = banking.TransactionTypeCardDebit
			bt.TransactionTypeStr = banking.TransactionTypeCardDebit.String()
		} else {
			bt.TransactionType = banking.TransactionTypeCardCredit
			bt.TransactionTypeStr = banking.TransactionTypeCardCredit.String()
		}
		bt.TreezorCardTransactionId = t.TreezorForeignId
	default:
		logging.L.Warn("Uncovered transaction type", zap.String("transactionType", t.TransactionType))
	}

	return bt
}
