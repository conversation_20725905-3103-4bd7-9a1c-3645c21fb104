package treezor

import (
	"encoding/json"
	"time"
)

type Document struct {
	ID string `bson:"_id"    json:"id"`

	TreezorUserId     int64 `bson:"treezorUserId"     json:"userId"`
	TreezorDocumentId int64 `bson:"treezorDocumentId" json:"documentId"`

	UserFirstName string `bson:"userFirstName" json:"userFirstName"`
	UserLastName  string `bson:"userLastName" json:"userLastName"`
	CodeStatus    string `bson:"codeStatus" json:"codeStatus"`

	ClientId    int64 `bson:"clientId" json:"clientId"`
	ResidenceId int64 `bson:"residenceId" json:"residenceId"`

	DocumentStatus string `bson:"documentStatus" json:"documentStatus"`
	DocumentTypeId int    `bson:"documentTypeId" json:"documentTypeId"`
	DocumentType   string `bson:"documentType" json:"documentType"`
	TotalRows      int    `bson:"totalRows" json:"totalRows"`

	FileName     string `bson:"fileName" json:"fileName"`
	FileSize     int64  `bson:"fileSize" json:"fileSize"`
	TemporaryUrl string `bson:"temporaryUrl" json:"temporaryUrl"`

	CreatedDate  string `bson:"createdDate" json:"createdDate"`
	ModifiedDate string `bson:"modifiedDate" json:"modifiedDate"`

	// We need our own createdAt field for sorting and logging purposes
	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (d *Document) GetTreezorID() (string, interface{}) {
	return "treezorDocumentId", d.TreezorDocumentId
}

func (d *Document) TreezorTimestamp() int64 {
	return d.WebhookTimestamp
}

func (d *Document) SetTreezorTimestamp(ts int64) {
	d.WebhookTimestamp = ts
}

func (d *Document) SetInsertID(id string) {
	d.ID = id
	d.CreatedAt = time.Now()
}

func (d *Document) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*Document)

	now := time.Now()
	d.UpdatedAt = &now
	d.WebhookTimestamp = in.WebhookTimestamp

	d.TreezorUserId = in.TreezorUserId
	d.TreezorDocumentId = in.TreezorDocumentId

	d.UserFirstName = in.UserFirstName
	d.UserLastName = in.UserLastName
	d.CodeStatus = in.CodeStatus

	d.ClientId = in.ClientId
	d.ResidenceId = in.ResidenceId

	d.DocumentStatus = in.DocumentStatus
	d.DocumentTypeId = in.DocumentTypeId
	d.DocumentType = in.DocumentType
	d.TotalRows = in.TotalRows

	d.FileName = in.FileName
	d.FileSize = in.FileSize
	d.TemporaryUrl = in.TemporaryUrl

	d.CreatedDate = in.CreatedDate
	d.ModifiedDate = in.ModifiedDate
}

func UnwrapDocumentsFromTreezorResponse(msg []byte) ([]*Document, error) {
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "documents", []string{"userId", "documentId", "clientId", "residenceId", "documentTypeId", "totalRows", "fileSize"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Documents []*Document `json:"documents"`
	}{}
	if err := json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Documents, nil
}
