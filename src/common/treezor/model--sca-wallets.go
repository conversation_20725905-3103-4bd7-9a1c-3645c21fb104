package treezor

import (
	"encoding/json"
	"time"
	json2 "yochbee/_base/json"
)

// SCAWallet is based on the structure in the Treezor API documentation: https://docs.treezor.com/guide/strong-customer-authentication/sca-wallets.html#manual-creation
// For some reason, the Treezor API docs states a different type of object in the webhook: https://docs.treezor.com/guide/users/events.html#user-s-sca-wallets ... a less complete version of the object. Thus, we use the more complete version from the SCAWallets API docs (the first link).
//
// Each used has only one SCAWallet, and it is created when the user is created.
type SCAWallet struct {
	ID                        string       `bson:"_id" json:"id"`
	TreezorId                 string       `bson:"treezorId" json:"treezorId"`
	Status                    string       `bson:"status" json:"status"`
	SubStatus                 string       `bson:"subStatus" json:"subStatus"`
	PasscodeStatus            string       `bson:"passcodeStatus" json:"passcodeStatus"`
	Locked                    bool         `bson:"locked" json:"locked"`
	LockReasons               []string     `bson:"lockReasons" json:"lockReasons"`
	LockMessage               string       `bson:"lockMessage" json:"lockMessage"`
	SettingsProfile           string       `bson:"settingsProfile" json:"settingsProfile"`
	ActivationCode            string       `bson:"activationCode" json:"activationCode"`
	CreationDate              *time.Time   `bson:"creationDate" json:"creationDate"`
	ActivationDate            *time.Time   `bson:"activationDate" json:"activationDate"`
	DeletionDate              *time.Time   `bson:"deletionDate" json:"deletionDate"`
	ActivationCodeExpiryDate  *time.Time   `bson:"activationCodeExpiryDate" json:"activationCodeExpiryDate"`
	AuthenticationMethods     []AuthMethod `bson:"authenticationMethods" json:"authenticationMethods"`
	InvalidActivationAttempts interface{}  `bson:"invalidActivationAttempts" json:"invalidActivationAttempts"`
	UserID                    string       `bson:"userId" json:"userId"`
	SCAWalletTag              string       `bson:"scaWalletTag" json:"scaWalletTag"`
	ClientID                  string       `bson:"clientId" json:"clientId"`
	CreatedAt                 time.Time    `bson:"createdAt" json:"createdAt"`
}

type AuthMethod struct {
	Type       string      `bson:"type" json:"type"`
	Usages     []string    `bson:"usages" json:"usages"`
	Parameters interface{} `bson:"parameters" json:"parameters"`
}

// UnwrapSingleSCAWalletFromTreezorResponse is a helper function to unwrap a single SCAWallet from a Treezor response.
// It is the responsibility of the caller to ensure that the ID is set correctly.
func UnwrapSingleSCAWalletFromTreezorResponse(msg []byte) (*SCAWallet, error) {
	wallet := &SCAWallet{}
	if err := json.Unmarshal(msg, &wallet); err != nil {
		return nil, err
	}

	wallet.TreezorId = wallet.ID
	wallet.ID = ""

	wallet.CreatedAt = time.Now()
	return wallet, nil
}

func (w *SCAWallet) ToMobileJSON() ([]byte, error) {
	// The "id" field is ours, but we will return the Treezor ID as the "id" field.
	return json2.MarshalRemoveFields(w, "id", "createdAt", "userId")
}
