package treezor

import (
	"encoding/json"
	"time"
)

type SepaSctrInstr struct {
	WalletID              int64     `json:"walletId"               bson:"walletId"`
	MessageID             string    `json:"messageId"              bson:"messageId"`
	TransactionEndToEndID string    `json:"transactionEndToEndId"  bson:"transactionEndToEndId"`
	TransactionID         string    `json:"transactionId"          bson:"transactionId"`
	SettlementAmount      float64   `json:"settlementAmount"       bson:"settlementAmount"`
	SettlementDate        time.Time `json:"settlementDate"         bson:"settlementDate"`
	DebtorIban            string    `json:"debtorIban"             bson:"debtorIban"`
	DebtorBic             string    `json:"debtorBic"              bson:"debtorBic"`
	DebitorName           string    `json:"debitorName"            bson:"debitorName"`
	DebitorAddressLine    string    `json:"debitorAddressLine"     bson:"debitorAddressLine"`
	DebitorCountry        string    `json:"debitorCountry"         bson:"debitorCountry"`
	CreditorIban          string    `json:"creditorIban"           bson:"creditorIban"`
	CreditorBIC           string    `json:"creditorBIC"            bson:"creditorBIC"`
	CreditorName          string    `json:"creditorName"           bson:"creditorName"`
	CreditorAddressLine   string    `json:"creditorAddressLine"    bson:"creditorAddressLine"`
	CreditorCountry       string    `json:"creditorCountry"        bson:"creditorCountry"`
	RemittanceInformation string    `json:"remittanceInformation"  bson:"remittanceInformation"`
	ReturnReasonCode      string    `json:"return_reason_code"     bson:"returnReasonCode"`
	VirtualIbanID         int64     `json:"virtualIbanId"          bson:"virtualIbanId"`
	VirtualIbanReference  string    `json:"virtualIbanReference"   bson:"virtualIbanReference"`
	WebhookTimestamp      int64     `json:"webhookTimestamp"       bson:"webhookTimestamp"`
}

func UnwrapSepaSctrInstEntriesFromTreezorResponse(msg []byte) ([]*SepaSctrInstr, error) {
	// Convert all int fields that are strings to ints
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "sepaSctrInsts", []string{
		"walletId",
		"settlementAmount",
		"virtualIbanId",
	})
	if err != nil {
		return nil, err
	}

	// Unmarshal into a list of SepaSctrInstr
	unmarshalled := struct {
		Entries []*SepaSctrInstr `json:"sepaSctrInsts"`
	}{}
	if err = json.Unmarshal(msg, &unmarshalled); err != nil {
		return nil, err
	}

	return unmarshalled.Entries, nil
}
