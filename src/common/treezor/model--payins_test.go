package treezor

import "testing"

func TestPayinsNumericStringId(t *testing.T) {
	treezorPayload := `
{
  "payins": [
    {
      "payinId": "8400867",
      "payinTag": "",
      "walletId": "2319554",
      "userId": "865508",
      "payinStatus": "PENDING",
      "paymentMethodId": "26",
      "messageToUser": "",
      "subtotalItems": "0.00",
      "subtotalServices": "0.00",
      "subtotalTax": "0.00",
      "amount": "200.00",
      "currency": "EUR",
      "createdDate": "2021-04-12 15:58:19",
      "walletEventName": "David MELOTTE Shine",
      "walletAlias": "david-melotte-shine-5eb95a5eb4246",
      "userFirstname": "Prestataire Chu00e8que",
      "userLastname": "",
      "codeStatus": "140001",
      "informationStatus": "",
      "refundAmount": null,
      "DbtrIBAN": null,
      "forwardUrl": null,
      "paymentAcceptedUrl": null,
      "paymentRefusedUrl": null,
      "paymentWaitingUrl": null,
      "paymentExceptionUrl": null,
      "paymentCanceledUrl": null,
      "payinDate": "0000-00-00",
      "mandateId": "0",
      "creditorName": null,
      "creditorAddressLine": null,
      "creditorCountry": null,
      "creditorIban": null,
      "creditorBIC": null,
      "virtualIbanId": null,
      "virtualIbanReference": null,
      "additionalData": {
        "cheque": {
          "cmc7": {
            "a": "0557016",
            "b": "045010041908",
            "c": "001071920333"
          },
          "drawerData": {
            "firstName": "M. LENIERE J.MARIE",
            "isNaturalPerson": true
          },
          "RLMCKey": "62"
        }
      },
      "paymentHtml": null
    },
    {
      "payinId": 100,
      "payinTag": "",
      "walletId": "2319554",
      "userId": "865508",
      "payinStatus": "PENDING",
      "paymentMethodId": "26",
      "messageToUser": "",
      "subtotalItems": "0.00",
      "subtotalServices": "0.00",
      "subtotalTax": "0.00",
      "amount": "200.00",
      "currency": "EUR",
      "createdDate": "2021-04-12 15:58:19",
      "walletEventName": "David MELOTTE Shine",
      "walletAlias": "david-melotte-shine-5eb95a5eb4246",
      "userFirstname": "Prestataire Chu00e8que",
      "userLastname": "",
      "codeStatus": "140001",
      "informationStatus": "",
      "refundAmount": null,
      "DbtrIBAN": null,
      "forwardUrl": null,
      "paymentAcceptedUrl": null,
      "paymentRefusedUrl": null,
      "paymentWaitingUrl": null,
      "paymentExceptionUrl": null,
      "paymentCanceledUrl": null,
      "payinDate": "0000-00-00",
      "mandateId": "0",
      "creditorName": null,
      "creditorAddressLine": null,
      "creditorCountry": null,
      "creditorIban": null,
      "creditorBIC": null,
      "virtualIbanId": null,
      "virtualIbanReference": null,
      "additionalData": {
        "cheque": {
          "cmc7": {
            "a": "0557016",
            "b": "045010041908",
            "c": "001071920333"
          },
          "drawerData": {
            "firstName": "M. LENIERE J.MARIE",
            "isNaturalPerson": true
          },
          "RLMCKey": "62"
        }
      },
      "paymentHtml": null
    },
    {
      "payinId": "2fb72ab4-7917-4783-a2c3-22e8de3c2a29",
      "payinTag": "",
      "walletId": "2319554",
      "userId": "865508",
      "payinStatus": "PENDING",
      "paymentMethodId": "26",
      "messageToUser": "",
      "subtotalItems": "0.00",
      "subtotalServices": "0.00",
      "subtotalTax": "0.00",
      "amount": "200.00",
      "currency": "EUR",
      "createdDate": "2021-04-12 15:58:19",
      "walletEventName": "David MELOTTE Shine",
      "walletAlias": "david-melotte-shine-5eb95a5eb4246",
      "userFirstname": "Prestataire Chu00e8que",
      "userLastname": "",
      "codeStatus": "140001",
      "informationStatus": "",
      "refundAmount": null,
      "DbtrIBAN": null,
      "forwardUrl": null,
      "paymentAcceptedUrl": null,
      "paymentRefusedUrl": null,
      "paymentWaitingUrl": null,
      "paymentExceptionUrl": null,
      "paymentCanceledUrl": null,
      "payinDate": "0000-00-00",
      "mandateId": "0",
      "creditorName": null,
      "creditorAddressLine": null,
      "creditorCountry": null,
      "creditorIban": null,
      "creditorBIC": null,
      "virtualIbanId": null,
      "virtualIbanReference": null,
      "additionalData": {
        "cheque": {
          "cmc7": {
            "a": "0557016",
            "b": "045010041908",
            "c": "001071920333"
          },
          "drawerData": {
            "firstName": "M. LENIERE J.MARIE",
            "isNaturalPerson": true
          },
          "RLMCKey": "62"
        }
      },
      "paymentHtml": null
    }
  ]
}
`
	payins, err := UnwrapPayinsFromTreezorResponse([]byte(treezorPayload))
	if err != nil {
		t.Fatal(err)
	}

	if len(payins) != 3 {
		t.Fatalf("expected 3 payins, got %d", len(payins))
	}

	if v, ok := payins[0].TreezorPayinID.(int64); !ok || v != 8400867 {
		t.Fatal("Expected payinId to be int64(8400867), got", v)
	}

	if v, ok := payins[1].TreezorPayinID.(int64); !ok || v != 100 {
		t.Fatal("Expected payinId to be int64(100), got", v)
	}

	if v, ok := payins[2].TreezorPayinID.(string); !ok || v != "2fb72ab4-7917-4783-a2c3-22e8de3c2a29" {
		t.Fatal("Expected payinId to be string(2fb72ab4-7917-4783-a2c3-22e8de3c2a29), got", v)
	}
}
