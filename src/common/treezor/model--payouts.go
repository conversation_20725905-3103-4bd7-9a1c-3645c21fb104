package treezor

import (
	"encoding/json"
	"time"
	"yochbee/common/banking"

	json2 "yochbee/_base/json"

	"github.com/eliezedeck/gobase/logging"
	"go.uber.org/zap"
)

type Payout struct {
	ID string `bson:"_id" json:"id"`

	TreezorPayoutId      int64 `bson:"treezorPayoutId"      json:"payoutId"`
	TreezorUserId        int64 `bson:"treezorUserId"        json:"userId"`
	TreezorWalletId      int64 `bson:"treezorWalletId"      json:"walletId"`
	TreezorBeneficiaryId int64 `bson:"treezorBeneficiaryId" json:"beneficiaryId"`

	Status            string  `bson:"status"             json:"payoutStatus"`
	PayoutTypeId      int     `bson:"payoutTypeId"       json:"payoutTypeId"`
	PayoutType        string  `bson:"payoutType"         json:"payoutType"`
	CodeStatus        string  `bson:"codeStatus"         json:"codeStatus"`
	BankAccountId     int64   `bson:"bankAccountId"      json:"bankaccountId"`
	Label             string  `bson:"label"              json:"label"`
	PayoutDate        string  `bson:"payoutDate"         json:"payoutDate"`
	Amount            float64 `bson:"amount"             json:"amount"`
	Currency          string  `bson:"currency"           json:"currency"`
	PartnerFee        string  `bson:"partnerFee"         json:"partnerFee"`
	CreatedDate       string  `bson:"createdDate"        json:"createdDate"`
	ModifiedDate      string  `bson:"modifiedDate"       json:"modifiedDate"`
	WalletEventName   string  `bson:"walletEventName"    json:"walletEventName"`
	WalletAlias       string  `bson:"walletAlias"        json:"walletAlias"`
	BankAccountIBAN   string  `bson:"bankAccountIBAN"    json:"bankaccountIBAN"`
	InformationStatus string  `bson:"informationStatus"  json:"informationStatus"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (p *Payout) GetTreezorID() (string, interface{}) {
	return "treezorPayoutId", p.TreezorPayoutId
}

func (p *Payout) TreezorTimestamp() int64 {
	return p.WebhookTimestamp
}

func (p *Payout) SetTreezorTimestamp(ts int64) {
	p.WebhookTimestamp = ts
}

func (p *Payout) SetInsertID(id string) {
	p.ID = id
	p.CreatedAt = time.Now()
}

func (p *Payout) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*Payout)

	now := time.Now()
	p.UpdatedAt = &now
	p.WebhookTimestamp = in.WebhookTimestamp

	// Treezor IDs are left intact

	p.Status = in.Status
	p.PayoutTypeId = in.PayoutTypeId
	p.PayoutType = in.PayoutType
	p.CodeStatus = in.CodeStatus
	p.BankAccountId = in.BankAccountId
	p.Label = in.Label
	p.PayoutDate = in.PayoutDate
	p.Amount = in.Amount
	p.Currency = in.Currency
	p.PartnerFee = in.PartnerFee
	p.CreatedDate = in.CreatedDate
	p.ModifiedDate = in.ModifiedDate
	p.WalletEventName = in.WalletEventName
	p.WalletAlias = in.WalletAlias
	p.BankAccountIBAN = in.BankAccountIBAN
	p.InformationStatus = in.InformationStatus
}

func (p *Payout) ToMobileJSON() ([]byte, error) {
	return json2.MarshalRemoveFields(p,
		"id",
		"rawEvent",
		"payoutId",
		"payoutTypeId",
		"payoutType",
		"userId",
		"walletId",
		"beneficiaryId",
		"bankaccountId",
		"createdDate",
		"modifiedDate",
		"partnerFee",
		"bankaccountIBAN",
		"treezorWebhookTimestamp",
	)
}

func (p *Payout) EnrichBankingTransaction(t *banking.Transaction) bool {
	if t.TreezorPayinWebhookTimestamp != 0 && t.TreezorPayinWebhookTimestamp > p.WebhookTimestamp {
		logging.L.Info("Payout.EnrichBankingTransaction: Payout is older than the Transaction", zap.Any("payout", p), zap.Any("transaction", t))
		return false
	}

	t.TreezorPayoutWebhookTimestamp = p.WebhookTimestamp

	t.TreezorPayoutId = p.TreezorPayoutId
	if f, ok := t.TreezorPayoutId.(float64); ok {
		t.TreezorPayoutId = int64(f)
	}
	t.TreezorPayoutTypeID = p.PayoutTypeId
	t.TransactionType = banking.TransactionTypePayout
	t.TransactionTypeStr = banking.TransactionTypePayout.String()
	t.Name = p.WalletEventName
	t.Amount = -p.Amount
	t.Description = ""
	t.Currency = p.Currency

	// Improve the label on the mobile app
	switch p.PayoutTypeId {
	case 1:
		// SEPA to a Benerificiary (SCTE)
		t.Name = "Virement SEPA vers bénéficiaire (SCTE)"
	}

	switch p.Status {
	case "PENDING":
		t.TransactionStatus = banking.TransactionStatusPending
		t.TransactionStatusStr = banking.TransactionStatusPending.String()
	case "CANCELED":
		t.TransactionStatus = banking.TransactionStatusCanceled
		t.TransactionStatusStr = banking.TransactionStatusCanceled.String()
	case "VALIDATED":
		t.TransactionStatus = banking.TransactionStatusSuccess
		t.TransactionStatusStr = banking.TransactionStatusSuccess.String()
	default:
		logging.L.Warn("Payout.EnrichBankingTransaction: Uncovered PayoutStatus", zap.String("status", p.Status), zap.Any("payout", p))
	}

	logging.L.Info("Payout.EnrichBankingTransaction: Synced Payout to Transaction", zap.Any("payout", p), zap.Any("transaction", t))
	return true
}

func UnwrapPayoutsFromTreezorResponse(msg []byte) ([]*Payout, error) {
	// Convert all int fields that are strings to ints
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "payouts", []string{
		"amount",
		"payoutId",
		"userId",
		"walletId",
		"beneficiaryId",
		"payoutTypeId",
		"bankaccountId"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Payouts []*Payout `json:"payouts"`
	}{}
	if err := json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Payouts, nil
}

type PayoutWallet2BeneficiaryDTO struct {
	WalletId      int64   `json:"walletId"`
	BeneficiaryId int64   `json:"beneficiaryId"`
	Amount        float64 `json:"amount"`
	Currency      string  `json:"currency"` // ISO 4217
	Label         string  `json:"label"`
}
