package treezor

import "testing"

func TestUnwrapOperationsFromTreezorResponse(t *testing.T) {
	data := `{
  "data": [
    {
      "operationType": "cardTransaction",
      "amount": {
        "amount": 30,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "DEBIT",
      "objectId": "405230993",
      "label": "CAFE CUP",
      "externalReference": null,
      "metadata": {
        "cardPayment": {
          "mcc": {
            "code": 5499
          },
          "mid": {
            "value": "3960895"
          },
          "localAmount": {
            "amount": 30,
            "currency": "EUR"
          },
          "authorizationNote": "",
          "authorisationResponseCode": {
            "action": "Approve",
            "description": "APPROVED",
            "value": 0
          }
        }
      },
      "status": "AUTHORIZED",
      "date": {
        "creation": "2024-06-20T15:38:58+02:00"
      }
    },
    {
      "operationType": "cardTransaction",
      "amount": {
        "amount": 30,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "DEBIT",
      "objectId": "405126641",
      "label": "CAFE CUP",
      "externalReference": null,
      "metadata": {
        "cardPayment": {
          "mcc": {
            "code": 5499
          },
          "mid": {
            "value": "3960895"
          },
          "localAmount": {
            "amount": 30,
            "currency": "EUR"
          },
          "authorizationNote": "",
          "authorisationResponseCode": {
            "action": "Approve",
            "description": "APPROVED",
            "value": 0
          }
        }
      },
      "status": "AUTHORIZED",
      "date": {
        "creation": "2024-06-20T12:04:31+02:00"
      }
    },
    {
      "operationType": "bankTransfer",
      "amount": {
        "amount": 1000,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "CREDIT",
      "objectId": "c17ca27e-f242-548d-9fe1-757b303313c8",
      "label": "Bank Transfer (c17ca27e-f242-548d-9fe1-757b303313c8)",
      "externalReference": null,
      "metadata": null,
      "status": "SETTLED",
      "date": {
        "creation": "2024-06-06T18:30:13+02:00"
      }
    },
    {
      "operationType": "bankTransfer",
      "amount": {
        "amount": 200,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "CREDIT",
      "objectId": "1735fd54-a533-5d03-a868-6eb64275f9d3",
      "label": "Bank Transfer (1735fd54-a533-5d03-a868-6eb64275f9d3)",
      "externalReference": null,
      "metadata": null,
      "status": "SETTLED",
      "date": {
        "creation": "2024-05-27T22:51:37+02:00"
      }
    },
    {
      "operationType": "cardTransaction",
      "amount": {
        "amount": 140,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "CREDIT",
      "objectId": "*********",
      "label": "CARREFOUR MARKET",
      "externalReference": null,
      "metadata": {
        "cardPayment": {
          "mcc": {
            "code": 5411
          },
          "mid": {
            "value": "********       "
          },
          "localAmount": {
            "amount": -140,
            "currency": "EUR"
          },
          "authorizationNote": " Refund\n",
          "authorisationResponseCode": {
            "action": "Approve",
            "description": "APPROVED",
            "value": 0
          }
        }
      },
      "status": "SETTLED",
      "date": {
        "creation": "2023-11-03T06:52:41+01:00",
        "settlement": "2023-11-03T01:00:00+01:00"
      }
    },
    {
      "operationType": "cardTransaction",
      "amount": {
        "amount": 198,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "DEBIT",
      "objectId": "320624430",
      "label": "INTERSPORT",
      "externalReference": null,
      "metadata": {
        "cardPayment": {
          "mcc": {
            "code": 5941
          },
          "mid": {
            "value": "08876198       "
          },
          "localAmount": {
            "amount": 198,
            "currency": "EUR"
          },
          "authorizationNote": "",
          "authorisationResponseCode": {
            "action": "Approve",
            "description": "APPROVED",
            "value": 0
          }
        }
      },
      "status": "SETTLED",
      "date": {
        "creation": "2023-10-29T06:36:46+01:00",
        "settlement": "2023-10-29T02:00:00+02:00"
      }
    },
    {
      "operationType": "cardTransaction",
      "amount": {
        "amount": 1198,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "DEBIT",
      "objectId": "320667005",
      "label": "PHARMACIE MAS",
      "externalReference": null,
      "metadata": {
        "cardPayment": {
          "mcc": {
            "code": 5912
          },
          "mid": {
            "value": "2915373"
          },
          "localAmount": {
            "amount": 1198,
            "currency": "EUR"
          },
          "authorizationNote": "",
          "authorisationResponseCode": {
            "action": "Approve",
            "description": "APPROVED",
            "value": 0
          }
        }
      },
      "status": "SETTLED",
      "date": {
        "creation": "2023-10-28T14:05:45+02:00",
        "settlement": "2023-10-30T01:00:00+01:00"
      }
    },
    {
      "operationType": "bankTransfer",
      "amount": {
        "amount": 1000,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "CREDIT",
      "objectId": "d2b218a2-8159-5af9-a2bc-ac8fa939e843",
      "label": "Bank Transfer (d2b218a2-8159-5af9-a2bc-ac8fa939e843)",
      "externalReference": null,
      "metadata": null,
      "status": "SETTLED",
      "date": {
        "creation": "2023-10-26T19:32:20+02:00"
      }
    },
    {
      "operationType": "bankTransfer",
      "amount": {
        "amount": 500,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "DEBIT",
      "objectId": "********",
      "label": "Bank Transfer (********)",
      "externalReference": null,
      "metadata": null,
      "status": "SETTLED",
      "date": {
        "creation": "2023-09-14T17:42:28+02:00",
        "settlement": "2023-09-15T02:00:00+02:00"
      }
    },
    {
      "operationType": "bankTransfer",
      "amount": {
        "amount": 1000,
        "currency": "EUR"
      },
      "walletId": ********,
      "direction": "CREDIT",
      "objectId": "74a3f941-bfaa-505b-8942-f35ac36b1ed2",
      "label": "Bank Transfer (74a3f941-bfaa-505b-8942-f35ac36b1ed2)",
      "externalReference": null,
      "metadata": null,
      "status": "SETTLED",
      "date": {
        "creation": "2023-09-13T11:05:01+02:00"
      }
    }
  ],
  "cursor": {
    "prev": null,
    "current": "356a192b7913b04c54574d18c28d46e6395428ab",
    "next": null
  }
}`
	operations, err := UnwrapOperationsFromTreezorResponse([]byte(data), "data")
	if err != nil {
		t.Fatalf("UnwrapOperationsFromTreezorResponse() failed: %s", err)
	}
	if len(operations) != 10 {
		t.Fatalf("UnwrapOperationsFromTreezorResponse() failed: expected 10 operations, got %d", len(operations))
	}

	// Test first operation
	if operations[0].OperationType != "cardTransaction" {
		t.Fatalf("UnwrapOperationsFromTreezorResponse() failed: expected first operation to be of type 'cardTransaction', got '%s'", operations[0].OperationType)
	}
	if operations[0].Amount.Amount != 30 {
		t.Fatalf("UnwrapOperationsFromTreezorResponse() failed: expected first operation amount to be 30, got %f", operations[0].Amount.Amount)
	}
	if operations[0].ObjectId != 405230993 {
		t.Fatalf("UnwrapOperationsFromTreezorResponse() failed: expected first operation object ID to be the number 405230993, got '%d'", operations[0].ObjectId)
	}
}
