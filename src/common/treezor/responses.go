package treezor

import (
	"errors"
	"fmt"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// TreezorReturnValues holds the status code, error code, and messages returned from a call to Treezor.
type TreezorReturnValues struct {
	StatusCode int
	ErrorCode  string
	ErrorMsg   string
}

func PatchBadStringFieldsThatShouldBeNumbers(msg []byte, listKey string, intFieldList []string) ([]byte, error) {
	gList := gjson.GetBytes(msg, listKey)
	if !gList.IsArray() {
		return nil, errors.New("must give a JSON []byte that contains a list of objects")
	}
	var err error
	for i, a := range gList.Array() {
		for _, f := range intFieldList {
			msg, err = sjson.SetBytes(msg, fmt.Sprintf("%s.%d.%s", listKey, i, f), a.Get(f).Float())
			if err != nil {
				return nil, err
			}
		}
	}
	return msg, nil
}

func PatchBadStringFieldsThatShouldBeBoolean(msg []byte, listKey string, boolFieldList []string) ([]byte, error) {
	gList := gjson.GetBytes(msg, listKey)
	if !gList.IsArray() {
		return nil, errors.New("must give a JSON []byte that contains a list of objects")
	}
	var err error
	for i, a := range gList.Array() {
		for _, f := range boolFieldList {
			msg, err = sjson.SetBytes(msg, fmt.Sprintf("%s.%d.%s", listKey, i, f), a.Get(f).Bool())
			if err != nil {
				return nil, err
			}
		}
	}
	return msg, nil
}

func PatchBadStringFieldsThatShouldBeNumbersInSingleObject(msg []byte, intFieldList []string) ([]byte, error) {
	var err error
	for _, f := range intFieldList {
		msg, err = sjson.SetBytes(msg, f, gjson.GetBytes(msg, f).Float())
		if err != nil {
			return nil, err
		}
	}
	return msg, nil
}
