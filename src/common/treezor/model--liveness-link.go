package treezor

import "time"

type LivenessLink struct {
	ID                string     `bson:"_id"               json:"id"`
	IdentificationId  string     `bson:"identificationId"  json:"identificationId"`
	IdentificationUrl string     `bson:"identificationUrl" json:"identificationUrl"`
	AccountId         string     `bson:"accountId"         json:"accountId"`
	UserId            int64      `bson:"userId"            json:"userId"`
	CreatedAt         time.Time  `bson:"createdAt"         json:"createdAt"`
	UpdatedAt         *time.Time `bson:"updatedAt"         json:"updatedAt"`

	Raw string `bson:"raw" json:"raw"`
}
