package treezor

import (
	"encoding/json"
	"time"
)

// LivenessStatus is a snapshot that represents the status of KYC liveness review for a User. As you may see here:
// https://docs.treezor.com/guide/users/events.html#kyc-liveness
// the status is apparently per User, not per Document. A reminder that a KYC Liveness is just a process to acquire the
// required documents for a User.
//
// This entry is a CREATE-ONLY entry in the `treezor-liveness` collection. The `createdAt` field can be used for getting
// the latest/current state. This is so that each entry is a point in history.
type LivenessStatus struct {
	ID string `bson:"_id" json:"id"`

	TreezorUserId int64 `bson:"treezorUserId" json:"user_id"`

	Status  string `bson:"status" json:"kyc-status"`
	Comment string `bson:"comment" json:"comment"`

	// Score is one of the most important fields in the LivenessStatus. When the value == 1, it was all good: we can
	// move forward to asking Treezor to start their own KYC review.
	Score int `bson:"score" json:"score"`

	ReviewStartedAt string `bson:"reviewStartedAt" json:"started-at"`
	ReviewUpdatedAt string `bson:"reviewUpdatedAt" json:"updated-at"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (l *LivenessStatus) GetTreezorID() (string, interface{}) {
	return "treezorUserId", l.TreezorUserId
}

func (l *LivenessStatus) TreezorTimestamp() int64 {
	return l.WebhookTimestamp
}

func (l *LivenessStatus) SetTreezorTimestamp(ts int64) {
	l.WebhookTimestamp = ts
}

func (l *LivenessStatus) SetInsertID(id string) {
	l.ID = id
	l.CreatedAt = time.Now()
}

func (l *LivenessStatus) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*LivenessStatus)

	now := time.Now()
	l.UpdatedAt = &now
	l.WebhookTimestamp = in.WebhookTimestamp

	l.TreezorUserId = in.TreezorUserId
	l.Status = in.Status
	l.Comment = in.Comment
	l.Score = in.Score
	l.ReviewStartedAt = in.ReviewStartedAt
	l.ReviewUpdatedAt = in.ReviewUpdatedAt
}

func UnwrapLivenessStatusFromTreezorResponse(msg []byte) (*LivenessStatus, error) {
	msg, err := PatchBadStringFieldsThatShouldBeNumbersInSingleObject(msg, []string{"user_id", "score"})
	if err != nil {
		return nil, err
	}
	var status LivenessStatus
	if err := json.Unmarshal(msg, &status); err != nil {
		return nil, err
	}
	return &status, nil
}
