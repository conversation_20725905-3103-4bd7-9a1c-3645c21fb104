package treezor

import "testing"

func TestUnwrapSepaSctrInstEntriesFromTreezorResponse(t *testing.T) {
	data := `{
		"sepaSctrInsts": [
			{
				"walletId": "2145901",
				"messageId": "a9bb88cd-dddb-11ed-8213-ceb159690977",
				"transactionEndToEndId": "a9bb88cd-dddb-11ed-8213-ceb159690977",
				"transactionId": "a9bba61b-dddb-11ed-8213-ceb159690977",
				"settlementAmount": "15.00",
				"settlementDate": "2023-04-18T00:00:00Z",
				"debtorIban": "***************************",
				"debtorBic": "SOGEFRPPXXX",
				"debitorName": "PIERRE XXXXX",
				"debitorAddressLine": "99 RUE XXXXXXX PARIS                       75003",
				"debitorCountry": "FR",
				"creditorIban": "***************************",
				"creditorBIC": "TRZOFR21XXX",
				"creditorName": "Elie Zedeck RANDRIAMIANDRIRAY",
				"creditorAddressLine": "99 RUE XXXXXXX PARIS                       75003",
				"creditorCountry": "FR",
				"remittanceInformation": "",
				"return_reason_code": "AG01",
				"virtualIbanId": "",
				"virtualIbanReference": ""
			}
		]
	}`

	// Test the conversion of data
	list, err := UnwrapSepaSctrInstEntriesFromTreezorResponse([]byte(data))
	if err != nil {
		t.Fatal(err)
	}
	if len(list) != 1 {
		t.Fatal("Expected 1 entry")
	}
	if list[0].WalletID != 2145901 {
		t.Fatal("Wrong walletId")
	}
	if list[0].SettlementAmount != 15.00 {
		t.Fatal("Wrong settlementAmount")
	}
	if list[0].ReturnReasonCode != "AG01" {
		t.Fatal("Wrong returnReasonCode")
	}
}
