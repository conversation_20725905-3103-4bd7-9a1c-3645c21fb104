package treezor

import "testing"

func TestUnmarshallingFromUserCreation(t *testing.T) {
	rawPayload := `{"users":[{"userId":3354853,"userTypeId":1,"userStatus":"VALIDATED","clientId":"39625","userTag":"62c5a86a110715456efe5587","parentUserId":null,"parentType":null,"title":"","firstname":"<PERSON><PERSON>","lastname":"RANDRIAMIANDRIRAY","middleNames":"","birthday":"1986-05-24","email":"<EMAIL>","address1":"Lot II G 20 R Bis Ambatomaro","address2":"","address3":null,"postcode":"101","city":"Antananarivo","state":"","country":"MG","countryName":"Madagascar","phone":"+261341023460","mobile":"+261341023460","nationality":"","nationalityOther":"","placeOfBirth":"","birthCountry":"","occupation":"","incomeRange":"","legalName":"","legalNameEmbossed":"","legalRegistrationNumber":"","legalTvaNumber":"","legalRegistrationDate":"0000-00-00","legalForm":"","legalShareCapital":"0","legalSector":"","legalAnnualTurnOver":"","legalNetIncomeRange":"","legalNumberOfEmployeeRange":"","effectiveBeneficiary":"0.00","position":"","personalAssets":"","taxResidence":"","taxNumber":"","kycLevel":0,"kycReview":0,"kycReviewComment":"","isFreezed":"0","language":null,"specifiedUSPerson":0,"employeeType":"0","entityType":"0","controllingPersonType":"0","activityOutsideEu":null,"economicSanctions":null,"residentCountriesSanctions":null,"involvedSanctions":null,"sanctionsQuestionnaireDate":null,"timezone":null,"createdDate":"2022-07-06 15:33:32","modifiedDate":"0000-00-00 00:00:00","codeStatus":"110009","informationStatus":"","sepaCreditorIdentifier":null,"walletCount":"0","payinCount":"0"}]}`
	users, err := UnwrapUsersFromTreezorResponse([]byte(rawPayload))
	if err != nil {
		t.Error(err)
	}
	t.Log(users[0])
}

func TestUnmarshallingFromUserKycReviewWebhookEvent(t *testing.T) {
	rawPayload := `{"users":[{"userId":"3354853","userTypeId":"1","userStatus":"VALIDATED","clientId":"39625","userTag":"62c5a86a110715456efe5587","parentUserId":null,"parentType":null,"title":"","firstname":"Elie Zedeck","lastname":"RANDRIAMIANDRIRAY","middleNames":"","birthday":"1986-05-24","email":"<EMAIL>","address1":"Lot II G 20 R Bis Ambatomaro","address2":"","address3":null,"postcode":"101","city":"Antananarivo","state":"","country":"MG","countryName":"Madagascar","phone":"+261341023460","mobile":"+261341023460","nationality":"","nationalityOther":"","placeOfBirth":"","birthCountry":"","occupation":"","incomeRange":"","legalName":"","legalNameEmbossed":"","legalRegistrationNumber":"","legalTvaNumber":"","legalRegistrationDate":"0000-00-00","legalForm":"","legalShareCapital":"0","legalSector":"","legalAnnualTurnOver":"","legalNetIncomeRange":"","legalNumberOfEmployeeRange":"","effectiveBeneficiary":"0.00","position":"","personalAssets":"","taxResidence":"","taxNumber":"","kycLevel":"0","kycReview":"0","kycReviewComment":"","isFreezed":"0","language":null,"specifiedUSPerson":"0","employeeType":"0","entityType":"0","controllingPersonType":"0","activityOutsideEu":null,"economicSanctions":null,"residentCountriesSanctions":null,"involvedSanctions":null,"sanctionsQuestionnaireDate":null,"timezone":null,"createdDate":"2022-07-06 15:33:32","modifiedDate":"0000-00-00 00:00:00","codeStatus":"110009","informationStatus":"","sepaCreditorIdentifier":null,"walletCount":"0","payinCount":"0"}]}`
	users, err := UnwrapUsersFromTreezorResponse([]byte(rawPayload))
	if err != nil {
		t.Error(err)
	}
	t.Log(users[0])
}
