package treezor

import (
	"encoding/json"
	"time"
)

type CardTxAuth struct {
	ID                     string    `bson:"_id" json:"authenticationRequestId"`
	UserID                 int64     `bson:"userId" json:"userId"`
	PublicToken            string    `bson:"publicToken" json:"publicToken"`
	CardID                 int64     `bson:"cardId" json:"cardId"`
	MaskedPan              string    `bson:"maskedPan" json:"maskedPan"`
	ThreeDSVersion         string    `bson:"3DSVersion" json:"3DSVersion"`
	AcquirerID             int64     `bson:"acquirerId" json:"acquirerId"`
	MerchantID             string    `bson:"merchantId" json:"merchantId"`
	MerchantName           string    `bson:"merchantName" json:"merchantName"`
	MerchantURL            string    `bson:"merchantURL" json:"merchantURL"`
	MerchantCategoryCode   string    `bson:"merchantCategoryCode" json:"merchantCategoryCode"`
	MerchantCountryCode    string    `bson:"merchantCountryCode" json:"merchantCountryCode"`
	MerchantAppRedirectURL string    `bson:"merchantAppRedirectURL" json:"merchantAppRedirectURL"`
	PaymentDate            time.Time `bson:"paymentDate" json:"paymentDate"`
	PaymentAmount          int64     `bson:"paymentAmount" json:"paymentAmount"` // Amount in cents
	PaymentCurrency        int       `bson:"paymentCurrency" json:"paymentCurrency"`

	Updates []CardTxAuthUpdate `bson:"updates" json:"updates"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

type CardTxAuthUpdate struct {
	ID                             string `bson:"_id" json:"authenticationRequestId"`
	PublicToken                    string `bson:"publicToken" json:"publicToken"`
	AuthenticationFinalResult      string `bson:"authenticationFinalResult" json:"authenticationFinalResult"`
	AuthenticationErrorRef         string `bson:"authenticationErrorRef" json:"authenticationErrorRef"`
	AuthenticationErrorDescription string `bson:"authenticationErrorDescription" json:"authenticationErrorDescription"`

	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

// UnwrapCardTxAuthFromTreezorResponse unwraps the response from Treezor for card3DSv2Authentication.create events
//
// This is how the response should look like (according to the doc, as of 2024-07-01). Usually, the `object_payload`
// part is an object that contains a nested array, but here, it's a single object.
//
//	{
//		"authenticationRequestId": "cd679525-c341-4018-92f5-594b06fec05f", // Authentication request UUID, should be returned in the URL of the auth-requests (previously authenticationRequestID)
//		"userId": "123456", 						// Unique identifier of the user
//		"publicToken": "123456789", 				// Public token of the card (previously PublicToken)
//		"cardId": "123456", 						// Unique identifier of the card
//		"maskedPan": "546923******1234",			// Card number partially masked
//		"3DSVersion": "2.2.0", 						// 3DS Version used for the message (1.x or 2.x)
//		"acquirerId": "513264", 					// Identifier of the merchant's acquirer (previously AcquirerID)
//		"merchantId": "05008620641413",				// Identifier of the merchant (previously MerchantID)
//		"merchantName": "Merchant 89111532", 		// Name of the merchant (previously MerchantName)
//		"merchantURL": "https://example.com", 		// Merchant URL (previously MerchantURL)
//		"merchantCategoryCode": "0000", 			// MCC of the merchant (previously MerchantCategoryCode)
//		"merchantCountryCode": "440", 				// Country code of the merchant (previously MerchantCountryCode)
//		"merchantAppRedirectURL": "",				// Merchant app redirect URL (previously MerchantAppRedirectURL)
//		"paymentDate": "2024-03-25T14:26:36.482Z", 	// Previously transactionTimeStamp, date and time of the authentication
//		"paymentAmount": "1822", 					// Previously transactionAmount, amount in cents of the transaction
//		"paymentCurrency": "978" 					// Previously transactionCurrency, currency of the transaction
//	}
func UnwrapCardTxAuthFromTreezorResponse(response []byte) (*CardTxAuth, error) {
	// Convert all int fields that are strings to numeric
	response, err := PatchBadStringFieldsThatShouldBeNumbersInSingleObject(response,
		[]string{"userId", "userID", "cardId", "AcquirerID", "acquirerId", "paymentAmount", "paymentCurrency"})
	if err != nil {
		return nil, err
	}

	var payload CardTxAuth
	if err = json.Unmarshal(response, &payload); err != nil {
		return nil, err
	}
	return &payload, nil
}

// UnwrapCardTxAuthUpdateFromTreezorResponse unwraps the response from Treezor for card3DSv2Authentication.update events
//
// Like above, the `object_payload` is a single object, not an array.
//
//	{
//		"publicToken": "112726918",							// Public token of the card
//		"authenticationRequestId": "cd679525-c341-4018-92f5-594b0xxxc05f", // Authentication request UUID (previously authenticationRequestID)
//		"authenticationFinalResult": "SUCCESS",  			// Final result which can be: SUCCESS|TIMEOUT|ERROR|UNAUTHENTICATED|FALLBACK
//		"authenticationErrorRef": null,						// Error reference given by the processor
//		"authenticationErrorDescription": ""				// Error description given by the processor
//	}
func UnwrapCardTxAuthUpdateFromTreezorResponse(response []byte) (*CardTxAuthUpdate, error) {
	var payload CardTxAuthUpdate
	if err := json.Unmarshal(response, &payload); err != nil {
		return nil, err
	}
	return &payload, nil
}
