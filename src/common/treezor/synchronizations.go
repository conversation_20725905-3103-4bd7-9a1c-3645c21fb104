package treezor

import (
	"yochbee/_base/dbnosql"

	"go.uber.org/zap"
)

type DBOperation int

const (
	ActualUpdateDBOpNoop   DBOperation = 0
	ActualUpdateDBOpInsert DBOperation = 1
	ActualUpdateDBOpUpdate DBOperation = 2
)

type ActualUpdater interface {
	// GetTreezorID returns the database field name used to identify the entity on Treezor side and ours. val is the
	// value of the field when queried locally.
	GetTreezorID() (dbField string, val interface{})
	TreezorTimestamp() int64
	SetTreezorTimestamp(ts int64)

	// SetInsertID will be used to give a unique ID to the entry, and the `CreatedAt` field will also be updated.
	SetInsertID(id string)
	UpdateActualFromIncoming(incoming ActualUpdater)
}

// SyncActualEntity synchronizes or inserts a new entity as an actual entity, that is: not a snapshot.
func SyncActualEntity(L *zap.Logger, coll dbnosql.DataCollection, existingId any, queryField string, incoming ActualUpdater) (DBOperation, error) {
	L = L.Named("SyncActualEntity")

	existing, err := dbnosql.GetOneByField[ActualUpdater](coll, queryField, existingId)
	if err != nil {
		L.Error("Failed getting existing entity", zap.Error(err))
		return ActualUpdateDBOpNoop, err
	}

	L.Info("Incoming entity", zap.Any("incoming", incoming))
	if existing != nil {
		L.Info("Existing entity found", zap.Any("existing", existing))

		if existing.TreezorTimestamp() != 0 && incoming.TreezorTimestamp() != 0 {
			if existing.TreezorTimestamp() >= incoming.TreezorTimestamp() {
				L.Info("Incoming entity is older than existing entity, skipping update", zap.Any("incoming", incoming), zap.Any("existing", existing))
				return ActualUpdateDBOpNoop, nil
			}
		} else if incoming.TreezorTimestamp() == 0 {
			L.Warn("Incoming entity has no Treezor timestamp, using existing one", zap.Any("incoming", incoming), zap.Any("existing", existing))
			incoming.SetTreezorTimestamp(existing.TreezorTimestamp())
		}

		existing.UpdateActualFromIncoming(incoming)

		L.Info("Updating existing entity", zap.Any("incoming", incoming), zap.Any("existing", existing))
		field, val := existing.GetTreezorID()
		return ActualUpdateDBOpUpdate, coll.UpdateOneByField(field, val, existing)
	} else {
		incoming.SetInsertID(coll.GenerateUniqueId()) // should also set the .CreatedAt field to now

		L.Info("Inserting new entity", zap.Any("incoming", incoming))
		_, err = coll.Insert(incoming)
		return ActualUpdateDBOpInsert, err
	}
}
