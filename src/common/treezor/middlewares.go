package treezor

import (
	"errors"
	"net/http"
	"strconv"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	web2 "yochbee/_base/web"
	"yochbee/common"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// MiddlewareRequireAndCacheSCAProof checks the `X-Yochbee-SCAProof-JWS` header and calls the Treezor RPC method `Method_CacheSCAProof` to store the JWS for near future usage in the treezor microservice.
func MiddlewareRequireAndCacheSCAProof(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		L := logging.L.Named("[treezor]MiddlewareRequireAndCacheSCAProof")
		if err := RequireAndCacheSCAProof(c, L); err != nil {
			return err
		}
		return next(c)
	}
}

// MiddlewareConditionalSCAProofForDates checks if any date in the request is more than 90 days old,
// and if so, requires SCA proof. Otherwise, caches SCA proof optionally.
// Supports both query parameters (startDate/endDate) and path parameters (year/month).
func MiddlewareConditionalSCAProofForDates(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		L := logging.L.Named("[treezor]MiddlewareConditionalSCAProofForDates")

		var dateFrom, dateTo time.Time
		var requiresSCA bool

		// Check if this is a year/month path parameter endpoint
		yearStr := strings.TrimSpace(c.Param("year"))
		monthStr := strings.TrimSpace(c.Param("month"))

		if yearStr != "" && monthStr != "" {
			// Handle year/month path parameters (monthly statements)
			year, parseErr := strconv.Atoi(yearStr)
			if parseErr != nil {
				L.Error("Error parsing year", zap.Error(parseErr))
				return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid year")
			}
			month, parseErr := strconv.Atoi(monthStr)
			if parseErr != nil {
				L.Error("Error parsing month", zap.Error(parseErr))
				return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid month")
			}

			// Get the start and end dates for the month
			dateFrom = time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
			dateTo = dateFrom.AddDate(0, 1, 0).Add(-time.Second)
		} else {
			// Handle startDate/endDate query parameters (transaction listings)
			startDateStr := strings.TrimSpace(c.QueryParam("startDate"))
			endDateStr := strings.TrimSpace(c.QueryParam("endDate"))

			// Parse startDate
			if startDateStr != "" {
				startDateInt, parseErr := strconv.ParseInt(startDateStr, 10, 64)
				if parseErr != nil {
					L.Error("Error parsing startDate", zap.Error(parseErr))
					return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid startDate")
				}
				dateFrom = time.Unix(startDateInt, 0)
			} else {
				dateFrom = time.Now().Add(-7 * 24 * time.Hour) // default 7 days back
			}

			// Parse endDate
			if endDateStr != "" {
				endDateInt, parseErr := strconv.ParseInt(endDateStr, 10, 64)
				if parseErr != nil {
					L.Error("Error parsing endDate", zap.Error(parseErr))
					return web2.ErrorWithStatusAndCode(c, http.StatusBadRequest, 400, "Invalid endDate")
				}
				dateTo = time.Unix(endDateInt, 0)
			} else {
				dateTo = time.Now().Add(5 * time.Minute) // default 5 minutes future
			}
		}

		// Check if any date is more than 90 days old
		now := time.Now()
		ninetyDaysAgo := now.Add(-90 * 24 * time.Hour)
		requiresSCA = dateFrom.Before(ninetyDaysAgo) || dateTo.Before(ninetyDaysAgo)

		L.Info("Date range SCA requirement", zap.Bool("requiresSCA", requiresSCA),
			zap.Time("dateFrom", dateFrom), zap.Time("dateTo", dateTo), zap.Time("ninetyDaysAgo", ninetyDaysAgo))

		if requiresSCA {
			// Require SCA proof for dates > 90 days old
			if err := RequireAndCacheSCAProof(c, L); err != nil {
				return err
			}
		} else {
			// Optional SCA proof for recent dates
			scaProofJWS := strings.TrimSpace(c.Request().Header.Get("X-Yochbee-SCAProof-JWS"))
			if scaProofJWS != "" {
				busrpc, ok := c.Get("busrpc").(bus2.RpcClient)
				if !ok {
					L.Error("BUG? busrpc not found in echo context")
					return errors.New("busrpc not found in echo context")
				}

				treezorUserId, ok := c.Get("treezorUserId").(int64)
				if !ok {
					L.Error("treezorUserId not found in echo context, account may not be associated with Treezor")
					return web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeTreezorUserNotFound, "User is not associated with Payment provider")
				}

				r, err := CacheSCAProof(busrpc, treezorUserId, scaProofJWS)
				if err != nil {
					L.Error("Error caching the SCA proof", zap.Error(err))
					return err
				}

				accountId := c.Get("accountId").(string)
				L.Info("SCAProof JWS cached successfully 🟢", zap.String("accountId", accountId), zap.String("sessionKey", r.SessionKey),
					zap.String("operationKey", r.OperationKey), zap.String("operationUrl", r.OperationUrl))
			}
		}

		return next(c)
	}
}

// RequireAndCacheSCAProof checks the `X-Yochbee-SCAProof-JWS` header and calls the Treezor RPC method `Method_CacheSCAProof` to store the JWS for near future usage in the treezor microservice.
func RequireAndCacheSCAProof(c echo.Context, L *zap.Logger) error {
	scaProofJWS := strings.TrimSpace(c.Request().Header.Get("X-Yochbee-SCAProof-JWS"))
	if scaProofJWS == "" {
		web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeSCAProofRequired, "SCA proof is required")
		return errors.New("SCA proof is required")
	}

	busrpc, ok := c.Get("busrpc").(bus2.RpcClient)
	if !ok {
		// Potentially a bug
		L.Error("BUG? busrpc not found in echo context")
		return errors.New("busrpc not found in echo context")
	}

	// Retrieve the account TreezorUserId
	treezorUserId, ok := c.Get("treezorUserId").(int64)
	if !ok {
		L.Error("treezorUserId not found in echo context, account may not be associated with Treezor")
		web2.ErrorWithStatusAndCode(c, http.StatusUnauthorized, common.ErrorCodeTreezorUserNotFound, "User is not associated with Payment provider")
		return errors.New("User is not associated with Payment provider")
	}

	// Call the Treezor RPC method `Method_CacheSCAProof` to store the JWS
	r, err := CacheSCAProof(busrpc, treezorUserId, scaProofJWS)
	if err != nil {
		L.Error("Error caching the SCA proof", zap.Error(err))
		return err // HTTP 500
	}

	// JWS cached successfully
	accountId := c.Get("accountId").(string)
	L.Info("SCAProof JWS cached successfully 🟢", zap.String("accountId", accountId), zap.String("sessionKey", r.SessionKey),
		zap.String("operationKey", r.OperationKey), zap.String("operationUrl", r.OperationUrl))

	return nil
}
