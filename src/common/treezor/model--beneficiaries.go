package treezor

import (
	"encoding/json"
	"time"
)

type Beneficiary struct {
	ID int64 `bson:"_id" json:"id"`

	TreezorUserId int64 `bson:"treezorUserId" json:"userId"`

	NickName string `bson:"nickName" json:"nickName"`
	Name     string `bson:"name"     json:"name"     validate:"required"`
	Address  string `bson:"address"  json:"address"  validate:"required"`
	IBAN     string `bson:"iban"     json:"iban"     validate:"required"`
	BIC      string `bson:"bic"      json:"bic"      validate:"required"`

	IsActive       bool `bson:"isActive"       json:"isActive"`
	IsUsableForSCT bool `bson:"isUsableForSCT" json:"usableForSct"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`
}

func UnwrapBeneficiariesFromTreezorResponse(msg []byte) ([]*Beneficiary, error) {
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "beneficiaries", []string{"userId"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Beneficiaries []*Beneficiary `json:"beneficiaries"`
	}{}
	if err := json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Beneficiaries, nil
}

type BeneficiaryDTO struct {
	UserId int64 `json:"userId"`

	NickName string `json:"nickName"`
	Name     string `json:"name"     validate:"required"`
	Address  string `json:"address"  validate:"required"`
	IBAN     string `json:"iban"     validate:"required"`
	BIC      string `json:"bic"      validate:"required"`

	// IsActive       bool `json:"isActive"` // not accepted
	IsUsableForSCT bool `json:"usableForSct"`
}

type BeneficiaryUpdateDTO struct {
	NickName string `json:"nickName"`
	Name     string `json:"name"     validate:"required"`
	Address  string `json:"address"  validate:"required"`
	IBAN     string `json:"iban"     validate:"required"`
	BIC      string `json:"bic"      validate:"required"`

	IsActive       bool `json:"isActive"`
	IsUsableForSCT bool `json:"usableForSct"`
}

type BeneficiaryRaw struct {
	ID                                 int64    `json:"id"`
	Tag                                string   `json:"tag"`
	UserId                             int64    `json:"userId"`
	NickName                           string   `json:"nickName"`
	Name                               string   `json:"name"`
	Address                            string   `json:"address"`
	IBAN                               string   `json:"iban"`
	BIC                                string   `json:"bic"`
	SepaCreditorIdentifier             string   `json:"sepaCreditorIdentifier"`
	SDDB2BWhitelist                    []string `json:"sddB2bWhitelist"`
	SDDCoreBlacklist                   []string `json:"sddCoreBlacklist"`
	UsableForSCT                       bool     `json:"usableForSct"`
	SDDCoreKnownUniqueMandateReference []string `json:"sddCoreKnownUniqueMandateReference"`
	IsActive                           bool     `json:"isActive"`
	CreatedDate                        string   `json:"createdDate"`
	ModifiedDate                       string   `json:"modifiedDate"`
}

// UnwrapBeneficiaryRawFromTreezorResponse unwraps the response from Treezor for beneficiaries.create and beneficiaries.update events.
// The payload looks like this:
//
//	{
//	  "beneficiaries": [
//	    {
//	      "id": 3904673,
//	      "tag": "",
//	      "userId": 9875442,
//	      "nickName": "Zoubeir",
//	      "name": "Ben Terdeyet",
//	      "address": "",
//	      "iban": "2338415345 05D5A405743 5550 7545346",
//	      "bic": "CMCIFR2A",
//	      "sepaCreditorIdentifier": "",
//	      "sddB2bWhitelist": [],
//	      "sddCoreBlacklist": [],
//	      "usableForSct": true,
//	      "sddCoreKnownUniqueMandateReference": [],
//	      "isActive": true,
//	      "createdDate": "2024-06-08 10:32:47",
//	      "modifiedDate": "2024-06-08 10:32:47"
//	    }
//	  ]
//	}
func UnwrapBeneficiaryRawFromTreezorResponse(response []byte) ([]*BeneficiaryRaw, error) {
	var payload struct {
		Beneficiaries []*BeneficiaryRaw `json:"beneficiaries"`
	}
	if err := json.Unmarshal(response, &payload); err != nil {
		return nil, err
	}
	return payload.Beneficiaries, nil
}
