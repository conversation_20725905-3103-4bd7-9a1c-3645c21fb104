package treezor

import (
	"encoding/json"
	"github.com/tidwall/gjson"
)

// Operation is not a saved model, it is used to parse the response from the API
type Operation struct {
	OperationType string `json:"operationType"`
	Amount        struct {
		Amount   float64 `json:"amount"`
		Currency string  `json:"currency"`
	} `json:"amount"`
	WalletId          int64       `json:"walletId"`
	Direction         string      `json:"direction"`
	ObjectId          int64       `json:"objectId"`
	Label             string      `json:"label"`
	ExternalReference interface{} `json:"externalReference"`
	Metadata          struct {
		CardPayment struct {
			Mcc struct {
				Code int `json:"code"`
			} `json:"mcc"`
			Mid struct {
				Value string `json:"value"`
			} `json:"mid"`
			LocalAmount struct {
				Amount   float64 `json:"amount"`
				Currency string  `json:"currency"`
			} `json:"localAmount"`
			AuthorizationNote         string `json:"authorizationNote"`
			AuthorisationResponseCode struct {
				Action      string `json:"action"`
				Description string `json:"description"`
				Value       int    `json:"value"`
			} `json:"authorisationResponseCode"`
		} `json:"cardPayment,omitempty"`
	} `json:"metadata,omitempty"`
	Status string `json:"status"`
	Date   struct {
		Creation   string `json:"creation"`
		Settlement string `json:"settlement,omitempty"`
	} `json:"date"`
}

// UnwrapOperationsFromTreezorResponse is a helper function to parse the response from the Treezor API.
//
// The response is expected to be a JSON object with a key that contains a list of operations. Normally, the key is
// "data", and not "operations", but you can pass any key you want.
func UnwrapOperationsFromTreezorResponse(msg []byte, listKey string) ([]*Operation, error) {
	// Convert all int fields that could be strings to numeric
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, listKey, []string{"walletId", "objectId"})
	if err != nil {
		return nil, err
	}

	// Pick only the list section of the response
	list := gjson.GetBytes(msg, listKey).Raw

	operations := make([]*Operation, 0, 10)
	if err = json.Unmarshal([]byte(list), &operations); err != nil {
		return nil, err
	}
	return operations, nil
}
