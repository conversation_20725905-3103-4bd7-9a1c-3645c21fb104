package treezor

import (
	"fmt"
	"strings"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/common/accounts"
	"yochbee/common/banking"
	"yochbee/common/kyc"
)

// =============

const Method_IngestFreshEntity = "treezor.IngestFreshEntity"

type IngestFreshEntityRequest struct {
	Event      *WebhookEvent `json:"event"`
	EntityType string        `json:"entityType"` // such as user, wallet, payin, payout, etc.
}

type IngestFreshEntityResponse struct {
}

// =============

const (
	Method_CreateUser = "treezor.CreateUser"
	Method_UpdateUser = "treezor.UpdateUser"
)

type CreateUserResponse struct {
	Id            string `json:"id"`
	TreezorUserId int64  `json:"userId"`
}

// =============

const Method_GetTreezorUser = "treezor.GetTreezorUser"

type GetTreezorUserRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
}

type GetTreezorUserResponse struct {
	User *User `json:"user"`
}

// =============

const Method_CancelUser = "treezor.CancelUser"

type CancelUserRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
}

type CancelUserResponse struct {
	Ok bool `json:"ok"`
}

// =============

// Method_ShiftUserInfo is used change the email + phone number of an existing Treezor user. This is useful in order to
// register another user that uses the same email + phone number, typically during testing.
const Method_ShiftUserInfo = "treezor.ShiftUserInfo"

type ShiftUserInfoRequest struct {
	TreezorUserId int64  `json:"treezorUserId"`
	PhoneNumber   string `json:"phoneNumber"`
	DoCancel      bool   `json:"doCancel"`
}

type ShiftUserInfoResponse struct {
	ShiftedEmail       string `json:"shiftedEmail"`
	ShiftedPhoneNumber string `json:"shiftedPhoneNumber"`
}

// =============

const Method_CreateWallet = "treezor.CreateWallet"

type CreateWalletResponse struct {
	WalletId int64 `json:"walletId"`
}

// =============

const Method_TriggerKYCLivenessDocumentsSendingTreezor = "treezor.TriggerKYCLivenessDocumentsSendingTreezor"

type TriggerKYCLivenessDocumentsSendingTreezorRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
}

type TriggerKYCLivenessDocumentsSendingTreezorResponse struct {
	Ok bool `json:"ok"`
}

// =============

const Method_UploadDocument = "treezor.UploadDocument"

type UploadDocumentRequest struct {
	ResidenceId   int64         `json:"residenceId,omitempty"`
	Document      *kyc.Document `json:"document"`
	TreezorUserId int64         `json:"treezorUserId"`
}

type UploadDocumentResponse struct {
	TreezorDocument *Document `json:"treezorDocument"`
}

// =============

const Method_StartKYCReview = "treezor.StartKYCReview"

type StartKYCReviewRequest struct {
	Progress *kyc.Progress `json:"progress"`
}

// =============

const Method_UpdateDeclarativeInformation = "treezor.UpdateDeclarativeInformation"

type UpdateDeclarativeInformationRequest struct {
	UserId int64                     `json:"userId"`
	Info   DeclarativeInformationDTO `json:"info"`
}

type UpdateDeclarativeInformationResponse struct {
	TreezorUser *User `json:"treezorUser"`
}

// =============

const Method_StartKYCLiveness = "treezor.StartKYCLiveness"

type StartKYCLivenessRequest struct {
	Account *accounts.Account `json:"account"`
}

type StartKYCLivenessResponse struct {
	LivenessLink *LivenessLink `json:"liveness"`
}

// =============

const Method_SyncBeneficiaryToTreezor = "treezor.SyncBeneficiaryToTreezor"

type SyncBeneficiaryToTreezorRequest struct {
	Beneficiary *banking.Beneficiary `json:"beneficiary"`
}

type SyncBeneficiaryToTreezorResponse struct {
	TreezorBeneficiary *Beneficiary `json:"treezorBeneficiary"`
}

// =============

const Method_DeleteBeneficiaryFromTreezor = "treezor.DeleteBeneficiaryFromTreezor"

type DeleteBeneficiaryFromTreezorRequest struct {
	Beneficiary *banking.Beneficiary `json:"beneficiary"`
}

type DeleteBeneficiaryFromTreezorResponse struct {
	Ok bool `json:"ok"`
}

// =============

const Method_GetWalletByTreezorId = "treezor.GetWalletByTreezorId"

type GetWalletByTreezorIdRequest struct {
	TreezorWalletId int64 `json:"treezorWalletId"`
}

type GetWalletByTreezorIdResponse struct {
	TreezorWallet *Wallet `json:"wallet"`
}

func GetWalletByTreezorId(busrpc bus2.RpcClient, treezorWalletId int64) (*Wallet, error) {
	result, err := bus2.CallMethodMU[GetWalletByTreezorIdResponse](busrpc, Method_GetWalletByTreezorId, &GetWalletByTreezorIdRequest{
		TreezorWalletId: treezorWalletId,
	}, 10*time.Second)
	if err != nil {
		return nil, err
	}
	if result == nil || result.TreezorWallet == nil {
		return nil, fmt.Errorf("wallet not found for ID %d", treezorWalletId)
	}
	return result.TreezorWallet, nil
}

// =============

const Method_GetWalletsByTreezorIds = "treezor.GetWalletsByTreezorIds"

type GetWalletsByTreezorIdsRequest struct {
	TreezorWalletIds []int64 `json:"treezorWalletIds"`
}

type GetWalletsByTreezorIdsResponse struct {
	TreezorWallets []*Wallet `json:"wallets"`
}

// =============

const Method_GetWalletByUserId = "treezor.GetWalletByUserId"

type GetWalletByUserIdRequest struct {
	TreezorUserId int64      `json:"treezorUserId"`
	WalletType    WalletType `json:"walletType"`
}

type GetWalletByUserIdResponse struct {
	TreezorWallet *Wallet `json:"wallet"`
}

// =============

const Method_PayoutWallet2Beneficiary = "treezor.PayoutWallet2Beneficiary"

type PayoutWallet2BeneficiaryRequest struct {
	Account       *accounts.Account    `json:"account"`
	Amount        float64              `json:"amount"`
	Beneficiary   *banking.Beneficiary `json:"beneficiary"`
	Label         string               `json:"label"`
	TreezorUserId int64                `json:"treezorUserId"`
}

type PayoutWallet2BeneficiaryResponse struct {
	TreezorPayout *Payout `json:"treezorPayout"`
}

// =============

const Method_RetriggerWebhookEvent = "treezor.RetriggerWebhookEvent"

type RetriggerWebhookEventRequest struct {
	WebhookEventID string `json:"webhookEventId"`
}

type RetriggerWebhookEventResponse struct {
}

// =============

const Method_RefreshWallet = "treezor.RefreshWallet"

type RefreshWalletRequest struct {
	TreezorWalletId int64 `json:"treezorWalletId" validate:"required"`
}

type RefreshWalletResponse struct {
	TreezorWallet *Wallet `json:"treezorWallet"`
}

// =============

const Method_OrderCard = "treezor.OrderCard"

type CardLimit struct {
	Withdrawals float64 `json:"atm"`
	Payments    float64 `json:"payments"`
}

type OrderCardRequest struct {
	IsPhysical bool `json:"isPhysical"`

	Account *accounts.Account `json:"account"`

	PinCode       string     `json:"pin"`
	Language      string     `json:"language"`
	OptionForeign int        `json:"foreign"`
	OptionOnline  int        `json:"online"`
	OptionAtm     int        `json:"atm"`
	OptionNfc     int        `json:"nfc"`
	DailyLimits   *CardLimit `json:"dailyLimits"`
	WeeklyLimits  *CardLimit `json:"weeklyLimits"`
	MonthlyLimits *CardLimit `json:"monthlyLimits"`
	YearlyLimits  *CardLimit `json:"yearlyLimits"`
	GlobalLimits  *CardLimit `json:"globalLimits"`
}

type OrderCardResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_ActivateCard = "treezor.ActivateCard"

type ActivateCardRequest struct {
	TreezorCardId int64             `json:"cardId"`
	Account       *accounts.Account `json:"account"`
}

type ActivateCardResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_RegisterCard3DS = "treezor.RegisterCard3DS"

type RegisterCard3DSRequest struct {
	TreezorCardId int64             `json:"cardId"`
	Account       *accounts.Account `json:"account"`
}

type RegisterCard3DSResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_GetFreshCard = "treezor.GetFreshCard"

type GetFreshCardRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
	TreezorCardId int64 `json:"cardId"`
}

type GetFreshCardResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_EnrollCard = "treezor.EnrollCard"

type EnrollCardRequest struct {
	TreezorCardId int64             `json:"cardId"`
	Account       *accounts.Account `json:"account"`
}

type EnrollCardResponse struct {
	Ok bool `json:"ok"`
}

// =============

const Method_UpdateKnownCardPIN = "treezor.UpdateKnownCardPIN"

type UpdateKnownCardPINRequest struct {
	TreezorCardId int64             `json:"cardId"`
	CurrentPIN    string            `json:"currentPIN"`
	NewPIN        string            `json:"newPIN"`
	Account       *accounts.Account `json:"account"`
}

type UpdateKnownCardPINResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_UpdateCardBlockingStatus = "treezor.UpdateCardBlockingStatus"

type UpdateCardBlockingStatusRequest struct {
	TreezorCardId int64             `json:"cardId"`
	LockStatus    int               `json:"status"` // 0: unblock, 1: block, 2: lost, 3: stolen, 4: destroyed
	Account       *accounts.Account `json:"account"`
}

type UpdateCardBlockingStatusResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_UpdateCardLimits = "treezor.UpdateCardLimits"

type UpdateCardLimitsRequest struct {
	TreezorCardId int64             `json:"cardId"`
	DailyLimits   *CardLimit        `json:"dailyLimits"`
	WeeklyLimits  *CardLimit        `json:"weeklyLimits"`
	MonthlyLimits *CardLimit        `json:"monthlyLimits"`
	YearlyLimits  *CardLimit        `json:"yearlyLimits"`
	GlobalLimits  *CardLimit        `json:"globalLimits"`
	Account       *accounts.Account `json:"account"`
}

type UpdateCardLimitsResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_UpdateCardFeatures = "treezor.UpdateCardFeatures"

type UpdateCardFeaturesRequest struct {
	TreezorCardId int64             `json:"cardId"`
	OptionForeign int               `json:"foreign"`
	OptionOnline  int               `json:"online"`
	OptionAtm     int               `json:"atm"`
	OptionNfc     int               `json:"nfc"`
	Account       *accounts.Account `json:"account"`
}

type UpdateCardFeaturesResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_ConvertVirtualCardToPhysical = "treezor.ConvertVirtualCardToPhysical"

type ConvertVirtualCardToPhysicalRequest struct {
	TreezorCardId int64 `json:"cardId"`
	TreezorUserId int64 `json:"treezorUserId"`
}

type ConvertVirtualCardToPhysicalResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_GetHiPayPublicAuthorization = "treezor.GetHiPayPublicAuthorization"

type GetHiPayPublicAuthorizationRequest struct {
}

type GetHiPayPublicAuthorizationResponse struct {
	PublicAuthorization string `json:"publicAuthorization"`
}

// =============

const Method_GetPayinByTreezorID = "treezor.GetPayinByTreezorID"

type GetPayinByTreezorIDRequest struct {
	TreezorID interface{} `json:"treezorID"` // int64 or string
}

type GetPayinByTreezorIDResponse struct {
	Payin *Payin `json:"payin"`
}

// =============

const Method_GetMultiplePayinsByTreezorIds = "treezor.GetMultiplePayinsByTreezorIDs"

type GetMultiplePayinsByTreezorIdsRequest struct {
	TreezorIDs []interface{} `json:"treezorIDs"` // []int64 or []string
}

type GetMultiplePayinsByTreezorIdsResponse struct {
	Payins map[string]*Payin `json:"payins"`
}

func GetMultiplePayinsByTreezorIds(busrpc bus2.RpcClient, treezorIDs []interface{}) (*GetMultiplePayinsByTreezorIdsResponse, error) {
	request := GetMultiplePayinsByTreezorIdsRequest{
		TreezorIDs: treezorIDs,
	}
	response, err := bus2.CallMethodMU[GetMultiplePayinsByTreezorIdsResponse](busrpc, Method_GetMultiplePayinsByTreezorIds, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetPayoutByTreezorID = "treezor.GetPayoutByTreezorID"

type GetPayoutByTreezorIDRequest struct {
	TreezorID interface{} `json:"treezorID"` // int64 or string
}

type GetPayoutByTreezorIDResponse struct {
	Payout *Payout `json:"payout"`
}

// =============

const Method_GetCardTransactionByTreezorID = "treezor.GetCardTransactionByTreezorID"

type GetCardTransactionByTreezorIDRequest struct {
	TreezorID int64 `json:"treezorID"`
}

type GetCardTransactionByTreezorIDResponse struct {
	CardTransaction *CardTransaction `json:"cardTransaction"`
}

// =============

// Method_UnblockPIN unblocks a card PIN after 3 failed attempts, it resets the `pinTryExceeds` field to 0.
const Method_UnblockPIN = "treezor.UnblockPIN"

type UnblockPINRequest struct {
	TreezorCardId int64 `json:"cardId"`
	TreezorUserId int64 `json:"treezorUserId"`
}

type UnblockPINResponse struct {
	Card *Card `json:"card"`
}

// =============

const Method_GetCardImage = "treezor.GetCardImage"

type GetCardImageRequest struct {
	TreezorCardId int64 `json:"cardId"`
	TreezorUserId int64 `json:"treezorUserId"`
}

type GetCardImageResponse struct {
	Image []byte `json:"image"`
	Mime  string
}

// =============

const Method_SendSCTRToUser = "treezor.SendSCTRToUser"

type SendSCTRToUserRequest struct {
	TreezorUserId   int64  `json:"userId"`
	TreezorWalletId int64  `json:"walletId"`
	PaymentMethodId int    `json:"paymentMethodId"`
	MessageToUser   string `json:"messageToUser"`
	Amount          float64
}

type SendSCTRToUserResponse struct {
	Payin *Payin `json:"payin"`
}

// =============

const Method_GetTreezorUserCards = "treezor.GetTreezorUserCards"

type GetTreezorUserCardsRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
}

type GetTreezorUserCardsResponse struct {
	Cards []*Card `json:"cards"`
}

// =============

const Method_CreateSCAWalletForUser = "treezor.CreateSCAWalletForUser"

type CreateSCAWalletForUserRequest struct {
	TreezorUserId int64  `json:"treezorUserId"`
	WalletTag     string `json:"walletTag"`
}

type CreateSCAWalletForUserResponse struct {
	TreezorSCAWallet *SCAWallet `json:"scaWallet"`
}

func CreateSCAWalletForUser(busrpc bus2.RpcClient, treezorUserId int64, walletTag string) (*CreateSCAWalletForUserResponse, error) {
	request := CreateSCAWalletForUserRequest{
		TreezorUserId: treezorUserId,
		WalletTag:     walletTag,
	}
	response, err := bus2.CallMethodMU[CreateSCAWalletForUserResponse](busrpc, Method_CreateSCAWalletForUser, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetSCAWalletByTreezorID = "treezor.GetSCAWalletByTreezorID"

type GetSCAWalletByTreezorIDRequest struct {
	TreezorWalletId string `json:"treezorWalletId"`
}

type GetSCAWalletByTreezorIDResponse struct {
	TreezorSCAWallet *SCAWallet `json:"scaWallet"`
}

func GetSCAWalletByTreezorID(busrpc bus2.RpcClient, treezorWalletId string) (*GetSCAWalletByTreezorIDResponse, error) {
	if strings.TrimSpace(treezorWalletId) == "" {
		return nil, nil
	}

	request := GetSCAWalletByTreezorIDRequest{
		TreezorWalletId: treezorWalletId,
	}
	response, err := bus2.CallMethodMU[GetSCAWalletByTreezorIDResponse](busrpc, Method_GetSCAWalletByTreezorID, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetFreshSCAWalletByTreezorID = "treezor.GetFreshSCAWalletByTreezorID"

type GetFreshSCAWalletByTreezorIdRequest struct {
	TreezorWalletId string `json:"treezorWalletId"`
	TreezorUserId   int64  `json:"treezorUserId"`
}

type GetFreshSCAWalletByTreezorIdResponse struct {
	TreezorSCAWallet *SCAWallet `json:"scaWallet"`
}

func GetFreshSCAWalletByTreezorId(busrpc bus2.RpcClient, treezorWalletId string) (*GetFreshSCAWalletByTreezorIdResponse, error) {
	if strings.TrimSpace(treezorWalletId) == "" {
		return nil, nil
	}

	request := GetFreshSCAWalletByTreezorIdRequest{
		TreezorWalletId: treezorWalletId,
	}
	response, err := bus2.CallMethodMU[GetFreshSCAWalletByTreezorIdResponse](busrpc, Method_GetFreshSCAWalletByTreezorID, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_DeleteSCAWallet = "treezor.DeleteSCAWallet"

type DeleteSCAWalletRequest struct {
	TreezorWalletId string `json:"treezorWalletId"`
	TreezorUserId   int64  `json:"treezorUserId"`
}

type DeleteSCAWalletResponse struct {
	Ok bool `json:"ok"`
}

func DeleteSCAWallet(busrpc bus2.RpcClient, treezorWalletId string) (*DeleteSCAWalletResponse, error) {
	if strings.TrimSpace(treezorWalletId) == "" {
		return nil, nil
	}

	request := DeleteSCAWalletRequest{
		TreezorWalletId: treezorWalletId,
	}
	response, err := bus2.CallMethodMU[DeleteSCAWalletResponse](busrpc, Method_DeleteSCAWallet, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_ExchangeSCAProof = "treezor.ExchangeSCAProof"

type ExchangeSCAProofRequest struct {
	TreezorUserId int64  `json:"treezorId"`
	JWS           string `json:"jws"`
}

type ExchangeSCAProofResponse struct {
	Ok bool `json:"ok"`
}

func ExchangeSCAProof(busrpc bus2.RpcClient, treezorUserId int64, jws string) (*ExchangeSCAProofResponse, error) {
	jws = strings.TrimSpace(jws)
	if jws == "" {
		return nil, nil
	}

	request := ExchangeSCAProofRequest{
		TreezorUserId: treezorUserId,
		JWS:           jws,
	}
	response, err := bus2.CallMethodMU[ExchangeSCAProofResponse](busrpc, Method_ExchangeSCAProof, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_CacheSCAProof = "treezor.CacheSCAProof"

type CacheSCAProofRequest struct {
	TreezorUserId int64  `json:"treezorId"`
	JWS           string `json:"jws"`
}

type CacheSCAProofResponse struct {
	Ok           bool   `json:"ok"`
	SessionKey   string `json:"sessionKey"`
	OperationKey string `json:"operationKey"`
	OperationUrl string `json:"operationUrl"`
}

func CacheSCAProof(busrpc bus2.RpcClient, treezorUserId int64, jws string) (*CacheSCAProofResponse, error) {
	jws = strings.TrimSpace(jws)
	if jws == "" {
		return nil, nil
	}

	request := CacheSCAProofRequest{
		TreezorUserId: treezorUserId,
		JWS:           jws,
	}
	response, err := bus2.CallMethodMU[CacheSCAProofResponse](busrpc, Method_CacheSCAProof, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetFreshWallets = "treezor.GetFreshWallets"

type GetFreshWalletsRequest struct {
	TreezorUserId int64 `json:"treezorUserId"`
}

type GetFreshWalletsResponse struct {
	Wallets []*Wallet `json:"wallets"`
}

func GetFreshWallets(busrpc bus2.RpcClient, treezorUserId int64) (*GetFreshWalletsResponse, error) {
	request := GetFreshWalletsRequest{
		TreezorUserId: treezorUserId,
	}
	response, err := bus2.CallMethodMU[GetFreshWalletsResponse](busrpc, Method_GetFreshWallets, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetFreshTransactions = "treezor.GetFreshTransactions"

type GetFreshTransactionsRequest struct {
	TreezorWalletId int64     `json:"treezorWalletId"`
	TreezorUserId   int64     `json:"treezorUserId"`
	DateFrom        time.Time `json:"dateFrom"`
	DateTo          time.Time `json:"dateTo"`
}

type GetFreshTransactionsResponse struct {
	Transactions []*banking.Transaction `json:"transactions"`
}

func GetFreshTransactions(busrpc bus2.RpcClient, treeozrUserId int64, treezorWalletId int64, dateFrom time.Time, dateTo time.Time) (*GetFreshTransactionsResponse, error) {
	request := GetFreshTransactionsRequest{
		TreezorWalletId: treezorWalletId,
		TreezorUserId:   treeozrUserId,
		DateFrom:        dateFrom,
		DateTo:          dateTo,
	}
	response, err := bus2.CallMethodMU[GetFreshTransactionsResponse](busrpc, Method_GetFreshTransactions, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetAccountStatement = "treezor.GetAccountStatement"

type GetAccountStatementRequest struct {
	TreezorWalletId int64 `json:"treezorWalletId" validate:"required"`
	TreezorUserId   int64 `json:"treezorUserId" validate:"required"`
	Year            int   `json:"year" validate:"required"`
	Month           int   `json:"month" validate:"required"`
}

type GetAccountStatementResponse struct {
	DownloadURL string `json:"downloadURL"`
	ExpiresIn   int    `json:"expiresIn"` // in seconds
}

// Helper function to get an account statement from Treezor
func GetAccountStatement(busrpc bus2.RpcClient, treezorUserId int64, treezorWalletId int64, year int, month int) (*GetAccountStatementResponse, error) {
	// Get the wallet to find the treezorUserId
	walletResp, err := bus2.CallMethodMU[GetWalletByTreezorIdResponse](busrpc, Method_GetWalletByTreezorId, &GetWalletByTreezorIdRequest{
		TreezorWalletId: treezorWalletId,
	}, 10*time.Second)
	if err != nil {
		return nil, err
	}

	if walletResp.TreezorWallet == nil {
		return nil, fmt.Errorf("wallet not found for ID %d", treezorWalletId)
	}

	request := GetAccountStatementRequest{
		TreezorWalletId: treezorWalletId,
		TreezorUserId:   treezorUserId,
		Year:            year,
		Month:           month,
	}

	response, err := bus2.CallMethodMU[GetAccountStatementResponse](busrpc, Method_GetAccountStatement, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetFreshCardTransactions = "treezor.GetFreshCardTransactions"

type GetFreshCardTransactionsRequest struct {
	TreezorUserId int64     `json:"treezorUserId"`
	TreezorCardId int64     `json:"treezorCardId"`
	DateFrom      time.Time `json:"dateFrom"`
	DateTo        time.Time `json:"dateTo"`
}

type GetFreshCardTransactionsResponse struct {
	CardTransactions []*CardTransaction `json:"cardTransactions"`
}

func GetFreshCardTransactions(busrpc bus2.RpcClient, treezorUserId int64, treezorCardId int64, dateFrom time.Time, dateTo time.Time) (*GetFreshCardTransactionsResponse, error) {
	request := GetFreshCardTransactionsRequest{
		TreezorUserId: treezorUserId,
		TreezorCardId: treezorCardId,
		DateFrom:      dateFrom,
		DateTo:        dateTo,
	}

	if treezorCardId == 0 {
		return nil, nil
	}

	response, err := bus2.CallMethodMU[GetFreshCardTransactionsResponse](busrpc, Method_GetFreshCardTransactions, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GetFreshOperations = "treezor.GetFreshOperations"

type GetFreshOperationsRequest struct {
	TreezorUserId   int64     `json:"treezorUserId"`
	TreezorWalletId int64     `json:"treezorWalletId"`
	PageSize        int       `json:"pageSize"`
	Cursor          string    `json:"cursor"`
	DateFrom        time.Time `json:"dateFrom"`
	DateTo          time.Time `json:"dateTo"`
}

type GetFreshOperationsResponse struct {
	Operations []*Operation `json:"operations"`
	Cursor     string       `json:"cursor"`
	CursorNext string       `json:"cursorNext"`
	CursorPrev string       `json:"cursorPrev"`
}

func GetFreshOperations(busrpc bus2.RpcClient, treezorUserId int64, treezorWalletId int64, pageSize int, cursor string, dateFrom time.Time, dateTo time.Time) (*GetFreshOperationsResponse, error) {
	request := GetFreshOperationsRequest{
		TreezorUserId:   treezorUserId,
		TreezorWalletId: treezorWalletId,
		PageSize:        pageSize,
		Cursor:          cursor,
		DateFrom:        dateFrom,
		DateTo:          dateTo,
	}

	if treezorWalletId <= 0 {
		return nil, nil
	}

	if request.PageSize > 100 {
		request.PageSize = 100
	}

	response, err := bus2.CallMethodMU[GetFreshOperationsResponse](busrpc, Method_GetFreshOperations, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// ============

const Method_GetCardTxAuthsByIDs = "treezor.GetCardTxAuthsByIDs"

type GetCardTxAuthsByIDsRequest struct {
	TreezorCardTxAuthIds []string `json:"treezorCardTxAuthIds"`
}

type GetCardTxAuthsByIDsResponse struct {
	CardTxAuths []*CardTxAuth `json:"cardTxAuths"`
}

func GetCardTxAuthsByIDs(busrpc bus2.RpcClient, treezorCardTxAuthIds []string) ([]*CardTxAuth, error) {
	if len(treezorCardTxAuthIds) == 0 {
		return nil, nil
	}

	request := GetCardTxAuthsByIDsRequest{
		TreezorCardTxAuthIds: treezorCardTxAuthIds,
	}
	response, err := bus2.CallMethodMU[GetCardTxAuthsByIDsResponse](busrpc, Method_GetCardTxAuthsByIDs, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response.CardTxAuths, nil
}

// ============

const Method_AuthenticateCardTransaction = "treezor.AuthenticateCardTransaction"

type AuthenticateCardTransactionRequest struct {
	TreezorUserId       int64  `json:"treezorUserId"`
	TreezorCardTxAuthId string `json:"treezorCardTxAuthId"`
	Result              string `json:"authenticationResult"`
	Signature           string `json:"authenticationSignature"`
}

type AuthenticateCardTransactionResponse struct {
	Ok          bool   `json:"ok"`
	StatusCode  int    `json:"statusCode"`
	RawResponse string `json:"rawResponse"`
}

func AuthenticateCardTransaction(busrpc bus2.RpcClient, treezorUserId int64, treezorCardTxAuthId, result, signature string) (*AuthenticateCardTransactionResponse, error) {
	request := AuthenticateCardTransactionRequest{
		TreezorCardTxAuthId: treezorCardTxAuthId,
		TreezorUserId:       treezorUserId,
		Result:              result,
		Signature:           signature,
	}
	response, err := bus2.CallMethodMU[AuthenticateCardTransactionResponse](busrpc, Method_AuthenticateCardTransaction, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_CreateMandate = "treezor.CreateMandate"

type CreateMandateRequest struct {
	TreezorUserId int64  `json:"treezorUserId"`
	Label         string `json:"label"`
	SddType       string `json:"sddType"`
	SequenceType  string `json:"sequenceType"`
	DebtorName    string `json:"debtorName"`
	DebtorAddress string `json:"debtorAddress"`
	DebtorCity    string `json:"debtorCity"`
	DebtorZipCode string `json:"debtorZipCode"`
	DebtorCountry string `json:"debtorCountry"`
	DebtorIban    string `json:"debtorIban"`
	SignatureDate string `json:"signatureDate"`
	CreatedIp     string `json:"createdIp"`
}

type CreateMandateResponse struct {
	Mandate *Mandate `json:"mandate"`
}

// =============
func CreateMandate(busrpc bus2.RpcClient, request *CreateMandateRequest) (*CreateMandateResponse, error) {
	response, err := bus2.CallMethodMU[CreateMandateResponse](busrpc, Method_CreateMandate, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

const Method_GetMandateByID = "treezor.GetMandateByID"

type GetMandateByIDRequest struct {
	TreezorUserId int64  `json:"treezorUserId"`
	MandateId     string `json:"mandateId"`
}

type GetMandateByIDResponse struct {
	Mandate *Mandate `json:"mandate"`
}

func GetMandateByID(busrpc bus2.RpcClient, treezorUserId int64, mandateId string) (*GetMandateByIDResponse, error) {
	request := GetMandateByIDRequest{
		TreezorUserId: treezorUserId,
		MandateId:     mandateId,
	}
	response, err := bus2.CallMethodMU[GetMandateByIDResponse](busrpc, Method_GetMandateByID, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_SearchMandates = "treezor.SearchMandates"

type SearchMandatesRequest struct {
	TreezorUserId   int64 `json:"treezorUserId"`
	TreezorWalletId int64 `json:"treezorWalletId,omitempty"`
}

type SearchMandatesResponse struct {
	Mandates []*Mandate `json:"mandates"`
}

func SearchMandates(busrpc bus2.RpcClient, treezorUserId int64, treezorWalletId int64) (*SearchMandatesResponse, error) {
	request := SearchMandatesRequest{
		TreezorUserId:   treezorUserId,
		TreezorWalletId: treezorWalletId,
	}
	response, err := bus2.CallMethodMU[SearchMandatesResponse](busrpc, Method_SearchMandates, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_DeleteMandate = "treezor.DeleteMandate"

type DeleteMandateRequest struct {
	TreezorUserId int64  `json:"treezorUserId"`
	MandateId     string `json:"mandateId"`
}

type DeleteMandateResponse struct {
	Ok bool `json:"ok"`
}

func DeleteMandate(busrpc bus2.RpcClient, treezorUserId int64, mandateId string) (*DeleteMandateResponse, error) {
	request := DeleteMandateRequest{
		TreezorUserId: treezorUserId,
		MandateId:     mandateId,
	}
	response, err := bus2.CallMethodMU[DeleteMandateResponse](busrpc, Method_DeleteMandate, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_SendSDDEToUser = "treezor.SendSDDEToUser"

type SendSDDEToUserRequest struct {
	TreezorUserId int64   `json:"userId"`
	WalletID      int64   `json:"walletId"`
	MandateID     int64   `json:"mandateId"`
	Amount        float64 `json:"amount"`
	Currency      string  `json:"currency"`
	PayinDate     string  `json:"payinDate"`
	MessageToUser string  `json:"messageToUser"`
}

type SendSDDEToUserResponse struct {
	Payin *Payin `json:"payin"`
}

func SendSDDEToUser(busrpc bus2.RpcClient, request *SendSDDEToUserRequest) (*SendSDDEToUserResponse, error) {
	response, err := bus2.CallMethodMU[SendSDDEToUserResponse](busrpc, Method_SendSDDEToUser, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// =============

const Method_GenerateCardTransactionsURL = "treezor.GenerateCardTransactionsURL"

type GenerateCardTransactionsURLRequest struct {
	TreezorCardId int64  `json:"treezorCardId" validate:"required"`
	DateFrom      string `json:"dateFrom" validate:"required"` // YYYY-MM-DD format
	DateTo        string `json:"dateTo" validate:"required"`   // YYYY-MM-DD format
}

type GenerateCardTransactionsURLResponse struct {
	URL                 string `json:"url"`
	ConvertedDateFromTZ string `json:"convertedDateFromTZ"` // Paris timezone formatted date
	ConvertedDateToTZ   string `json:"convertedDateToTZ"`   // Paris timezone formatted date
}

func GenerateCardTransactionsURL(busrpc bus2.RpcClient, treezorCardId int64, dateFrom, dateTo string) (*GenerateCardTransactionsURLResponse, error) {
	request := GenerateCardTransactionsURLRequest{
		TreezorCardId: treezorCardId,
		DateFrom:      dateFrom,
		DateTo:        dateTo,
	}

	response, err := bus2.CallMethodMU[GenerateCardTransactionsURLResponse](busrpc, Method_GenerateCardTransactionsURL, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response, nil
}
