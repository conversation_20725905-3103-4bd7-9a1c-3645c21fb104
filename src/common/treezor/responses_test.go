package treezor

import (
	"bytes"
	"testing"
)

func TestPatchBadTreezorStringFieldsThatAreNumbers(t *testing.T) {
	msg := []byte(`{"users":[{"dontTouch":"ok","userId":"1","userTypeId":1,"kycLevel":1,"kycReview":1,"specifiedUSPerson":"1"},{"userId":2,"userTypeId":2,"kycLevel":2,"kycReview":"2","specifiedUSPerson":2}]}`)
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "users", []string{"userId", "userTypeId", "kycLevel", "kycReview", "specifiedUSPerson"})
	if err != nil {
		t.Error(err)
	}

	expected := []byte(`{"users":[{"dontTouch":"ok","userId":1,"userTypeId":1,"kycLevel":1,"kycReview":1,"specifiedUSPerson":1},{"userId":2,"userTypeId":2,"kycLevel":2,"kycReview":2,"specifiedUSPerson":2}]}`)
	if !bytes.Equal(msg, expected) {
		t.Errorf("Expected %s, got %s", expected, msg)
	}
}

func TestPatchBadTreezorStringFieldsThatAreBooleans(t *testing.T) {
	msg := []byte(`{"cardtransactions":[{"cardtransactionId":"578153572","cardId":"5090296","walletId":"14096016","walletCurrency":"978","merchantId":"2431146","merchantName":"PAUL","merchantCity":"PARIS LA D","merchantCountry":"FRA","paymentLocalTime":"160933","publicToken":"246800038","paymentAmount":"8.10","paymentCurrency":"978","fees":"0.00","paymentCountry":"FRA","paymentId":"316435670","paymentStatus":"A","paymentLocalAmount":"8.10","posCardholderPresence":"0","posPostcode":"92092","posCountry":"250","posTerminalId":"M1339363","posCardPresence":"0","panEntryMethod":"5","authorizationNote":"PSD2 Counter Reset\n","authorizationResponseCode":"0","authorizationIssuerId":"13505492991","authorizationIssuerTime":"2023-10-16 15:10:10","authorizationMti":"100","authorizedBalance":"50.00","limitAtmYear":"0","limitAtmMonth":"0","limitAtmWeek":"2000","limitAtmDay":"2000","limitAtmAll":"0","limitPaymentYear":"0","limitPaymentMonth":"0","limitPaymentWeek":"3000","limitPaymentDay":"2000","limitPaymentAll":"0","paymentDailyLimit":"0.00","totalLimitAtmYear":"0.00","totalLimitAtmMonth":"0.00","totalLimitAtmWeek":"0.00","totalLimitAtmDay":"0.00","totalLimitAtmAll":"0.00","totalLimitPaymentYear":"0.00","totalLimitPaymentMonth":"0.00","totalLimitPaymentWeek":"8.10","totalLimitPaymentDay":"8.10","totalLimitPaymentAll":"0.00","cardDigitalizationExternalId":"0","mccCode":"5814","acquirerId":"12653","is3DS":"0","merchantAddress":null,"paymentLocalDate":null}]}`)
	msg, err := PatchBadStringFieldsThatShouldBeBoolean(msg, "cardtransactions", []string{"is3DS"})
	if err != nil {
		t.Error(err)
	}

	expected := []byte(`{"cardtransactions":[{"cardtransactionId":"578153572","cardId":"5090296","walletId":"14096016","walletCurrency":"978","merchantId":"2431146","merchantName":"PAUL","merchantCity":"PARIS LA D","merchantCountry":"FRA","paymentLocalTime":"160933","publicToken":"246800038","paymentAmount":"8.10","paymentCurrency":"978","fees":"0.00","paymentCountry":"FRA","paymentId":"316435670","paymentStatus":"A","paymentLocalAmount":"8.10","posCardholderPresence":"0","posPostcode":"92092","posCountry":"250","posTerminalId":"M1339363","posCardPresence":"0","panEntryMethod":"5","authorizationNote":"PSD2 Counter Reset\n","authorizationResponseCode":"0","authorizationIssuerId":"13505492991","authorizationIssuerTime":"2023-10-16 15:10:10","authorizationMti":"100","authorizedBalance":"50.00","limitAtmYear":"0","limitAtmMonth":"0","limitAtmWeek":"2000","limitAtmDay":"2000","limitAtmAll":"0","limitPaymentYear":"0","limitPaymentMonth":"0","limitPaymentWeek":"3000","limitPaymentDay":"2000","limitPaymentAll":"0","paymentDailyLimit":"0.00","totalLimitAtmYear":"0.00","totalLimitAtmMonth":"0.00","totalLimitAtmWeek":"0.00","totalLimitAtmDay":"0.00","totalLimitAtmAll":"0.00","totalLimitPaymentYear":"0.00","totalLimitPaymentMonth":"0.00","totalLimitPaymentWeek":"8.10","totalLimitPaymentDay":"8.10","totalLimitPaymentAll":"0.00","cardDigitalizationExternalId":"0","mccCode":"5814","acquirerId":"12653","is3DS":false,"merchantAddress":null,"paymentLocalDate":null}]}`)
	if !bytes.Equal(msg, expected) {
		t.Errorf("Expected %s, got %s", expected, msg)
	}
}
