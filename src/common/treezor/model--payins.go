package treezor

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	json2 "yochbee/_base/json"
	"yochbee/common/banking"

	"github.com/eliezedeck/gobase/logging"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go.uber.org/zap"
)

// Payin is as close as possible to the Treezor payin structure.
// See https://docs.treezor.com/guide/cards/events.html#structure-of-a-payin-create
type Payin struct {
	ID string `bson:"_id" json:"id"`

	// TreezorPayinID can either be an int64 or a string. That's because it will transition to UUIDv4 in the future from an int64: see https://docs.treezor.com/guide/cards/acquisition.html#acquiring
	TreezorPayinID       interface{} `bson:"treezorPayinId"        json:"payinId"` //
	TreezorWalletID      int64       `bson:"treezorWalletId"       json:"walletId"`
	TreezorUserID        int64       `bson:"treezorUserId"         json:"userId"`
	TreezorCartID        int64       `bson:"treezorCartId"         json:"cartId"`
	TreezorMandateID     int64       `bson:"treezorMandateId"      json:"mandateId"`
	TreezorVirtualIbanID int64       `bson:"treezorVirtualIbanId"  json:"virtualIbanId"`

	PayinTag             string  `bson:"payinTag"              json:"payinTag"`
	PayinStatus          string  `bson:"payinStatus"           json:"payinStatus"`
	CodeStatus           int     `bson:"codeStatus"            json:"codeStatus"`
	InformationStatus    string  `bson:"informationStatus"     json:"informationStatus"`
	WalletEventName      string  `bson:"walletEventName"       json:"walletEventName"`
	WalletAlias          string  `bson:"walletAlias"           json:"walletAlias"`
	UserFirstname        string  `bson:"userFirstname"         json:"userFirstname"`
	UserLastname         string  `bson:"userLastname"          json:"userLastname"`
	MessageToUser        string  `bson:"messageToUser"         json:"messageToUser"`
	PaymentMethodID      int     `bson:"paymentMethodId"       json:"paymentMethodId"`
	SubtotalItems        float64 `bson:"subtotalItems"         json:"subtotalItems"`
	SubtotalServices     float64 `bson:"subtotalServices"      json:"subtotalServices"`
	SubtotalTax          float64 `bson:"subtotalTax"           json:"subtotalTax"`
	Amount               float64 `bson:"amount"                json:"amount"`
	Currency             string  `bson:"currency"              json:"currency"`
	DistributorFee       string  `bson:"distributorFee"        json:"distributorFee"`
	CreatedDate          string  `bson:"createdDate"           json:"createdDate"`
	CreatedIP            string  `bson:"createdIp"             json:"createdIp"`
	PaymentHTML          string  `bson:"paymentHtml"           json:"paymentHtml"`
	PaymentLanguage      string  `bson:"paymentLanguage"       json:"paymentLanguage"`
	PaymentPostURL       string  `bson:"paymentPostUrl"        json:"paymentPostUrl"`
	PaymentPostDataURL   string  `bson:"paymentPostDataUrl"    json:"paymentPostDataUrl"`
	PaymentAcceptedURL   string  `bson:"paymentAcceptedUrl"    json:"paymentAcceptedUrl"`
	PaymentWaitingURL    string  `bson:"paymentWaitingUrl"     json:"paymentWaitingUrl"`
	PaymentRefusedURL    string  `bson:"paymentRefusedUrl"     json:"paymentRefusedUrl"`
	PaymentCanceledURL   string  `bson:"paymentCanceledUrl"    json:"paymentCanceledUrl"`
	PaymentExceptionURL  string  `bson:"paymentExceptionUrl"   json:"paymentExceptionUrl"`
	IbanFullname         string  `bson:"ibanFullname"          json:"ibanFullname"`
	IbanID               string  `bson:"ibanId"                json:"ibanId"`
	IbanBic              string  `bson:"ibanBic"               json:"ibanBic"`
	IbanTxEndToEndID     string  `bson:"ibanTxEndToEndId"      json:"ibanTxEndToEndId"`
	IbanTxID             string  `bson:"ibanTxId"              json:"ibanTxId"`
	RefundAmount         string  `bson:"refundAmount"          json:"refundAmount"`
	TotalRows            int     `bson:"totalRows"             json:"totalRows"`
	ForwardURL           string  `bson:"forwardUrl"            json:"forwardUrl"`
	PayinDate            string  `bson:"payinDate"             json:"payinDate"`
	CreditorName         string  `bson:"creditorName"          json:"creditorName"`
	CreditorAddressLine  string  `bson:"creditorAddressLine"   json:"creditorAddressLine"`
	CreditorCountry      string  `bson:"creditorCountry"       json:"creditorCountry"`
	CreditorIban         string  `bson:"creditorIban"          json:"creditorIban"`
	CreditorBIC          string  `bson:"creditorBIC"           json:"creditorBIC"`
	VirtualIbanReference string  `bson:"virtualIbanReference"  json:"virtualIbanReference"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (p *Payin) GetTreezorID() (string, interface{}) {
	return "treezorPayinId", p.TreezorPayinID
}

func (p *Payin) TreezorTimestamp() int64 {
	return p.WebhookTimestamp
}

func (p *Payin) SetTreezorTimestamp(ts int64) {
	p.WebhookTimestamp = ts
}

func (p *Payin) SetInsertID(id string) {
	p.ID = id
}

func (p *Payin) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*Payin)

	now := time.Now()
	p.UpdatedAt = &now
	p.WebhookTimestamp = in.WebhookTimestamp

	// Treezor IDs left intact

	p.PayinTag = in.PayinTag
	p.PayinStatus = in.PayinStatus
	p.CodeStatus = in.CodeStatus
	p.InformationStatus = in.InformationStatus
	p.WalletEventName = in.WalletEventName
	p.WalletAlias = in.WalletAlias
	p.UserFirstname = in.UserFirstname
	p.UserLastname = in.UserLastname
	p.MessageToUser = in.MessageToUser
	p.PaymentMethodID = in.PaymentMethodID
	p.SubtotalItems = in.SubtotalItems
	p.SubtotalServices = in.SubtotalServices
	p.SubtotalTax = in.SubtotalTax
	p.Amount = in.Amount
	p.Currency = in.Currency
	p.DistributorFee = in.DistributorFee
	p.CreatedDate = in.CreatedDate
	p.CreatedIP = in.CreatedIP
	p.PaymentHTML = in.PaymentHTML
	p.PaymentLanguage = in.PaymentLanguage
	p.PaymentPostURL = in.PaymentPostURL
	p.PaymentPostDataURL = in.PaymentPostDataURL
	p.PaymentAcceptedURL = in.PaymentAcceptedURL
	p.PaymentWaitingURL = in.PaymentWaitingURL
	p.PaymentRefusedURL = in.PaymentRefusedURL
	p.PaymentCanceledURL = in.PaymentCanceledURL
	p.PaymentExceptionURL = in.PaymentExceptionURL
	p.IbanFullname = in.IbanFullname
	p.IbanID = in.IbanID
	p.IbanBic = in.IbanBic
	p.IbanTxEndToEndID = in.IbanTxEndToEndID
	p.IbanTxID = in.IbanTxID
	p.RefundAmount = in.RefundAmount
	p.TotalRows = in.TotalRows
	p.ForwardURL = in.ForwardURL
	p.PayinDate = in.PayinDate
	p.CreditorName = in.CreditorName
	p.CreditorAddressLine = in.CreditorAddressLine
	p.CreditorCountry = in.CreditorCountry
	p.CreditorIban = in.CreditorIban
	p.CreditorBIC = in.CreditorBIC
	p.VirtualIbanReference = in.VirtualIbanReference
}

func (p *Payin) EnrichBankingTransaction(bankingTransaction *banking.Transaction) bool {
	if bankingTransaction.TreezorPayinWebhookTimestamp != 0 && bankingTransaction.TreezorPayinWebhookTimestamp > p.WebhookTimestamp {
		logging.L.Info("Payin.EnrichBankingTransaction: Payin is older than the Transaction", zap.Any("transaction", bankingTransaction), zap.Any("p", p))
		return false
	}

	bankingTransaction.TreezorPayinWebhookTimestamp = p.WebhookTimestamp

	bankingTransaction.TreezorPayinId = p.TreezorPayinID
	if f, ok := bankingTransaction.TreezorPayinId.(float64); ok {
		bankingTransaction.TreezorPayinId = int64(f)
	}
	bankingTransaction.TreezorPayinMethodID = p.PaymentMethodID
	bankingTransaction.TransactionType = banking.TransactionTypePayin
	bankingTransaction.TransactionTypeStr = banking.TransactionTypePayin.String()
	bankingTransaction.Name = p.WalletEventName // this is the default name we'll use for now
	bankingTransaction.Amount = p.Amount
	bankingTransaction.Description = p.MessageToUser
	bankingTransaction.Currency = p.Currency

	// Improve the name that appears in the mobile app
	// See also: https://docs.treezor.com/guide/operations/introduction.html#objects-types-mapping
	switch p.PaymentMethodID {
	case 20:
		// SEPA transfer in, aka SCTR
		bankingTransaction.Name = "Réception Virement SEPA (SCTR)"
	case 2, 4, 5:
		// Bank transfer
		bankingTransaction.Name = "Réception Virement Bancaire"
	case 25:
		// Top-up wallet with a Captured Card
		bankingTransaction.Name = "Chargement du compte"
	case 1, 7, 10, 11, 14, 23, 24:
		// Top-up with a card
		bankingTransaction.Name = "Chargement du compte par carte"
	case 27:
		// SEPA transfer in, instant, aka SCTR Inst
		bankingTransaction.Name = "Réception Virement SEPA Instantanée (SCTR Inst)"
	}

	switch p.PayinStatus {
	case "PENDING":
		bankingTransaction.TransactionStatus = banking.TransactionStatusPending
		bankingTransaction.TransactionStatusStr = banking.TransactionStatusPending.String()
	case "CANCELED":
		bankingTransaction.TransactionStatus = banking.TransactionStatusCanceled
		bankingTransaction.TransactionStatusStr = banking.TransactionStatusCanceled.String()
	case "VALIDATED":
		bankingTransaction.TransactionStatus = banking.TransactionStatusSuccess
		bankingTransaction.TransactionStatusStr = banking.TransactionStatusSuccess.String()
	default:
		logging.L.Warn("Payin.EnrichBankingTransaction: Uncovered PayinStatus", zap.Any("transaction", bankingTransaction), zap.Any("p", p), zap.String("payinStatus", p.PayinStatus))
	}

	logging.L.Info("Payin.EnrichBankingTransaction: Synced Payin to Transaction", zap.Any("transaction", bankingTransaction), zap.Any("p", p))
	return true
}

func UnwrapPayinsFromTreezorResponse(msg []byte) ([]*Payin, error) {
	// Convert all int fields that are strings to ints
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "payins", []string{
		"walletId",
		"userId",
		"cartId",
		"mandateId",
		"virtualIbanId",
		"codeStatus",
		"paymentMethodId",
		"subtotalItems",
		"subtotalServices",
		"subtotalTax",
		"amount",
		"totalRows"}) // exclude payinId
	if err != nil {
		return nil, err
	}

	// If the payinId can map to a number, use that as the ID. This is important as we should not have '1234' and 1234
	// map to different objects in the database while they are both numbers.
	// UUID strings are not affected by this.
	failed := false
	h := gjson.GetBytes(msg, "payins.#.payinId")
	idsInt64 := make([]int64, 0, len(h.Array()))
	h.ForEach(func(key, value gjson.Result) bool {
		v, err := strconv.ParseInt(value.String(), 10, 64)
		if err != nil {
			return true // continue iterating, probably a UUID
		}

		msg, err = sjson.SetBytes(msg, fmt.Sprintf("payins.%s.payinId", key.String()), v)
		if err != nil {
			failed = true
			return false
		}
		idsInt64 = append(idsInt64, v)
		return true
	})
	if failed {
		return nil, err
	}

	payload := struct {
		Payins []*Payin `json:"payins"`
	}{}
	if err = json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}

	// Patch the payinId to be an int64 from the default Unmarshal's float64
	for i, payin := range payload.Payins {
		if _, ok := payin.TreezorPayinID.(float64); ok {
			payin.TreezorPayinID = idsInt64[i]
		}
	}

	return payload.Payins, nil
}

func (p *Payin) ToMobileJSON() ([]byte, error) {
	return json2.MarshalRemoveFields(p, "treezorWebhookTimestamp")
}
