// Package methods will contain names of publicly used methods. If a method is provided by a microservice A and is used
// in microservice B, put its name here.
//
// It will provide the following benefits:
// - static type checking
// - avoid human errors
// - find reference and uses of a particular method

package treezor

import (
	"encoding/json"
	"time"
)

// User is a mirrored representation of what's stored on Treezor. It is **NOT** an Account.
type User struct {
	ID string `bson:"_id" json:"-"`

	TreezorUserId int64  `bson:"treezorUserId"            json:"userId"`
	Tag           string `bson:"tag"                      json:"userTag"`

	UserTypeId        int    `bson:"userTypeId"        json:"userTypeId"`
	UserStatus        string `bson:"userStatus"        json:"userStatus"`
	SpecifiedUSPerson int    `bson:"specifiedUSPerson" json:"specifiedUSPerson"`
	KYCLevel          int    `bson:"kycLevel"          json:"kycLevel"`
	KYCReview         int    `bson:"kycReview"         json:"kycReview"`
	KYCReviewComment  string `bson:"kycReviewComment"  json:"kycReviewComment"`

	Email    string `json:"email"`
	Address1 string `json:"address1"`
	Mobile   string `json:"mobile"` // must be in E.164 format
	Phone    string `json:"phone"`  // must be in E.164 format

	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
	Address2  string `json:"address2"`
	PostCode  string `json:"postcode"`
	State     string `json:"state"` // corresponds to the City in Accounts
	Birthday  string `json:"birthday"`
	Country   string `json:"country"` // must be in ISO 3166-1 alpha-2 format

	Title          string `json:"title"`
	PlaceOfBirth   string `json:"placeOfBirth"`
	BirthCountry   string `json:"birthCountry"`
	Nationality    string `json:"nationality"`
	IncomeRange    string `json:"incomeRange"`
	PersonalAssets string `json:"personalAssets"`
	Occupation     string `json:"occupation"`

	CreatedAt  time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt  *time.Time `bson:"updatedAt" json:"updatedAt"`
	CanceledAt *time.Time `bson:"canceledAt" json:"canceledAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (u *User) GetTreezorID() (string, interface{}) {
	return "treezorUserId", u.TreezorUserId
}

func (u *User) TreezorTimestamp() int64 {
	return u.WebhookTimestamp
}

func (u *User) SetTreezorTimestamp(ts int64) {
	u.WebhookTimestamp = ts
}

func (u *User) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*User)

	now := time.Now()
	u.UpdatedAt = &now
	u.CanceledAt = in.CanceledAt
	u.WebhookTimestamp = in.WebhookTimestamp

	u.UserTypeId = in.UserTypeId
	u.UserStatus = in.UserStatus
	u.SpecifiedUSPerson = in.SpecifiedUSPerson
	u.KYCLevel = in.KYCLevel
	u.KYCReview = in.KYCReview
	u.KYCReviewComment = in.KYCReviewComment

	u.Email = in.Email
	u.Address1 = in.Address1
	u.Mobile = in.Mobile

	u.FirstName = in.FirstName
	u.LastName = in.LastName
	u.Address2 = in.Address2
	u.PostCode = in.PostCode
	u.State = in.State
	u.Birthday = in.Birthday
	u.Country = in.Country

	u.Title = in.Title
	u.PlaceOfBirth = in.PlaceOfBirth
	u.BirthCountry = in.BirthCountry
	u.Nationality = in.Nationality
	u.IncomeRange = in.IncomeRange
	u.PersonalAssets = in.PersonalAssets
	u.Occupation = in.Occupation
}

func (u *User) SetInsertID(id string) {
	u.ID = id
	u.CreatedAt = time.Now()
}

func UnwrapUsersFromTreezorResponse(msg []byte) ([]*User, error) {
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "users", []string{"userId", "userTypeId", "specifiedUSPerson", "kycLevel", "kycReview"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Users []*User `json:"users"`
	}{}
	if err := json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Users, nil
}

type UserCreateDTO struct {
	UserTypeId        int `bson:"userTypeId"        json:"userTypeId"`
	SpecifiedUSPerson int `bson:"specifiedUSPerson" json:"specifiedUSPerson"`

	Email    string `json:"email"`
	Address1 string `json:"address1"`
	Mobile   string `json:"mobile"` // must be in E.164 format
	Phone    string `json:"phone"`  // must be in E.164 format

	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
	Address2  string `json:"address2"`
	PostCode  string `json:"postcode"`
	City      string `json:"city"`
	State     string `json:"state"`
	Birthday  string `json:"birthday"`
	Country   string `json:"country"` // must be in ISO 3166-1 alpha-2 format

	Tag string `json:"userTag"`
}

type UserUpdateDTO struct {
	Email    string `json:"email"`
	Address1 string `json:"address1"`
	Mobile   string `json:"mobile"` // must be in E.164 format
	Phone    string `json:"phone"`  // must be in E.164 format

	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
	Address2  string `json:"address2"`
	PostCode  string `json:"postcode"`
	City      string `json:"city"`
	State     string `json:"state"` // corresponds to the City in Accounts
	Birthday  string `json:"birthday"`
	Country   string `json:"country"` // must be in ISO 3166-1 alpha-2 format

	Tag string `json:"userTag"`
}

type DeclarativeInformationDTO struct {
	Title             string `bson:"title"             json:"title"             validate:"required,oneof=M MELLE MME"`
	PlaceOfBirth      string `bson:"placeOfBirth"      json:"placeOfBirth"      validate:"required,min=2,max=255"`
	BirthCountry      string `bson:"birthCountry"      json:"birthCountry"      validate:"required,iso3166_1_alpha2"`
	Nationality       string `bson:"nationality"       json:"nationality"       validate:"required,iso3166_1_alpha2"`
	SpecifiedUSPerson int    `bson:"specifiedUSPerson" json:"specifiedUSPerson" validate:"oneof=0 1"`
	FrenchTaxResident int    `bson:"frenchTaxResident" json:"frenchTaxResident" validate:"oneof=0 1"` // will be converted to `taxResidence` field for Treezor
	IncomeRange       string `bson:"incomeRange"       json:"incomeRange"       validate:"required,oneof=0-18 19-23 24-27 28-35 36-56 57-*"`
	PersonalAssets    string `bson:"personalAssets"    json:"personalAssets"    validate:"required,oneof=0-2 3-22 23-*********** ***********-"`

	Occupation string `bson:"occupation" json:"occupation"`
}
