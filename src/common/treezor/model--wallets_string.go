// Code generated by "stringer -type=WalletType,WalletStatus -output=model--wallets_string.go"; DO NOT EDIT.

package treezor

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[WalletTypeElectronicMoneyWallet-9]
	_ = x[WalletTypePaymentAccountWallet-10]
	_ = x[WalletTypeMirrorWallet-13]
	_ = x[WalletTypeElectronicMoneyCard-14]
}

const (
	_WalletType_name_0 = "WalletTypeElectronicMoneyWalletWalletTypePaymentAccountWallet"
	_WalletType_name_1 = "WalletTypeMirrorWalletWalletTypeElectronicMoneyCard"
)

var (
	_WalletType_index_0 = [...]uint8{0, 31, 61}
	_WalletType_index_1 = [...]uint8{0, 22, 51}
)

func (i WalletType) String() string {
	switch {
	case 9 <= i && i <= 10:
		i -= 9
		return _WalletType_name_0[_WalletType_index_0[i]:_WalletType_index_0[i+1]]
	case 13 <= i && i <= 14:
		i -= 13
		return _WalletType_name_1[_WalletType_index_1[i]:_WalletType_index_1[i+1]]
	default:
		return "WalletType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[WalletStatusPending-1]
	_ = x[WalletStatusActive-2]
	_ = x[WalletStatusCancelled-3]
}

const _WalletStatus_name = "WalletStatusPendingWalletStatusActiveWalletStatusCancelled"

var _WalletStatus_index = [...]uint8{0, 19, 37, 58}

func (i WalletStatus) String() string {
	i -= 1
	if i < 0 || i >= WalletStatus(len(_WalletStatus_index)-1) {
		return "WalletStatus(" + strconv.FormatInt(int64(i+1), 10) + ")"
	}
	return _WalletStatus_name[_WalletStatus_index[i]:_WalletStatus_index[i+1]]
}
