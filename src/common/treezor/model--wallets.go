// Requires:
//  $ go install golang.org/x/tools/cmd/stringer@latest
//
//go:generate stringer -type=WalletType,WalletStatus -output=model--wallets_string.go

package treezor

import (
	"encoding/json"
	"time"
	"yochbee/_base/dbnosql"
	json2 "yochbee/_base/json"

	"github.com/tidwall/sjson"
)

type WalletType int

const (
	WalletTypeElectronicMoneyWallet WalletType = 9
	WalletTypePaymentAccountWallet  WalletType = 10
	WalletTypeMirrorWallet          WalletType = 13
	WalletTypeElectronicMoneyCard   WalletType = 14
)

// WalletStatus is computed value that is sent only to the mobile
type WalletStatus int8

const (
	WalletStatusPending WalletStatus = iota + 1
	WalletStatusActive
	WalletStatusCancelled
)

type Wallet struct {
	ID string `bson:"_id"           json:"id"`

	TreezorWalletId int64  `bson:"treezorWalletId"  json:"walletId"`
	TreezorUserId   int64  `bson:"treezorUserId"    json:"userId"`
	Tag             string `bson:"-"                json:"walletTag"`

	Type              WalletType `bson:"type"              json:"walletTypeId"` // typically 10 = Payment Account wallet, 14 = Electronic Money Card
	Name              string     `bson:"name"              json:"eventName"`
	Status            string     `bson:"status"            json:"walletStatus"`
	Balance           float64    `bson:"balance"           json:"balance"`
	AuthorizedBalance float64    `bson:"authorizedBalance" json:"authorizedBalance"`

	TariffId int64 `bson:"tariffId" json:"tariffId"`

	Bic      string `bson:"bic" json:"bic"`
	Iban     string `bson:"iban" json:"iban"`
	Currency string `bson:"currency" json:"currency"` // ISO 4217 format, see https://en.wikipedia.org/wiki/ISO_4217

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"` // used manually to find the latest wallet snapshot
}

func (w *Wallet) GetTreezorID() (string, interface{}) {
	return "treezorWalletId", w.TreezorWalletId
}

func (w *Wallet) TreezorTimestamp() int64 {
	return w.WebhookTimestamp
}

func (w *Wallet) SetTreezorTimestamp(ts int64) {
	w.WebhookTimestamp = ts
}

func (w *Wallet) SetInsertID(id string) {
	w.ID = id
	w.CreatedAt = time.Now()
}

func (w *Wallet) UpdateActualFromIncoming(incoming ActualUpdater) {
	in := incoming.(*Wallet)

	now := time.Now()
	w.UpdatedAt = &now
	w.WebhookTimestamp = in.WebhookTimestamp

	w.Type = in.Type
	w.Name = in.Name
	w.Status = in.Status
	w.Balance = in.Balance
	w.AuthorizedBalance = in.AuthorizedBalance

	w.TariffId = in.TariffId

	w.Bic = in.Bic
	w.Iban = in.Iban
	w.Currency = in.Currency
}

func (w *Wallet) UpdateFromBalance(balance *Balance) {
	w.Balance = balance.CurrentBalance
	w.AuthorizedBalance = balance.AuthorizedBalance
	w.WebhookTimestamp = balance.WebhookTimestamp
	w.UpdatedAt = dbnosql.PNow()
}

func (w *Wallet) ToMobileJSON() ([]byte, error) {
	p, err := json2.MarshalRemoveFields(w, "id", "walletId", "userId", "walletTag", "walletTypeId", "eventName", "walletStatus", "tariffId", "createdAt", "updatedAt", "treezorWebhookTimestamp")
	if err != nil {
		return nil, err
	}

	status := WalletStatusPending
	if w.Status == "VALIDATED" {
		status = WalletStatusActive
	} else if w.Status == "CANCELLED" {
		status = WalletStatusCancelled
	}

	p, _ = sjson.SetBytes(p, "treezorWalletId", w.TreezorWalletId)
	p, _ = sjson.SetBytes(p, "status", status)
	p, _ = sjson.SetBytes(p, "statusStr", status.String())
	p, _ = sjson.SetBytes(p, "name", w.Name)
	return p, nil
}

func UnwrapWalletsFromTreezorResponse(msg []byte) ([]*Wallet, error) {
	msg, err := PatchBadStringFieldsThatShouldBeNumbers(msg, "wallets", []string{"walletId", "userId", "walletTypeId", "balance", "authorizedBalance", "tariffId"})
	if err != nil {
		return nil, err
	}

	payload := struct {
		Wallets []*Wallet `json:"wallets"`
	}{}
	if err := json.Unmarshal(msg, &payload); err != nil {
		return nil, err
	}
	return payload.Wallets, nil
}

type WalletCreateDTO struct {
	Type WalletType `bson:"type" json:"walletTypeId"` // typically 10 = Payment Account wallet
	Name string     `bson:"name" json:"eventName"`

	OwnerUserId int64 `bson:"ownerUserId" json:"userId"`

	TariffId int64 `bson:"tariffId" json:"tariffId"`

	Currency string `bson:"currency" json:"currency"` // ISO 4217 format, see https://en.wikipedia.org/wiki/ISO_4217

	Tag string `json:"walletTag"`
}
