// Code generated by "stringer -type=DocumentType,DocumentStatus,DocumentSummarizedStatus -output=model--documents_string.go"; DO NOT EDIT.

package kyc

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[DT_IdentityCard-9]
	_ = x[DT_DrivingLicence-16]
	_ = x[DT_Passport-17]
	_ = x[DT_ProofOfAddress-12]
	_ = x[DT_ResidencePermit-15]
	_ = x[DT_TaxStatement-24]
	_ = x[DT_TaxExemptionStatement-25]
}

const (
	_DocumentType_name_0 = "DT_IdentityCard"
	_DocumentType_name_1 = "DT_ProofOfAddress"
	_DocumentType_name_2 = "DT_ResidencePermitDT_DrivingLicenceDT_Passport"
	_DocumentType_name_3 = "DT_TaxStatementDT_TaxExemptionStatement"
)

var (
	_DocumentType_index_2 = [...]uint8{0, 18, 35, 46}
	_DocumentType_index_3 = [...]uint8{0, 15, 39}
)

func (i DocumentType) String() string {
	switch {
	case i == 9:
		return _DocumentType_name_0
	case i == 12:
		return _DocumentType_name_1
	case 15 <= i && i <= 17:
		i -= 15
		return _DocumentType_name_2[_DocumentType_index_2[i]:_DocumentType_index_2[i+1]]
	case 24 <= i && i <= 25:
		i -= 24
		return _DocumentType_name_3[_DocumentType_index_3[i]:_DocumentType_index_3[i+1]]
	default:
		return "DocumentType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[DSY_Uploaded-0]
	_ = x[DSY_UnderReview-1]
	_ = x[DSY_Validated-2]
	_ = x[DSY_Rejected-3]
	_ = x[DSY_SentToTreezor-4]
}

const _DocumentStatus_name = "DSY_UploadedDSY_UnderReviewDSY_ValidatedDSY_RejectedDSY_SentToTreezor"

var _DocumentStatus_index = [...]uint8{0, 12, 27, 40, 52, 69}

func (i DocumentStatus) String() string {
	if i < 0 || i >= DocumentStatus(len(_DocumentStatus_index)-1) {
		return "DocumentStatus(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _DocumentStatus_name[_DocumentStatus_index[i]:_DocumentStatus_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[DSS_Pending-1]
	_ = x[DSS_Canceled-2]
	_ = x[DSS_Validated-3]
	_ = x[DSS_Refused - -1]
}

const (
	_DocumentSummarizedStatus_name_0 = "DSS_Refused"
	_DocumentSummarizedStatus_name_1 = "DSS_PendingDSS_CanceledDSS_Validated"
)

var (
	_DocumentSummarizedStatus_index_1 = [...]uint8{0, 11, 23, 36}
)

func (i DocumentSummarizedStatus) String() string {
	switch {
	case i == -1:
		return _DocumentSummarizedStatus_name_0
	case 1 <= i && i <= 3:
		i -= 1
		return _DocumentSummarizedStatus_name_1[_DocumentSummarizedStatus_index_1[i]:_DocumentSummarizedStatus_index_1[i+1]]
	default:
		return "DocumentSummarizedStatus(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
