package kyc

import "yochbee/common/accounts"

// =============

const Method_CreateProgressForAccount = "kyc.CreateProgressForAccount"

type CreateProgressForAccountRequest struct {
	Account *accounts.Account `json:"account"`
}

type CreateProgressForAccountResponse struct {
	Progress *Progress `json:"progress"`
}

// =============

const Method_UpdateProgressForAccount = "kyc.UpdateProgressForAccount"

type UpdateProgressForAccountRequest struct {
	Account *accounts.Account `json:"account"`
}

type UpdateProgressForAccountResponse struct {
	Progress *Progress `json:"progress"`
}

// =============

const Method_YochbeeTeamReviewCompleted = "kyc.YochbeeTeamReviewCompleted"

type YochbeeTeamReviewCompletedRequest struct {
	AccountId            string   `json:"accountId"`
	OKForTreezor         bool     `json:"okForTreezor"`
	ValidatedDocumentIDs []string `json:"validatedDocumentIDs"`
	RejectedDocumentIDs  []string `json:"rejectedDocumentIDs"`
	Remarks              string   `json:"remarks"`
}

// =============

const Method_UpdateDeclarativeInfoReadiness = "kyc.UpdateDeclarativeInfoReadiness"

type UpdateDeclarativeInfoReadinessRequest struct {
	OldAccount     *accounts.Account `json:"oldAccount"`
	UpdatedAccount *accounts.Account `json:"updatedAccount"`
	IsReady        bool              `json:"isReady"`
}

type UpdateDeclarativeInfoReadinessResponse struct {
	OK bool `json:"ok"`
}

// =============

const Method_GetProgressByAccountId = "kyc.GetProgressByAccountId"

type GetProgressByAccountIdRequest struct {
	Account *accounts.Account `json:"account"`
}

type GetProgressByAccountIdResponse struct {
	Progress *Progress `json:"progress"`
}
