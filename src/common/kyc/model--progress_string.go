// Code generated by "stringer -type=ProgressStatus,<PERSON>ycL<PERSON><PERSON>,KycReview -output=model--progress_string.go"; DO NOT EDIT.

package kyc

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[PS_RequireIdentityDocument-1]
	_ = x[PS_RequireAddressProofDocument-2]
	_ = x[PS_ReviewCanStart-3]
	_ = x[PS_DocumentsUnderReview-10]
	_ = x[PS_RequiresInitialPayment-19]
	_ = x[PS_Validated-20]
	_ = x[PS_PermanentlyRefused - -1]
}

const (
	_ProgressStatus_name_0 = "PS_PermanentlyRefused"
	_ProgressStatus_name_1 = "PS_RequireIdentityDocumentPS_RequireAddressProofDocumentPS_ReviewCanStart"
	_ProgressStatus_name_2 = "PS_DocumentsUnderReview"
	_ProgressStatus_name_3 = "PS_RequiresInitialPaymentPS_Validated"
)

var (
	_ProgressStatus_index_1 = [...]uint8{0, 26, 56, 73}
	_ProgressStatus_index_3 = [...]uint8{0, 25, 37}
)

func (i ProgressStatus) String() string {
	switch {
	case i == -1:
		return _ProgressStatus_name_0
	case 1 <= i && i <= 3:
		i -= 1
		return _ProgressStatus_name_1[_ProgressStatus_index_1[i]:_ProgressStatus_index_1[i+1]]
	case i == 10:
		return _ProgressStatus_name_2
	case 19 <= i && i <= 20:
		i -= 19
		return _ProgressStatus_name_3[_ProgressStatus_index_3[i]:_ProgressStatus_index_3[i+1]]
	default:
		return "ProgressStatus(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[KL_None-0]
	_ = x[KL_Light-1]
	_ = x[KL_Regular-2]
	_ = x[KL_Strong-3]
	_ = x[KL_Refused-4]
}

const _KycLevel_name = "KL_NoneKL_LightKL_RegularKL_StrongKL_Refused"

var _KycLevel_index = [...]uint8{0, 7, 15, 25, 34, 44}

func (i KycLevel) String() string {
	if i >= KycLevel(len(_KycLevel_index)-1) {
		return "KycLevel(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _KycLevel_name[_KycLevel_index[i]:_KycLevel_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[KR_Investigating-5]
	_ = x[KR_None-0]
	_ = x[KR_Pending-1]
	_ = x[KR_Validated-2]
	_ = x[KR_Refused-3]
}

const (
	_KycReview_name_0 = "KR_NoneKR_PendingKR_ValidatedKR_Refused"
	_KycReview_name_1 = "KR_Investigating"
)

var (
	_KycReview_index_0 = [...]uint8{0, 7, 17, 29, 39}
)

func (i KycReview) String() string {
	switch {
	case i <= 3:
		return _KycReview_name_0[_KycReview_index_0[i]:_KycReview_index_0[i+1]]
	case i == 5:
		return _KycReview_name_1
	default:
		return "KycReview(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
