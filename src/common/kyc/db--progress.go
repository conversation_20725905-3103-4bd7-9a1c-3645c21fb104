package kyc

import (
	"fmt"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/_base/dbnosql"
	"yochbee/_base/lock"
	accounts "yochbee/common/accounts"

	"github.com/eliezedeck/gobase/logging"
	"github.com/go-redsync/redsync/v4"
	"go.uber.org/zap"
)

const (
	progressCollection = "kyc-progress"
)

type ProgressCollection struct {
	dbnosql.DataCollection
}

func (c *ProgressCollection) GetByTreezorUserId(treezorUserId int64) (*Progress, error) {
	return dbnosql.GetOneByField[*Progress](c, "treezorUserId", treezorUserId)
}

func (c *ProgressCollection) Update(progress *Progress) error {
	if progress.ID == "" {
		return dbnosql.ErrMissingID
	}
	return c.UpdateById(progress.ID, progress)
}

func (c *ProgressCollection) DLockKeyByAccountId(accountId string) string {
	return fmt.Sprintf("kyc-progress-by-accountId-%s", accountId)
}

// GetDLockMutexByAccountId acquires a lock that matches the Progress object by the given accountId. It is not a DB
// lock, but a distributed lock. All writes to the Progress object must be done while holding this lock, or within 10
// seconds of acquiring it.
// It then returns a function that must be called to release the lock, otherwise, it will be released after 10 seconds.
//
// Why using a key that is associated with the accountId instead of the Progress ID? Because the Progress object is
// generally queried by the accountId, and we want to avoid having to query the DB to get the Progress ID. There are
// also places where we don't want to change how the Progress object is retrieved, but we know for sure that it will
// match the accountId, so we can use this lock.
func (c *ProgressCollection) GetDLockMutexByAccountId(dlocker *lock.DistributedLocker, accountId string) (unlockFunc func(), err error) {
	key := c.DLockKeyByAccountId(accountId)
	mutex := dlocker.GetMutex(key, redsync.WithExpiry(10*time.Second))
	if err := mutex.Lock(); err != nil {
		return nil, err
	}
	unlockFunc = func() {
		_, err := mutex.Unlock()
		if err != nil {
			logging.L.Error("Cannot unlock mutex (ignored)", zap.Error(err), zap.String("key", key))
		}
	}
	return unlockFunc, nil
}

func (c *ProgressCollection) GetByTreezorWalletId(busrpc bus2.RpcClient, treezorWalletId int64) (*Progress, error) {
	// Get the Account that matches the given Treezor wallet ID (via the bus)
	result, err := bus2.CallMethodMU[accounts.GetByTreezorPrimaryWalletIdResponse](busrpc, accounts.Method_GetByTreezorPrimaryWalletId, &accounts.GetByTreezorPrimaryWalletIdRequest{
		TreezorPrimaryWalletId: treezorWalletId,
	}, 5*time.Second)
	if err != nil {
		return nil, err
	}
	if result.Account == nil {
		return nil, nil
	}

	// Now, get the progress of this account
	return c.GetByTreezorUserId(result.Account.TreezorUserId)
}

func SetupProgressCollection(db dbnosql.Database) (*ProgressCollection, error) {
	progressColl := db.DeclareCollection(progressCollection, func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &Progress{}
	})

	if err := progressColl.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "accountId", Order: dbnosql.OrderASC},
		},
		Name: "accountId",
	}); err != nil {
		return nil, err
	}

	return &ProgressCollection{progressColl}, nil
}
