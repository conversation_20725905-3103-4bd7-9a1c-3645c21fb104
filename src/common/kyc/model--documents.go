// Requires:
//  $ go install golang.org/x/tools/cmd/stringer@latest
//
//go:generate stringer -type=DocumentType,DocumentStatus,DocumentSummarizedStatus -output=model--documents_string.go

package kyc

import (
	"time"
	"yochbee/_base/json"
)

type DocumentType uint8

const (
	DT_IdentityCard   DocumentType = 9
	DT_DrivingLicence DocumentType = 16
	DT_Passport       DocumentType = 17

	DT_ProofOfAddress  DocumentType = 12
	DT_ResidencePermit DocumentType = 15

	DT_TaxStatement          DocumentType = 24
	DT_TaxExemptionStatement DocumentType = 25
)

type DocumentSummarizedStatus int

const (
	DSS_Pending   DocumentSummarizedStatus = 1
	DSS_Canceled  DocumentSummarizedStatus = 2
	DSS_Validated DocumentSummarizedStatus = 3
	DSS_Refused   DocumentSummarizedStatus = -1
)

func GetDocumentSummarizedStatusFromCode(code string) DocumentSummarizedStatus {
	switch code {
	case "600001", "600003", "600002":
		return DSS_Pending
	case "600004", "600005", "600006", "600007":
		return DSS_Canceled
	case "600009":
		return DSS_Validated
	default:
		return DSS_Refused
	}
}

func IsSupportedDocumentType(t uint8) bool {
	switch t {
	case 9, 12, 15, 16, 17:
		return true
	default:
		return false
	}
}

type DocumentStatus int8

// These are document status while still on Yochbee's hand
const (
	DSY_Uploaded      DocumentStatus = 0
	DSY_UnderReview   DocumentStatus = 1
	DSY_Validated     DocumentStatus = 2
	DSY_Rejected      DocumentStatus = 3
	DSY_SentToTreezor DocumentStatus = 4
)

// Document is an entry in the `kyc-documents` database collection.
type Document struct {
	ID                string         `bson:"_id"               json:"id"`
	AccountId         string         `bson:"accountId"         json:"accountId"`
	TreezorUserId     int64          `bson:"treezorUserId"     json:"treezorUserId"`     // XXXXXX
	TreezorDocumentId int64          `bson:"treezorDocumentId" json:"treezorDocumentId"` // XXXXXX
	TreezorStatusCode string         `bson:"treezorStatusCode" json:"treezorStatusCode"` // XXXXXX
	TreezorStatus     string         `bson:"treezorStatus"     json:"treezorStatus"`     // XXXXXX
	Type              DocumentType   `bson:"type"              json:"type"`
	TypeText          string         `bson:"typeText"          json:"typeText"`
	Filename          string         `bson:"filename"          json:"filename"`
	OriginalFilename  string         `bson:"originalFilename"  json:"originalFilename"` // XXXXXX
	Size              int64          `bson:"size"              json:"size"`
	SecretUrl         string         `bson:"secretUrl"         json:"secretUrl"`
	Status            DocumentStatus `bson:"status"            json:"status"`
	StatusText        string         `bson:"statusText"        json:"statusText"`
	Remarks           string         `bson:"remarks"           json:"remarks"`
	CreatedAt         time.Time      `bson:"createdAt"         json:"createdAt"`
	UpdatedAt         *time.Time     `bson:"updatedAt"         json:"updatedAt"`
}

func (k *Document) ToMobileJSON() ([]byte, error) {
	if k == nil {
		return []byte("null"), nil
	}
	return json.MarshalRemoveFields(k, "treezorUserId", "treezorDocumentId", "treezorStatusCode", "treezorStatus", "originalFilename")
}

func (k *Document) IsAcceptable() bool {
	dss := GetDocumentSummarizedStatusFromCode(k.TreezorStatusCode)
	return (dss == DSS_Pending || dss == DSS_Validated) && k.Status != DSY_Rejected
}

func (k *Document) IsIdentityDocument() bool {
	switch k.Type {
	case DT_IdentityCard, DT_DrivingLicence, DT_Passport:
		return true
	default:
		return false
	}
}

func (k *Document) IsAddressProofDocument() bool {
	switch k.Type {
	case DT_ProofOfAddress, DT_ResidencePermit:
		return true
	default:
		return false
	}
}
