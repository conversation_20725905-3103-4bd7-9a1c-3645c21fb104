// Requires:
//  $ go install golang.org/x/tools/cmd/stringer@latest
//
//go:generate stringer -type=ProgressStatus,KycLevel,KycReview -output=model--progress_string.go

package kyc

import (
	"encoding/json"
	"time"
)

// ProgressStatus is a dynamically computed value that is returned by API(s)
type ProgressStatus int8

const (
	PS_RequireIdentityDocument     ProgressStatus = 1
	PS_RequireAddressProofDocument ProgressStatus = 2 // skipped if Liveness
	PS_ReviewCanStart              ProgressStatus = 3
	PS_DocumentsUnderReview        ProgressStatus = 10
	PS_RequiresInitialPayment      ProgressStatus = 19
	PS_Validated                   ProgressStatus = 20
	PS_PermanentlyRefused          ProgressStatus = -1
)

// KycLevel is the User's level (not document's)
// https://docs.treezor.com/guide/users/kyc.html#user-s-kyc-levels-kyclevel
type KycLevel uint8

const (
	KL_None    KycLevel = 0
	KL_Light   KycLevel = 1
	KL_Regular KycLevel = 2
	KL_Strong  KycLevel = 3
	KL_Refused KycLevel = 4 // no more reviews accepted
)

// KycReview is the User's level (not document's)
// https://docs.treezor.com/guide/users/kyc.html#user-s-kyc-review-status-kycreview
type KycReview uint8

const (
	KR_Investigating KycReview = 5
	KR_None          KycReview = 0
	KR_Pending       KycReview = 1
	KR_Validated     KycReview = 2
	KR_Refused       KycReview = 3
)

// Progress is an entry in the `kyc-progress` database collection.
type Progress struct {
	ID                       string    `bson:"_id" json:"id"`
	AccountId                string    `bson:"accountId" json:"accountId"`
	TreezorUserId            int64     `bson:"treezorUserId" json:"treezorUserId"`
	TreezorKYCLevel          KycLevel  `bson:"treezorKYCLevel" json:"treezorKYCLevel"`
	TreezorKYCReview         KycReview `bson:"treezorKYCReview" json:"treezorKYCReview"`
	TreezorKYCReviewComment  string    `bson:"treezorKYCReviewComment" json:"treezorKYCReviewComment"`
	TreezorKYCLivenessId     string    `bson:"treezorLivenessId" json:"treezorLivenessId"` // ID in the `treezor-liveness` collection
	TreezorKYCLivenessUrl    string    `bson:"treezorKYCLivenessUrl" json:"treezorKYCLivenessUrl"`
	TreezorKYCLivenessStatus string    `bson:"treezorLivenessStatus" json:"treezorLivenessStatus"`
	TreezorKYCLivenessScore  int       `bson:"treezorLivenessScore" json:"treezorLivenessScore"`

	// IsInitialPaymentReceived is true if the user has paid the initial amount
	IsInitialPaymentReceived    bool    `bson:"isInitialPaymentReceived" json:"isInitialPaymentReceived"`
	TotalInitialPaymentReceived float64 `bson:"totalInitialPaymentReceived" json:"totalInitialPaymentReceived"`

	// IsReviewRequested is true if the user has requested a review, and that the URL for Liveness has been returned to Mobile
	IsReviewRequested bool `bson:"isReviewRequested" json:"isReviewRequested"`

	LastKYCLivenessWebhookTimestamp int64 `bson:"lastKYCLivenessWebhookTimestamp" json:"lastKYCLivenessWebhookTimestamp"`
	LastKYCLivenessScore            int   `bson:"lastKYCLivenessScore"            json:"lastKYCLivenessScore"`

	// IsTreezorReviewStarted is when Treezor's actual KYC review has been requested (non-KYC Liveness)
	IsTreezorReviewStarted bool `bson:"isTreezorReviewStarted" json:"isTreezorReviewStarted"`

	// IsDocumentsSentToTreezor is true if the `document.create` webhooks have been received for this user. This has
	// been the result of the `PUT /v1/users/{id}/kycliveness` API call, which also sends the documents to Treezor.
	// Without this, Treezor will not start the KYC review as there are no documents on their hands.
	IsDocumentsSentToTreezor bool `bson:"isDocumentsSentToTreezor" json:"isDocumentsSentToTreezor"`

	HasRequiredDeclarativeKYCInfo bool       `bson:"hasRequiredDeclarativeKYCInfo" json:"hasRequiredDeclarativeKYCInfo"`
	CreatedAt                     time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt                     *time.Time `bson:"updatedAt" json:"updatedAt"`
}

func (p *Progress) IsKYCTreezorValidated() bool {
	return p.TreezorKYCLevel == KL_Regular && p.TreezorKYCReview == KR_Validated
}

func (p *Progress) IsKYCInProgress() bool {
	// Review has been requested by the user, and we have a Liveness URL
	if p.IsReviewRequested && p.TreezorKYCLivenessUrl != "" {
		return true
	}

	switch p.TreezorKYCReview {
	case KR_Investigating, KR_Pending:
		return true
	default:
		return false
	}
}

func (p *Progress) IsKYCPermanentlyRefused() bool {
	return p.TreezorKYCLevel == KL_Refused
}

// Downgrade needs to be executed after:
// - KYC Liveness has been requested by the user, the user already provided all required declarative information
// - Liveness has been unsuccessful (aborted, expired, negative result from Ubble)
//
// The Progress is thus downgraded because it cannot go further. Should the user want to continue, he'll need to request
// again a KYC Liveness to acquire the documents.
//
// This only updates the in-memory object, it's up to the caller to persist into the DB.
func (p *Progress) Downgrade() {
	now := time.Now()
	p.UpdatedAt = &now
	p.IsReviewRequested = false
	p.IsTreezorReviewStarted = false
	p.IsDocumentsSentToTreezor = false

	p.TreezorKYCLivenessId = ""
	p.TreezorKYCLivenessUrl = ""
	p.TreezorKYCLivenessStatus = ""
	p.TreezorKYCLivenessScore = -1

	p.LastKYCLivenessWebhookTimestamp = 0
	p.LastKYCLivenessScore = 0
}

type ComputedStatus struct {
	Status               ProgressStatus `json:"-"`
	StatusStr            string         `json:"status"`
	StatusCode           int8           `json:"statusCode"`
	IsValidated          bool           `json:"isValidated"`
	IdentityDocument     *Document      `json:"identityDocument"`
	AddressProofDocument *Document      `json:"addressProofDocument"`
	ProgressObject       *Progress      `json:"progressObject"`
}

// ComputeProgressStatusWithLiveness is similar to ComputeProgressStatus, but is used when the KYC Liveness is used
// instead of us acquiring the documents from the users via our app.
func (p *Progress) ComputeProgressStatusWithLiveness() (*ComputedStatus, error) {
	progressStatus := PS_RequireIdentityDocument // this is the default status, needs an identity document

	// Calculate the correct status of the progress at this time, based on what's happening
	if p.TreezorKYCLevel == KL_Refused {
		progressStatus = PS_PermanentlyRefused
	} else if p.TreezorKYCReview == KR_Investigating || p.TreezorKYCReview == KR_Pending {
		// Whether it is under review by the Yochbee team, or by the Treezor team, it's under review
		progressStatus = PS_DocumentsUnderReview
	} else if p.TreezorKYCReview == KR_None && p.TreezorKYCLevel == KL_None {
		// Treezor review hasn't happened, or completed yet
		if p.LastKYCLivenessScore > 0 {
			// Last Ubble KYC Liveness was successful
			if p.IsTreezorReviewStarted {
				progressStatus = PS_DocumentsUnderReview
			} else {
				progressStatus = PS_RequiresInitialPayment
			}
		} else {
			// As long as it hasn't been started or successful, we consider requirements of the documents
			progressStatus = PS_RequireIdentityDocument

			// Now, if the IsReviewRequested is true, it means that the user has requested a review, but the Liveness
			// has not been completed yet.
		}
	} else if p.TreezorKYCReview == KR_Validated && p.TreezorKYCLevel == KL_Regular {
		// Validated
		if !p.HasCompletedKYCAndInitialPayment() {
			progressStatus = PS_RequiresInitialPayment
		} else {
			progressStatus = PS_Validated
		}
	}

	return &ComputedStatus{
		Status:         progressStatus,
		StatusStr:      progressStatus.String(),
		StatusCode:     int8(progressStatus),
		IsValidated:    p.IsKYCTreezorValidated(),
		ProgressObject: p,
	}, nil
}

func (p *Progress) HasCompletedKYCAndInitialPayment() bool {
	return p.TreezorKYCLevel == KL_Regular && p.TreezorKYCReview == KR_Validated && p.IsInitialPaymentReceived
}

// ComputeProgressStatus computes the progress status
// NOTE: Only use this if we upload documents to our own server, not via Liveness
func (p *Progress) ComputeProgressStatus(allUserDocuments []*Document) (*ComputedStatus, error) {
	identityDocuments := make([]*Document, 0, 2)
	addressProofDocuments := make([]*Document, 0, 2)
	progressStatus := PS_RequireIdentityDocument // this is the default status, needs an identity document
	for _, d := range allUserDocuments {
		if !d.IsAcceptable() {
			continue
		}
		if d.IsIdentityDocument() {
			identityDocuments = append(identityDocuments, d)
		} else if d.IsAddressProofDocument() {
			addressProofDocuments = append(addressProofDocuments, d)
		}
	}

	// Already validated
	if p.TreezorKYCLevel == KL_Regular && p.TreezorKYCReview == KR_Validated {
		progressStatus = PS_Validated
		if !p.IsInitialPaymentReceived {
			progressStatus = PS_RequiresInitialPayment
		}
	} else if p.TreezorKYCLevel == KL_Refused {
		// Calculate the correct status of the progress at this time, based on what's happening
		progressStatus = PS_PermanentlyRefused
	} else if p.TreezorKYCReview == KR_Investigating || p.TreezorKYCReview == KR_Pending {
		// Whether it is under review by the Yochbee team, or by the Treezor team, it's under review
		progressStatus = PS_DocumentsUnderReview
	} else {
		if len(identityDocuments) == 0 {
			progressStatus = PS_RequireIdentityDocument
		} else if len(addressProofDocuments) == 0 {
			progressStatus = PS_RequireAddressProofDocument
		} else if p.TreezorKYCReview == KR_None {
			progressStatus = PS_ReviewCanStart
		}
	}

	return &ComputedStatus{
		Status:               progressStatus,
		StatusStr:            progressStatus.String(),
		StatusCode:           int8(progressStatus),
		IsValidated:          p.IsKYCTreezorValidated(),
		IdentityDocument:     identityDocuments[:1][0],     // this works because we have the capacity
		AddressProofDocument: addressProofDocuments[:1][0], // this works because we have the capacity
		ProgressObject:       p,
	}, nil
}

func (c *ComputedStatus) ToMobileJSON() ([]byte, error) {
	// FIXME: this requires a new marshaller for a recursive and generic ToMobileJSON(), mush like the Marshaller interface
	return json.Marshal(c)
}
