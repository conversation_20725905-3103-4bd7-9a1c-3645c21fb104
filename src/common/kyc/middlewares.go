package kyc

import (
	"errors"
	"time"
	bus2 "yochbee/_base/bus"
	"yochbee/common/accounts"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

// MiddlewareRetrieveProgressByAccountId retrieves the progress for the account, and it requires the "account" object
// to be set in the context.
func MiddlewareRetrieveProgressByAccountId(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		account, ok := c.Get("account").(*accounts.Account)
		if !ok {
			return errors.New("account not found in context")
		}
		busrpc, ok := c.Get("busrpc").(bus2.RpcClient)
		if !ok {
			return errors.New("busrpc not found in context")
		}

		// Retrieval is through the bus
		result, err := bus2.CallMethodMU[GetProgressByAccountIdResponse](busrpc, Method_GetProgressByAccountId, &GetProgressByAccountIdRequest{
			Account: account,
		}, 5*time.Second)
		if err != nil {
			return err // HTTP 500
		}
		if result.Progress == nil {
			// Didn't exist
			logging.L.Error("Progress not found for account", zap.Any("account", account))
			return errors.New("progress not found")
		}

		// Set in the context
		c.Set("progress", result.Progress)

		return next(c)
	}
}
