// Requires:
//  $ go install golang.org/x/tools/cmd/stringer@latest
//
//go:generate go run golang.org/x/tools/cmd/stringer -type=TransactionType,TransactionStatus -output=model--transactions_string.go

package banking

import (
	"sort"
	"time"
	json2 "yochbee/_base/json"
)

type TransactionType int8

const (
	TransactionTypePayin           TransactionType = iota + 1 // Load wallet with a Captured Card
	TransactionTypePayinRefund                                // Refund a Payin
	TransactionTypePayout                                     // From Wallet: Withdrawal to a bank account
	TransactionTypePayoutRefund                               // Refund a Payout
	TransactionTypeCardDebit                                  // Purchase made on a Treezor card
	TransactionTypeCardCredit                                 // Refund made back to a Treezor card
	TransactionTypeCardTranscation                            // Card Transaction
)

type TransactionStatus int8

const (
	TransactionStatusPending TransactionStatus = iota + 1
	TransactionStatusCanceled
	TransactionStatusFailed
	TransactionStatusSuccess
	TransactionStatusRefunded
	TransactionStatusAuthorized
	TransactionStatusSettled
	TransactionStatusDeclined
)

type Transaction struct {
	ID                    string `bson:"_id" json:"id"`
	TreezorId             int64  `bson:"treezorId"              json:"treezorId"`
	TreezorWalletDebitId  int64  `bson:"treezorWalletDebitId"   json:"treezorWalletDebitId"`
	TreezorWalletCreditId int64  `bson:"treezorWalletCreditId"  json:"treezorWalletCreditId"`

	TransactionType      TransactionType   `bson:"transactionType"        json:"transactionType"`
	TransactionTypeStr   string            `bson:"transactionTypeStr"     json:"transactionTypeStr"`
	TransactionStatus    TransactionStatus `bson:"transactionStatus"      json:"transactionStatus"`
	TransactionStatusStr string            `bson:"transactionStatusStr"   json:"transactionStatusStr"`

	// TreezorPayinId can be a string (UUID) or an int64 (possibly float64 from unmarshalling)
	TreezorPayinId               interface{} `bson:"treezorPayinId"                json:"treezorPayinId"`
	TreezorPayinMethodID         int         `bson:"treezorPayinMethodId"          json:"treezorPayinMethodId"`
	TreezorPayinWebhookTimestamp int64       `bson:"treezorPayinWebhookTimestamp"  json:"treezorPayinWebhookTimestamp"`

	TreezorPayoutId               interface{} `bson:"treezorPayoutId"                json:"treezorPayoutId"`
	TreezorPayoutTypeID           int         `bson:"treezorPayoutMethodId"          json:"treezorPayoutMethodId"`
	TreezorPayoutWebhookTimestamp int64       `bson:"treezorPayoutWebhookTimestamp"  json:"treezorPayoutWebhookTimestamp"`

	TreezorCardTransactionId               int64 `bson:"treezorCardTransactionId" json:"treezorCardTransactionId"`
	TreezorCardTransactionWebhookTimestamp int64 `bson:"treezorCardTransactionWebhookTimestamp" json:"treezorCardTransactionWebhookTimestamp"`

	Name          string  `bson:"name"           json:"name"`
	Description   string  `bson:"description"    json:"description"`
	ValueDate     string  `bson:"valueDate"      json:"valueDate"`
	ExecutionDate string  `bson:"executionDate"  json:"executionDate"`
	Amount        float64 `bson:"amount"         json:"amount"`
	Currency      string  `bson:"currency"       json:"currency"`
	PostBalance   string  `bson:"postBalance"    json:"postBalance"`

	// CreatedAt will match the createdDate from Treezor (if it's given, in general it will be)
	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"webhookTimestamp" json:"webhookTimestamp"`
}

func (t *Transaction) ToMobileJSON() ([]byte, error) {
	if t.ID == "" {
		return json2.MarshalRemoveFields(t,
			"id",
			"treezorId",
			"treezorWalletDebitId",
			"treezorWalletCreditId",
			"treezorPayinId",
			"treezorPayinMethodId",
			"treezorPayinWebhookTimestamp",
			"treezorPayoutWebhookTimestamp",
			"treezorCardTransactionWebhookTimestamp",
			"createdDate", // mobile should refer to createdAt
			"webhookTimestamp",
		)
	}
	return json2.MarshalRemoveFields(t,
		"treezorId",
		"treezorWalletDebitId",
		"treezorWalletCreditId",
		"treezorPayinId",
		"treezorPayinMethodId",
		"treezorPayinWebhookTimestamp",
		"treezorPayoutWebhookTimestamp",
		"treezorCardTransactionWebhookTimestamp",
		"createdDate", // mobile should refer to createdAt
		"webhookTimestamp",
	)
}

func SortTransactionsByCreatedAt(transactions []*Transaction) []*Transaction {
	// Create a copy of the transactions slice
	sortedTransactions := make([]*Transaction, len(transactions))
	copy(sortedTransactions, transactions)

	// Sort the copied slice
	sort.Slice(sortedTransactions, func(i, j int) bool {
		return sortedTransactions[i].CreatedAt.After(sortedTransactions[j].CreatedAt)
	})

	return sortedTransactions
}

func SkipCardTransactions(transactions []*Transaction) []*Transaction {
	filtered := make([]*Transaction, 0, len(transactions))
	for _, transaction := range transactions {
		if transaction.TransactionType != TransactionTypeCardTranscation {
			filtered = append(filtered, transaction)
		}
	}
	return filtered
}
