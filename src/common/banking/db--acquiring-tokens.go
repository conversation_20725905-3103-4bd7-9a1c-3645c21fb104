package banking

import "yochbee/_base/dbnosql"

type AcquiringTokensCollection struct {
	dbnosql.DataCollection
}

func SetupAcquiringTokenCollection(db dbnosql.Database) (*AcquiringTokensCollection, error) {
	coll := db.DeclareCollection("banking-acquiring-tokens", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &AcquiringToken{}
	})

	if err := coll.SetMultipleIndexesOn("accountId", "treezorUserId", "createdAt"); err != nil {
		return nil, err
	}

	return &AcquiringTokensCollection{coll}, nil
}
