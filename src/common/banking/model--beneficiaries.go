package banking

import (
	"encoding/json"
	"regexp"
	"strings"
	"time"
	json2 "yochbee/_base/json"

	"github.com/go-playground/validator/v10"
)

type Beneficiary struct {
	ID                   string `bson:"_id" json:"id"`
	AccountId            string `bson:"accountId" json:"accountId"`
	TreezorUserId        int64  `bson:"treezorUserId" json:"treezorUserId"`
	TreezorBeneficiaryId int64  `bson:"treezorBeneficiaryId" json:"treezorBeneficiaryId"`

	NickName string `bson:"nickName" json:"nickName"                     mod:"trim"`
	Name     string `bson:"name"     json:"name"     validate:"required" mod:"trim"`
	Address  string `bson:"address"  json:"address"                      mod:"trim"`
	IBAN     string `bson:"iban"     json:"iban"     validate:"required" mod:"trim"`
	BIC      string `bson:"bic"      json:"bic"      validate:"required" mod:"trim"`

	IsActive       bool `bson:"isActive" json:"isActive"`
	IsUsableForSCT bool `bson:"isUsableForSCT" json:"usableForSct"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`
}

type BeneficiaryFormDTO struct {
	NickName string `bson:"nickName"  json:"nickName"  mod:"trim"`
	Name     string `bson:"name"      json:"name"      validate:"required,beneficiaryNameValidator"  mod:"trim"`
	Address  string `bson:"address"   json:"address"   mod:"trim"`
	IBAN     string `bson:"iban"      json:"iban"      validate:"required,min=27,max=34"             mod:"trim"`
	BIC      string `bson:"bic"       json:"bic"       validate:"required"                           mod:"trim"`

	IsActive bool `bson:"isActive" json:"isActive"`
}

var nameRegex = regexp.MustCompile(`^[a-zA-Z0-9/\-?:().'+ ]+$`)

func SetupBeneficiaryRequirements() error {
	// Register a beneficiaryNameValidator
	if err := json2.V.RegisterValidation("beneficiaryNameValidator", func(fl validator.FieldLevel) bool {
		trimmed := strings.TrimSpace(fl.Field().String())
		return nameRegex.MatchString(trimmed)
	}); err != nil {
		return err
	}

	return nil
}

func (b *Beneficiary) ToMobileJSON() ([]byte, error) {
	return json.Marshal(b)
}
