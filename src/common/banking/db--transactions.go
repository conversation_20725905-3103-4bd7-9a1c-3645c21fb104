package banking

import (
	"context"
	"time"
	"yochbee/_base/dbnosql"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type TransactionsCollection struct {
	dbnosql.DataCollection
}

func (c *TransactionsCollection) GetByTreezorPayinId(treezorPayinID interface{}) (*Transaction, error) {
	return dbnosql.GetOneByField[*Transaction](c, "treezorPayinId", treezorPayinID)
}

func (c *TransactionsCollection) GetByTreezorCardTransactionId(treezorCardTransactionID int64) (*Transaction, error) {
	return dbnosql.GetOneByField[*Transaction](c, "treezorCardTransactionId", treezorCardTransactionID)
}

func (c *TransactionsCollection) GetOneByTreezorID(id int64) (*Transaction, error) {
	return dbnosql.GetOneByField[*Transaction](c, "treezorId", id)
}

func (c *TransactionsCollection) Update(transaction *Transaction) error {
	if transaction.ID == "" {
		return dbnosql.ErrMissingID
	}
	return c.UpdateById(transaction.ID, transaction)
}

func (c *TransactionsCollection) GetByTreezorPayoutId(treezorPayoutID interface{}) (*Transaction, error) {
	return dbnosql.GetOneByField[*Transaction](c, "treezorPayoutId", treezorPayoutID)
}

// GetListInOutTransactions returns all transactions for a given account, between start and end dates, ordered by date
// (oldest first), and only those that will change the balance of the account.
func (c *TransactionsCollection) GetListInOutTransactions(treezorWalletId int64, start, end time.Time, page, pageSize int64) ([]*Transaction, error) {
	coll := c.GetRawCollection().(*mongo.Collection)

	// Page setup
	opts := options.Find()
	opts.SetSkip(page * pageSize)
	opts.SetLimit(pageSize)
	opts.SetSort(bson.M{"createdAt": 1})

	q := bson.M{
		"$or": []bson.M{
			{"treezorWalletCreditId": treezorWalletId},
			{"treezorWalletDebitId": treezorWalletId},
		},
		"createdAt": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	result, err := coll.Find(context.Background(), q, opts)
	if err != nil {
		return nil, err
	}

	// Convert to []*Transaction
	var transactions []*Transaction
	for result.Next(context.Background()) {
		var transaction Transaction
		if err := result.Decode(&transaction); err != nil {
			return nil, err
		}
		transactions = append(transactions, &transaction)
	}

	return transactions, nil
}

// -------------------------

func SetupTransactionsCollection(db dbnosql.Database) (*TransactionsCollection, error) {
	coll := db.DeclareCollection("banking-transactions", func(object interface{}) interface{} {
		return object
	}, func() (destination interface{}) {
		return &Transaction{}
	})

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorWalletCreditId", Order: dbnosql.OrderASC},
		},
		Name: "treezorWalletCreditId",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "treezorWalletDebitId", Order: dbnosql.OrderASC},
		},
		Name: "treezorWalletDebitId",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "transactionType", Order: dbnosql.OrderASC},
		},
		Name: "transactionType",
	}); err != nil {
		return nil, err
	}

	if err := coll.SetIndexOn(dbnosql.IndexDefinition{
		Fields: []dbnosql.IndexField{
			{Name: "createdAt", Order: dbnosql.OrderDESC},
		},
		Name: "createdAt",
	}); err != nil {
		return nil, err
	}

	return &TransactionsCollection{coll}, nil
}
