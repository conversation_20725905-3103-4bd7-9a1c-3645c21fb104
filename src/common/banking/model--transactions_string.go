// Code generated by "stringer -type=TransactionType,TransactionStatus -output=model--transactions_string.go"; DO NOT EDIT.

package banking

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[TransactionTypePayin-1]
	_ = x[TransactionTypePayinRefund-2]
	_ = x[TransactionTypePayout-3]
	_ = x[TransactionTypePayoutRefund-4]
	_ = x[TransactionTypeCardDebit-5]
	_ = x[TransactionTypeCardCredit-6]
	_ = x[TransactionTypeCardTranscation-7]
}

const _TransactionType_name = "TransactionTypePayinTransactionTypePayinRefundTransactionTypePayoutTransactionTypePayoutRefundTransactionTypeCardDebitTransactionTypeCardCreditTransactionTypeCardTranscation"

var _TransactionType_index = [...]uint8{0, 20, 46, 67, 94, 118, 143, 173}

func (i TransactionType) String() string {
	i -= 1
	if i < 0 || i >= TransactionType(len(_TransactionType_index)-1) {
		return "TransactionType(" + strconv.FormatInt(int64(i+1), 10) + ")"
	}
	return _TransactionType_name[_TransactionType_index[i]:_TransactionType_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[TransactionStatusPending-1]
	_ = x[TransactionStatusCanceled-2]
	_ = x[TransactionStatusFailed-3]
	_ = x[TransactionStatusSuccess-4]
	_ = x[TransactionStatusRefunded-5]
	_ = x[TransactionStatusAuthorized-6]
	_ = x[TransactionStatusSettled-7]
	_ = x[TransactionStatusDeclined-8]
}

const _TransactionStatus_name = "TransactionStatusPendingTransactionStatusCanceledTransactionStatusFailedTransactionStatusSuccessTransactionStatusRefundedTransactionStatusAuthorizedTransactionStatusSettledTransactionStatusDeclined"

var _TransactionStatus_index = [...]uint8{0, 24, 49, 72, 96, 121, 148, 172, 197}

func (i TransactionStatus) String() string {
	i -= 1
	if i < 0 || i >= TransactionStatus(len(_TransactionStatus_index)-1) {
		return "TransactionStatus(" + strconv.FormatInt(int64(i+1), 10) + ")"
	}
	return _TransactionStatus_name[_TransactionStatus_index[i]:_TransactionStatus_index[i+1]]
}
