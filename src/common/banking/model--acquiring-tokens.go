package banking

import (
	"time"
	json2 "yochbee/_base/json"
)

// AcquiringToken represents a token that corresponds to a Credit/Debit card. This is the result of the HiPay API call
// that is done on the client side. It is the client who sent us this data.
type AcquiringToken struct {
	ID string `bson:"_id" json:"id"`

	AccountId     string `bson:"accountId"      json:"accountId"`
	TreezorUserId int64  `bson:"treezorUserId"  json:"treezorUserId"`

	CardLabel string `bson:"cardLabel" json:"cardLabel"`

	Token                  string `bson:"token"                   json:"token"`
	RequestId              string `bson:"requestId"               json:"requestId"`
	CardId                 string `bson:"cardId"                  json:"cardId"`
	MultiUse               int    `bson:"multiUse"                json:"multiUse"`
	Brand                  string `bson:"brand"                   json:"brand"`
	Pan                    string `bson:"pan"                     json:"pan"`
	CardCategory           string `bson:"cardCategory"            json:"cardCategory"`
	CardHolder             string `bson:"cardHolder"              json:"cardHolder"`
	CardExpiryMonth        string `bson:"cardExpiryMonth"         json:"cardExpiryMonth"`
	CardExpiryYear         string `bson:"cardExpiryYear"          json:"cardExpiryYear"`
	Issuer                 string `bson:"issuer"                  json:"issuer"`
	Country                string `bson:"country"                 json:"country"`
	CardType               string `bson:"cardType"                json:"cardType"`
	ForbiddenIssuerCountry bool   `bson:"forbiddenIssuerCountry"  json:"forbiddenIssuerCountry"`

	CreatedAt time.Time `bson:"createdAt" json:"createdAt"`
}

func (t *AcquiringToken) ToMobileJSON() ([]byte, error) {
	// The token needs to actually be returned to the client, like in the list of saved Card tokens
	return json2.MarshalRemoveFields(t, "accountId", "treezorUserId")
}
