package banking

import (
	"time"
	"yochbee/_base/json"
)

type Payout struct {
	ID              string `bson:"_id"             json:"id"`
	AccountId       string `bson:"accountId"       json:"accountId"`
	TreezorUserId   int64  `bson:"treezorUserId"   json:"treezorUserId"`
	TreezorPayoutId int64  `bson:"treezorPayoutId" json:"treezorPayoutId"`

	PayoutStatus string `bson:"payoutStatus" json:"payoutStatus"`
	CodeStatus   string `bson:"codeStatus"   json:"codeStatus"`
	PayoutTypeId int    `bson:"payoutTypeId" json:"payoutTypeId"`
	PayoutType   string `bson:"payoutType"   json:"payoutType"`
	Label        string `bson:"label"        json:"label"`
	PayoutDate   string `bson:"payoutDate"   json:"payoutDate"`
	Amount       string `bson:"amount"       json:"amount"`
	Currency     string `bson:"currency"     json:"currency"`
	PartnerFee   string `bson:"partnerFee"   json:"partnerFee"`

	// IsRefunded is set as soon as we receive a payout refund event from <PERSON><PERSON> (at PENDING state)
	IsRefunded bool `bson:"isRefunded" json:"isRefunded"`
	// IsRefundCompleted is set as soon as refund event is completed from Treezor (at VALIDATED state)
	IsRefundCompleted bool `bson:"isRefundCompleted" json:"isRefundCompleted"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`
}

func (p *Payout) ToMobileJSON() ([]byte, error) {
	return json.MarshalRemoveFields(p)
}
