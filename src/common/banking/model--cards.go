package banking

import (
	"time"
	"yochbee/_base/json"
	"yochbee/_base/web"

	"github.com/tidwall/sjson"
)

type Card struct {
	ID        string `bson:"_id"        json:"id"`
	AccountId string `bson:"accountId"  json:"accountId"`

	TreezorCardId   int64 `bson:"treezorCardId"    json:"treezorCardId"`
	TreezorUserId   int64 `bson:"treezorUserId"    json:"treezorUserId"`
	TreezorWalletId int64 `bson:"treezorWalletId"  json:"treezorWalletId"`

	IsPhysical         bool `bson:"isPhysical"        json:"isPhysical"`
	Is3DSRegistered    bool `bson:"is3DSRegistered"   json:"is3DSRegistered"`
	IsSCAEnabled       bool `bson:"isSCAEnabled" json:"isSCAEnabled"`
	IsVirtualConverted bool `bson:"virtualConverted"  json:"virtualConverted"`

	WalletCardTransactionID int64   `bson:"walletCardTransactionID"  json:"walletCardTransactionID"`
	PublicToken             string  `bson:"publicToken"              json:"publicToken"`
	StatusCode              string  `bson:"statusCode"               json:"statusCode"`
	IsLive                  int     `bson:"isLive"                   json:"isLive"`
	PinTryExceeds           int     `bson:"pinTryExceeds"            json:"pinTryExceeds"`
	MaskedPan               string  `bson:"maskedPan"                json:"maskedPan"`
	EmbossedName            string  `bson:"embossedName"             json:"embossedName"`
	ExpiryDate              string  `bson:"expiryDate"               json:"expiryDate"`
	CVV                     string  `bson:"cvv"                      json:"cvv"`
	CountryCode             string  `bson:"countryCode"              json:"countryCode"`
	CurrencyCode            string  `bson:"currencyCode"             json:"currencyCode"`
	Lang                    string  `bson:"lang"                     json:"lang"`
	OptionAtm               bool    `bson:"optionAtm"                json:"optionAtm"`
	OptionForeign           bool    `bson:"optionForeign"            json:"optionForeign"`
	OptionOnline            bool    `bson:"optionOnline"             json:"optionOnline"`
	OptionNfc               bool    `bson:"optionNfc"                json:"optionNfc"`
	LimitAtmDay             float64 `bson:"limitAtmDay"              json:"limitAtmDay"`
	LimitAtmWeek            float64 `bson:"limitAtmWeek"             json:"limitAtmWeek"`
	LimitAtmMonth           float64 `bson:"limitAtmMonth"            json:"limitAtmMonth"`
	LimitAtmYear            float64 `bson:"limitAtmYear"             json:"limitAtmYear"`
	LimitAtmAll             float64 `bson:"limitAtmAll"              json:"limitAtmAll"`
	LimitPaymentDay         float64 `bson:"limitPaymentDay"          json:"limitPaymentDay"`
	LimitPaymentWeek        float64 `bson:"limitPaymentWeek"         json:"limitPaymentWeek"`
	LimitPaymentMonth       float64 `bson:"limitPaymentMonth"        json:"limitPaymentMonth"`
	LimitPaymentYear        float64 `bson:"limitPaymentYear"         json:"limitPaymentYear"`
	LimitPaymentAll         float64 `bson:"limitPaymentAll"          json:"limitPaymentAll"`
	PaymentDailyLimit       float64 `bson:"paymentDailyLimit"        json:"paymentDailyLimit"`
	TotalAtmDay             float64 `bson:"totalAtmDay"              json:"totalAtmDay"`
	TotalAtmWeek            float64 `bson:"totalAtmWeek"             json:"totalAtmWeek"`
	TotalAtmMonth           float64 `bson:"totalAtmMonth"            json:"totalAtmMonth"`
	TotalAtmYear            float64 `bson:"totalAtmYear"             json:"totalAtmYear"`
	TotalAtmAll             float64 `bson:"totalAtmAll"              json:"totalAtmAll"`
	TotalPaymentDay         float64 `bson:"totalPaymentDay"          json:"totalPaymentDay"`
	TotalPaymentWeek        float64 `bson:"totalPaymentWeek"         json:"totalPaymentWeek"`
	TotalPaymentMonth       float64 `bson:"totalPaymentMonth"        json:"totalPaymentMonth"`
	TotalPaymentYear        float64 `bson:"totalPaymentYear"         json:"totalPaymentYear"`
	TotalPaymentAll         float64 `bson:"totalPaymentAll"          json:"totalPaymentAll"`

	// Wallet is a special field that is not stored in the database. It may be injected with a *treezor.Wallet object.
	Wallet interface{} `bson:"-" json:"wallet"`

	CreatedAt time.Time  `bson:"createdAt" json:"createdAt"`
	UpdatedAt *time.Time `bson:"updatedAt" json:"updatedAt"`

	WebhookTimestamp int64 `bson:"treezorWebhookTimestamp" json:"treezorWebhookTimestamp"`
}

func (c *Card) ToMobileJSON() ([]byte, error) {
	origMaskedPan := c.MaskedPan
	if c.IsLive == 0 {
		// Change the last 4 digits to ****
		c.MaskedPan = c.MaskedPan[:len(c.MaskedPan)-4] + "****"
	}

	data, err := json.MarshalRemoveFields(c, "treezorWalletId", "treezorWebhookTimestamp", "walletCardTransactionID")
	if err != nil {
		return nil, err
	}
	if c.Wallet != nil {
		data2, err := c.Wallet.(web.MobileJSONer).ToMobileJSON()
		if err != nil {
			return nil, err
		}
		data, err = sjson.SetRawBytes(data, "wallet", data2)
		if err != nil {
			return nil, err
		}
	}

	data, _ = sjson.SetBytes(data, "treezorWalletCardTransactionId", c.WalletCardTransactionID)
	c.MaskedPan = origMaskedPan
	return data, nil
}

func (c *Card) IsActivated() bool {
	// See also: https://docs.treezor.com/guide/cards/introduction.html#key-attributes
	return c.IsLive > 0
}

func (c *Card) IsBlocked() bool {
	switch c.StatusCode {
	case "DESTROYED", "LOST", "STOLEN":
		return true
	default:
		return false
	}
}
