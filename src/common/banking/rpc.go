package banking

import (
	"time"

	bus2 "yochbee/_base/bus"
)

// =============

const Method_GetCardByTreezorID = "banking.GetCardByTreezorID"

type GetCardByTreezorIDRequest struct {
	TreezorCardID int64 `json:"treezorCardId"`
}

type GetCardByTreezorIDResponse struct {
	Card *Card `json:"card"`
}

func GetCardByTreezorID(rpc bus2.RpcClient, treezorCardID int64) (*Card, error) {
	request := GetCardByTreezorIDRequest{TreezorCardID: treezorCardID}
	response, err := bus2.CallMethodMU[GetCardByTreezorIDResponse](rpc, Method_GetCardByTreezorID, request, 10*time.Second)
	if err != nil {
		return nil, err
	}
	return response.Card, nil
}

// ============
