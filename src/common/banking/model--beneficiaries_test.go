package banking

import (
	"testing"
	json2 "yochbee/_base/json"
)

func TestNameValidation(t *testing.T) {
	// Important call to set up the validator
	if err := SetupBeneficiaryRequirements(); err != nil {
		t.<PERSON>rrorf("SetupBeneficiaryRequirements() error = %v", err)
		return
	}

	testCases := []struct {
		subject BeneficiaryFormDTO
		valid   bool
	}{
		{
			subject: BeneficiaryFormDTO{
				Name: "<PERSON>",
				IBAN: "***************************",
				BIC:  "BNPAFRPPXXX",
			},
			valid: true,
		},
		{
			subject: BeneficiaryFormDTO{
				Name: "John Doe 123",
				IBAN: "***************************",
				BIC:  "BNPAFRPPXXX",
			},
			valid: true,
		},
		{
			subject: BeneficiaryFormDTO{
				Name: "John Doe 😁",
				IBAN: "***************************",
				BIC:  "BNPAFRPPXXX",
			},
			valid: false,
		},
		{
			subject: BeneficiaryFormDTO{
				Name: "<PERSON> Doe 123, <PERSON> Mama",
				IBAN: "***************************",
				BIC:  "BNPAFRPPXXX",
			},
			valid: false,
		},
		{
			subject: BeneficiaryFormDTO{
				Name: "               ",
				IBAN: "FR763000600",
				BIC:  "BNPAFRPPXXX",
			},
			valid: false,
		},
		{
			subject: BeneficiaryFormDTO{
				Name: "John Doe",
				IBAN: "FR763", // too short
				BIC:  "BNPAFRPPXXX",
			},
			valid: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.subject.Name, func(t *testing.T) {
			if err := json2.V.Struct(tc.subject); (err == nil) != tc.valid {
				t.Errorf("V.Struct() error = %v, wantErr %v, name %v", err, tc.valid, tc.subject.Name)
			}
		})
	}
}
