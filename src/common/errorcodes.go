package common

import (
	"net/http"

	"github.com/eliezedeck/gobase/logging"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
)

const (
	ErrorCodeBadRequest           = 400
	ErrorCodeNotFound             = 404
	ErrorCodeServiceFailure       = 500
	ErrorCodeTemporaryServerError = 503

	ErrorCodeBadAuth                       = 1000
	ErrorCodeRequirePhoneVerification      = 1001
	ErrorCodeAccountNotFound               = 1002
	ErrorCodeAssociatedAccountDataNotFound = 1003
	ErrorCode2FARequired                   = 1004
	ErrorCode2FAIncorrect                  = 1005
	ErrorCode2FATemporaryBlocked           = 1006
	ErrorCode2FATokenRequired              = 1007
	ErrorCodeAccountBlocked                = 1010

	ErrorCodeUnmatchedPasswords             = 1100
	ErrorCodeUsernameAlreadyExists          = 1101
	ErrorCodeUnacceptablePassword           = 1102
	ErrorCodeWeakPassword                   = 1103
	ErrorCodePasswordResetEmailRecentlySent = 1104
	ErrorCodeEmailAlreadyTaken              = 1105
	ErrorCodeReferralCodeAlreadyTaken       = 1106
	ErrorCodeTreezorUserNotFound            = 1110
	ErrorCodeAccountNotLinkedWithTreezor    = 1111
	ErrorCodeWalletNotFound                 = 1112
	ErrorCodeAccountNotConnectedToTreezor   = 1113

	ErrorCodeSMSRecentlySent              = 1200
	ErrorCodeSMSCodeIncorrect             = 1201
	ErrorCodePhoneNumberNeedsVerification = 1203
	ErrorCodePhoneNumberInvalid           = 1204

	ErrorCodeUploadFileSizeUnacceptable = 1300
	ErrorCodeUploadFileInvalidType      = 1301

	ErrorCodeKYCCannotStartReview      = 1400
	ErrorCodeLivenessMethodOnly        = 1402
	ErrorCodeKYCCannotStartPermanently = 1403
	ErrorCodeKYCCompletionRequired     = 1404

	ErrorCodeBeneficiaryNotFound     = 1500
	ErrorCodeBeneficiaryInvalidIBAN  = 1501
	ErrorCodeBeneficiaryInvalidBIC   = 1502
	ErrorCodeBeneficiaryCannotDelete = 1503

	ErrorCodeInvalidWalletState    = 1600
	ErrorCodeNoEnoughWalletBalance = 1601
	ErrorCodePayoutAmountTooHigh   = 1602

	ErrorCodeCardAlreadyOrdered          = 2000
	ErrorCodeCardNotFound                = 2001
	ErrorCodeCardAlreadyActivated        = 2002
	ErrorCodeCardBadStatus               = 2003
	ErrorCodeCardRequiresMoreProfileInfo = 2004
	ErrorCodeCardIncorrectPIN            = 2005
	ErrorCodeCardLimitsRequired          = 2006

	ErrorCodeSCAProofRequired  = 3002
	ErrorCodeSCASessionExpired = 3003

	ErrorCodeInvalidTime = 98000

	ErrorInvalidAPICall = 99998
	ErrorInternalError  = 99999
)

var (
	invalidApiCallResponse = map[string]interface{}{
		"error": "Invalid API call",
		"code":  ErrorInvalidAPICall,
	}
	invalidJWTTokenResponse = map[string]interface{}{
		"error": "Invalid JWT token",
		"code":  ErrorCodeBadAuth,
	}
	invalidPhoneNumberResponse = map[string]interface{}{
		"error": "Phone number may be invalid",
		"code":  ErrorCodePhoneNumberInvalid,
	}
	livenessMethodOnly = map[string]interface{}{
		"error": "Only Liveness KYC method is allowed",
		"code":  ErrorCodeLivenessMethodOnly,
	}
)

func ReturnInvalidAPICallResponse(c echo.Context) error {
	logging.L.Warn("Invalid API call", zap.String("path", c.Path()), zap.String("method", c.Request().Method))
	return c.JSON(http.StatusBadRequest, invalidApiCallResponse)
}

func ReturnInvalidJWTTokenResponse(c echo.Context) error {
	logging.L.Warn("Invalid JWT token", zap.String("path", c.Path()), zap.String("method", c.Request().Method))
	return c.JSON(http.StatusBadRequest, invalidJWTTokenResponse)
}

func ReturnInvalidPhoneNumberResponse(c echo.Context) error {
	logging.L.Warn("Invalid phone number", zap.String("path", c.Path()), zap.String("method", c.Request().Method))
	return c.JSON(http.StatusBadRequest, invalidPhoneNumberResponse)
}

func ReturnLivenessMethodOnly(c echo.Context) error {
	logging.L.Warn("Only Liveness KYC method is allowed", zap.String("path", c.Path()), zap.String("method", c.Request().Method))
	return c.JSON(http.StatusBadRequest, livenessMethodOnly)
}

func ReturnResourceNotFoundWithMessageResponse(c echo.Context, message string) error {
	return c.JSON(http.StatusNotFound, map[string]interface{}{
		"error": message,
		"code":  ErrorCodeNotFound,
	})
}
