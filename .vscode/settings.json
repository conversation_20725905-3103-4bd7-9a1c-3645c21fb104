{"remote.SSH.defaultForwardedPorts": [{"name": "ETCD", "localPort": 2379, "remotePort": 2379}, {"name": "MongoDB", "localPort": 27017, "remotePort": 27017}, {"name": "RabbitMQ UI", "localPort": 15672, "remotePort": 15672}, {"name": "pixel microservice", "localPort": 8000, "remotePort": 8000}], "git.postCommitCommand": "none", "git.followTagsWhenSync": true, "gopls": {"ui.diagnostic.analyses": {"composites": false}}}