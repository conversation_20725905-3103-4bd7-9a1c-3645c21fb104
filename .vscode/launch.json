{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "accounts",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/cmd/microservices/accounts",
      "env": {
        "ETCD_ENDPOINTS": "yochbee-backend:2379",
        "SEQ_URL": "http://yochbee-backend:5341",
        "ALLOW_ACCOUNTS_DELETION": "true",
        "DEBUG": "true"
      }
    },
    {
      "name": "notifications",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/cmd/microservices/notifications",
      "env": {
        "ETCD_ENDPOINTS": "yochbee-backend:2379",
        "SEQ_URL": "http://yochbee-backend:5341",
        "FAKE_SEND_EMAIL": "true",
        "DEBUG": "true",
        "FAKE_SEND_SMS": "true",
      }
    },
    {
      "name": "treezor",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/cmd/microservices/treezor",
      "env": {
        "ETCD_ENDPOINTS": "yochbee-backend:2379",
        "SEQ_URL": "http://yochbee-backend:5341",
        "DEBUG": "true",
        "ALLOW_TOKENS_LOGGING": "true",
        "IS_SCA_ENABLED": "true",
      }
    },
    {
      "name": "kyc",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/cmd/microservices/kyc",
      "env": {
        "ETCD_ENDPOINTS": "yochbee-backend:2379",
        "SEQ_URL": "http://yochbee-backend:5341",
        "DEBUG": "true"
      }
    },
    {
      "name": "banking",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/cmd/microservices/banking",
      "env": {
        "ETCD_ENDPOINTS": "yochbee-backend:2379",
        "SEQ_URL": "http://yochbee-backend:5341",
        "DEBUG": "true",
        "IS_SCA_ENABLED": "true"
      }
    },
    {
      "name": "webhookhandler",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/src/cmd/microservices/treezorwebhookhandler",
      "env": {
        "ETCD_ENDPOINTS": "yochbee-backend:2379",
        "SEQ_URL": "http://yochbee-backend:5341",
        "DEBUG": "true"
      }
    },
  ],
  "compounds": [
    {
      "name": "Compound",
      "configurations": [
        "accounts",
        "notifications",
        "treezor",
        "kyc",
        "banking",
        "webhookhandler",
      ]
    }
  ]
}