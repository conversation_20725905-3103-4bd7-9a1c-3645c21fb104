{
	"Template for Service Bus file": {
		"prefix": "templateServiceBus",
		"body": [
			"package main",
			"",
			"import (",
			"\t\"encoding/json\"",
			"\tbus2 \"yochbee/_base/bus\"",
			"\t\"yochbee/_base/dbnosql\"",
			")",
			"",
			"func registerBusMethods(rpc bus2.RpcClient, coll dbnosql.DataCollection) error {",
			"\t$0",
			"",
			"\treturn nil",
			"}"
		],
		"description": "Long Description"
	},
	"Register a BUS method": {
		"prefix": "registerMethod",
		"body": [
			"if err := rpc.Register(${1:module}.Method_${2:MethodName}, func(payload []byte) ([]byte, error) {",
			"\trequest := $1.${2}Request{}",
			"\tif err := json.Unmarshal(payload, &request); err != nil {",
			"\t\treturn nil, err",
			"\t}",
			"",
			"\tresponse :=  $1.${2}Response{}",
			"",
			"\treturn json.Marshal(response)",
			"}, true); err != nil {",
			"\treturn err",
			"}",
			""
		],
	},
	"RPC method definition": {
		"prefix": "rpcMethod",
		"body": [
			"// =============",
			"",
			"const Method_${1:MethodName} = \"${2:pkg}.$1\"",
			"",
			"type ${1}Request struct {",
			"\t$0",
			"}",
			"",
			"type ${1}Response struct {",
			"}",
			""
		],
		"description": "Long Description"
	}
}